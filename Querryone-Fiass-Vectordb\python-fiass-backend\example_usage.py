#!/usr/bin/env python3
"""
Example Usage of Language-Aware Query Processing System

This script demonstrates how to use the language-aware query processing system
with practical examples and different scenarios.
"""

import sys
import os
import pandas as pd
import requests
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def example_api_usage():
    """Demonstrate API usage of the language-aware processing system."""
    print("=" * 60)
    print("API USAGE EXAMPLES")
    print("=" * 60)
    
    # Base URL for the API (adjust as needed)
    base_url = "http://localhost:5010"
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "English Query with English CSV",
            "query": "What is the stock market performance?",
            "csv_language": "English",
            "expected_strategy": "direct"
        },
        {
            "name": "Tamil Query with Tamil CSV", 
            "query": "பங்குச் சந்தையின் செயல்திறன் என்ன?",
            "csv_language": "Tamil",
            "expected_strategy": "direct"
        },
        {
            "name": "English Query with Tamil CSV",
            "query": "What is the market performance?",
            "csv_language": "Tamil", 
            "expected_strategy": "translation_based"
        },
        {
            "name": "Tamil Query with English CSV",
            "query": "பங்குச் சந்தை செய்தி என்ன?",
            "csv_language": "English",
            "expected_strategy": "translation_based"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n--- {scenario['name']} ---")
        print(f"Query: {scenario['query']}")
        print(f"CSV Language: {scenario['csv_language']}")
        print(f"Expected Strategy: {scenario['expected_strategy']}")
        
        try:
            # Test the language-aware processing endpoint
            response = requests.post(
                f"{base_url}/api/test-language-aware",
                json={
                    "query": scenario["query"],
                    "csv_language": scenario["csv_language"]
                },
                timeout=30
            )
            
            if response.ok:
                data = response.json()
                if data.get("success"):
                    result = data.get("processing_result", {})
                    print(f"✅ Success!")
                    print(f"   Strategy Used: {result.get('strategy', 'unknown')}")
                    print(f"   Query Translated: {result.get('query_translated', False)}")
                    print(f"   Results Translated: {result.get('results_translated', False)}")
                    print(f"   Processing Time: {result.get('processing_duration_ms', 0)}ms")
                    print(f"   Results Count: {result.get('results_count', 0)}")
                    
                    # Check if strategy matches expectation
                    actual_strategy = result.get('strategy', 'unknown')
                    if actual_strategy == scenario['expected_strategy']:
                        print(f"   Strategy Match: ✅")
                    else:
                        print(f"   Strategy Match: ❌ (expected {scenario['expected_strategy']}, got {actual_strategy})")
                else:
                    print(f"❌ API Error: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP Error: {response.status_code} - {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection Error: Make sure the server is running on http://localhost:5010")
        except requests.exceptions.Timeout:
            print("❌ Timeout Error: Request took too long")
        except Exception as e:
            print(f"❌ Error: {str(e)}")

def example_direct_usage():
    """Demonstrate direct usage of the language-aware processor."""
    print("\n" + "=" * 60)
    print("DIRECT USAGE EXAMPLES")
    print("=" * 60)
    
    try:
        from services.language_aware_processor import language_aware_processor
        
        # Example 1: Direct processing with matching languages
        print("\n--- Example 1: Direct Processing (Tamil-Tamil) ---")
        
        # Create Tamil CSV data
        tamil_df = pd.DataFrame({
            'தலைப்பு': ['பங்குச் சந்தை செய்தி', 'பொருளாதார அறிக்கை', 'நிதி தகவல்'],
            'உள்ளடக்கம்': [
                'பங்குச் சந்தை இன்று நன்றாக செயல்பட்டது',
                'பொருளாதார குறிகாட்டிகள் வளர்ச்சியைக் காட்டுகின்றன',
                'காலாண்டு நிதி முடிவுகள் வெளியிடப்பட்டன'
            ],
            'வகை': ['நிதி', 'பொருளாதாரம்', 'வணிகம்']
        })
        
        # Tamil query
        tamil_query = "பங்குச் சந்தையின் செயல்திறன் என்ன?"
        
        # Mock search function
        def mock_search(query, **kwargs):
            return [
                {
                    'text': f'Search result for: {query}',
                    'score': 0.95,
                    'metadata': {'source': 'tamil_data'}
                }
            ]
        
        # Process with language awareness
        result = language_aware_processor.process_query_with_language_awareness(
            query=tamil_query,
            dataframe=tamil_df,
            search_function=mock_search
        )
        
        print(f"Query: {tamil_query}")
        print(f"Strategy: {result.get('processing_strategy', 'unknown')}")
        print(f"Success: {result.get('success', False)}")
        print(f"Direct Processing: {result.get('use_direct_processing', False)}")
        print(f"Query Translated: {result.get('query_translated', False)}")
        
        # Example 2: Translation-based processing
        print("\n--- Example 2: Translation-Based Processing (English-Tamil) ---")
        
        english_query = "What is the stock market performance?"
        
        result2 = language_aware_processor.process_query_with_language_awareness(
            query=english_query,
            dataframe=tamil_df,
            search_function=mock_search
        )
        
        print(f"Query: {english_query}")
        print(f"Strategy: {result2.get('processing_strategy', 'unknown')}")
        print(f"Success: {result2.get('success', False)}")
        print(f"Direct Processing: {result2.get('use_direct_processing', False)}")
        print(f"Query Translated: {result2.get('query_translated', False)}")
        print(f"Translations Performed: {len(result2.get('translations_performed', []))}")
        
        # Example 3: Language detection only
        print("\n--- Example 3: Language Detection ---")
        
        test_queries = [
            "What is the market trend?",
            "பங்குச் சந்தை எப்படி இருக்கிறது?",
            "మార్కెట్ ట్రెండ్ ఎలా ఉంది?",
            "ಮಾರುಕಟ್ಟೆ ಪ್ರವೃತ್ತಿ ಹೇಗಿದೆ?"
        ]
        
        for query in test_queries:
            lang_info = language_aware_processor.detect_query_language(query)
            print(f"Query: {query}")
            print(f"  Language: {lang_info.get('language', 'Unknown')}")
            print(f"  Confidence: {lang_info.get('confidence', 0):.3f}")
            print()
        
    except ImportError as e:
        print(f"❌ Language-aware processor not available: {e}")
        print("Make sure the services are properly installed and configured.")
    except Exception as e:
        print(f"❌ Error in direct usage example: {e}")

def example_csv_language_detection():
    """Demonstrate CSV language detection."""
    print("\n" + "=" * 60)
    print("CSV LANGUAGE DETECTION EXAMPLES")
    print("=" * 60)
    
    try:
        from services.language_aware_processor import language_aware_processor
        
        # Create test DataFrames in different languages
        datasets = [
            {
                'name': 'English Financial Data',
                'df': pd.DataFrame({
                    'Title': ['Stock Market Update', 'Economic News', 'Financial Report'],
                    'Content': [
                        'The stock market performed well today with significant gains',
                        'Economic indicators show positive growth trends',
                        'Quarterly financial results exceeded expectations'
                    ],
                    'Category': ['Finance', 'Economics', 'Business']
                }),
                'expected': 'English'
            },
            {
                'name': 'Tamil Financial Data',
                'df': pd.DataFrame({
                    'தலைப்பு': ['பங்குச் சந்தை செய்தி', 'பொருளாதார அறிக்கை', 'நிதி தகவல்'],
                    'உள்ளடக்கம்': [
                        'பங்குச் சந்தை இன்று நன்றாக செயல்பட்டது',
                        'பொருளாதார குறிகாட்டிகள் வளர்ச்சியைக் காட்டுகின்றன',
                        'காலாண்டு நிதி முடிவுகள் எதிர்பார்ப்பை விட அதிகம்'
                    ],
                    'வகை': ['நிதி', 'பொருளாதாரம்', 'வணிகம்']
                }),
                'expected': 'Tamil'
            }
        ]
        
        for dataset in datasets:
            print(f"\n--- {dataset['name']} ---")
            print(f"Expected Language: {dataset['expected']}")
            
            # Detect language
            lang_info = language_aware_processor.detect_csv_language(dataframe=dataset['df'])
            
            print(f"Detected Language: {lang_info.get('language', 'Unknown')}")
            print(f"Confidence: {lang_info.get('confidence', 0):.3f}")
            print(f"Sample Size: {lang_info.get('sample_size', 0)}")
            print(f"Method: {lang_info.get('method', 'unknown')}")
            
            # Check accuracy
            detected = lang_info.get('language', 'Unknown')
            if detected == dataset['expected']:
                print("Accuracy: ✅ Correct")
            else:
                print(f"Accuracy: ❌ Incorrect (expected {dataset['expected']})")
        
    except ImportError as e:
        print(f"❌ Language-aware processor not available: {e}")
    except Exception as e:
        print(f"❌ Error in CSV language detection example: {e}")

def main():
    """Run all examples."""
    print("🚀 LANGUAGE-AWARE QUERY PROCESSING SYSTEM")
    print("Example Usage and Demonstrations")
    print("=" * 60)
    
    # Run examples
    example_api_usage()
    example_direct_usage()
    example_csv_language_detection()
    
    print("\n" + "=" * 60)
    print("EXAMPLES COMPLETED")
    print("=" * 60)
    print("\nFor more information, see:")
    print("- LANGUAGE_AWARE_PROCESSING.md for detailed documentation")
    print("- test_language_aware_processing.py for comprehensive tests")
    print("- services/language_aware_processor.py for implementation details")

if __name__ == "__main__":
    main()
