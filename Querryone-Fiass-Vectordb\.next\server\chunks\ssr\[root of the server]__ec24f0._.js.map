{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/adminsidebar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { PiDatabase, PiPlus, PiList, PiGear } from 'react-icons/pi';\r\n\r\ninterface AdminSidebarProps {\r\n  currentView?: string;\r\n  onViewChange?: (view: string) => void;\r\n  isOpen?: boolean;\r\n  onToggle?: () => void;\r\n}\r\n\r\nconst AdminSidebar: React.FC<AdminSidebarProps> = ({\r\n  currentView = 'create',\r\n  onViewChange,\r\n  isOpen = true,\r\n  onToggle\r\n}) => {\r\n  const router = useRouter();\r\n\r\n  const handleNavigation = (view: string) => {\r\n    if (view === 'show') {\r\n      // Navigate to the show-index route\r\n      router.push('/show-index');\r\n    } else if (view === 'create') {\r\n      // Navigate to the file-upload-standalone route\r\n      router.push('/file-upload-standalone');\r\n    } else if (onViewChange) {\r\n      onViewChange(view);\r\n    }\r\n  };\r\n\r\n  const menuItems = [\r\n    {\r\n      id: 'create',\r\n      label: 'Create Index',\r\n      icon: PiPlus,\r\n      description: 'Upload CSV or Excel files to create new Fiass indexes'\r\n    },\r\n    {\r\n      id: 'show',\r\n      label: 'Show Index',\r\n      icon: PiDatabase,\r\n      description: 'View and manage existing PINE collection entries'\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className={`admin-sidebar w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 fixed left-0 top-0 p-4 h-full z-40 transition-transform duration-300 ${\r\n      isOpen ? 'translate-x-0' : '-translate-x-full'\r\n    } lg:translate-x-0`}>\r\n      {/* Header */}\r\n      <div className=\"sidebar-header mb-8\">\r\n        <div className=\"flex items-center gap-2 mb-2\">\r\n          <PiGear className=\"text-primaryColor text-xl\" />\r\n          <h2 className=\"text-xl font-semibold dark:text-white\">\r\n            Admin Panel\r\n          </h2>\r\n        </div>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n          Manage your FiassDB and data\r\n        </p>\r\n      </div>\r\n\r\n      {/* Navigation Menu */}\r\n      <nav className=\"space-y-2\">\r\n        {menuItems.map((item) => {\r\n          const Icon = item.icon;\r\n          const isActive = currentView === item.id;\r\n\r\n          return (\r\n            <button\r\n              key={item.id}\r\n              onClick={() => handleNavigation(item.id)}\r\n              className={`block w-full text-left px-4 py-3 rounded-md transition-colors group ${\r\n                isActive\r\n                  ? 'bg-primaryColor text-white shadow-sm'\r\n                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'\r\n              }`}\r\n            >\r\n              <div className=\"flex items-center gap-3\">\r\n                <Icon\r\n                  className={`text-lg ${\r\n                    isActive ? 'text-white' : 'text-primaryColor'\r\n                  }`}\r\n                />\r\n                <div>\r\n                  <div className=\"font-medium\">{item.label}</div>\r\n                  <div className={`text-xs mt-1 ${\r\n                    isActive\r\n                      ? 'text-white/80'\r\n                      : 'text-gray-500 dark:text-gray-400'\r\n                  }`}>\r\n                    {item.description}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </button>\r\n          );\r\n        })}\r\n      </nav>\r\n\r\n      {/* Footer */}\r\n      {/* <div className=\"absolute bottom-4 left-4 right-4\">\r\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3\">\r\n          <div className=\"flex items-center gap-2 mb-2\">\r\n            <PiList className=\"text-primaryColor\" />\r\n            <span className=\"text-sm font-medium dark:text-white\">Quick Info</span>\r\n          </div>\r\n          <p className=\"text-xs text-gray-600 dark:text-gray-400\">\r\n            Use this panel to manage your Pinecone vector databases and PINE collection data.\r\n          </p>\r\n        </div>\r\n      </div> */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminSidebar;"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,eAA4C,CAAC,EACjD,cAAc,QAAQ,EACtB,YAAY,EACZ,SAAS,IAAI,EACb,QAAQ,EACT;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS,QAAQ;YACnB,mCAAmC;YACnC,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,SAAS,UAAU;YAC5B,+CAA+C;YAC/C,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,cAAc;YACvB,aAAa;QACf;IACF;IAEA,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,8IAAA,CAAA,SAAM;YACZ,aAAa;QACf;QACA;YACE,IAAI;YAC<PERSON>,OAAO;YACP,MAAM,8IAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,kKAAkK,EACjL,SAAS,kBAAkB,oBAC5B,iBAAiB,CAAC;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;;;;;;;kCAIxD,8OAAC;wBAAE,WAAU;kCAA2C;;;;;;;;;;;;0BAM1D,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC;oBACd,MAAM,OAAO,KAAK,IAAI;oBACtB,MAAM,WAAW,gBAAgB,KAAK,EAAE;oBAExC,qBACE,8OAAC;wBAEC,SAAS,IAAM,iBAAiB,KAAK,EAAE;wBACvC,WAAW,CAAC,oEAAoE,EAC9E,WACI,yCACA,6EACJ;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAW,CAAC,QAAQ,EAClB,WAAW,eAAe,qBAC1B;;;;;;8CAEJ,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAe,KAAK,KAAK;;;;;;sDACxC,8OAAC;4CAAI,WAAW,CAAC,aAAa,EAC5B,WACI,kBACA,oCACJ;sDACC,KAAK,WAAW;;;;;;;;;;;;;;;;;;uBArBlB,KAAK,EAAE;;;;;gBA2BlB;;;;;;;;;;;;AAiBR;uCAEe"}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/services/api.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Create an axios instance with default config\r\n// Configure base URL based on environment\r\nconst baseURL = process.env.NODE_ENV === 'development'\r\n  ? 'http://localhost:5010'\r\n  : 'http://localhost:5010';\r\n\r\nconst api = axios.create({\r\n  baseURL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// API functions\r\nexport const fetchNews = async (category: string = 'all') => {\r\n  try {\r\n    const response = await api.get('/api/news', {\r\n      params: { category }\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching news:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// FAISS-specific API functions\r\nexport const listEmbeddingModels = async () => {\r\n  try {\r\n    const response = await api.get('/api/list-embedding-models');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching embedding models:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const listFaissCategories = async () => {\r\n  try {\r\n    const response = await api.post('/api/list-categories');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching FAISS categories:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const checkFaissIndex = async (indexName: string, embedModel?: string) => {\r\n  try {\r\n    const response = await api.post('/api/check-index', {\r\n      index_name: indexName,\r\n      embed_model: embedModel\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error checking FAISS index:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get current user's email from session storage\r\n */\r\nconst getCurrentUserEmail = (): string | null => {\r\n  try {\r\n    if (typeof window === 'undefined') return null;\r\n\r\n    // Try multiple sources for user email\r\n    const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');\r\n    if (directEmail) return directEmail;\r\n\r\n    // Try from user session data\r\n    const userSession = sessionStorage.getItem('resultUser');\r\n    if (userSession) {\r\n      const userData = JSON.parse(userSession);\r\n      return userData.email || userData.username || null;\r\n    }\r\n\r\n    return null;\r\n  } catch (error) {\r\n    console.error('Error getting current user email:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\nexport const queryFaissIndex = async (query: string, indexName: string, k: number = 5, userEmail?: string) => {\r\n  try {\r\n    // Get user email if not provided\r\n    const emailToUse = userEmail || getCurrentUserEmail();\r\n\r\n    const requestBody: any = {\r\n      query,\r\n      index_name: indexName,\r\n      k\r\n    };\r\n\r\n    // Add user email for access validation if available\r\n    if (emailToUse) {\r\n      requestBody.user_email = emailToUse;\r\n    }\r\n\r\n    const response = await api.post('/api/query-faiss', requestBody);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error querying FAISS index:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const deleteFaissIndex = async (indexName: string) => {\r\n  try {\r\n    const response = await api.post('/api/delete-faiss-index', {\r\n      index_name: indexName\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error deleting FAISS index:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Data Management API functions\r\nexport const getIndexData = async (indexName: string, limit: number = 1000, offset: number = 0) => {\r\n  try {\r\n    const response = await api.post('/api/get-index-data', {\r\n      index_name: indexName,\r\n      limit,\r\n      offset\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching index data:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const deleteIndexRows = async (indexName: string, rowIds: string[]) => {\r\n  try {\r\n    const response = await api.delete('/api/delete-index-rows', {\r\n      data: {\r\n        index_name: indexName,\r\n        row_ids: rowIds\r\n      }\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error deleting index rows:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport default api;\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAEA,+CAA+C;AAC/C,0CAA0C;AAC1C,MAAM,UAAU,uCACZ;AAGJ,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB;IACA,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,YAAY,OAAO,WAAmB,KAAK;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,aAAa;YAC1C,QAAQ;gBAAE;YAAS;QACrB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;QAChC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,kBAAkB,OAAO,WAAmB;IACvD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;YAClD,YAAY;YACZ,aAAa;QACf;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEA;;CAEC,GACD,MAAM,sBAAsB;IAC1B,IAAI;QACF,wCAAmC,OAAO;;QAE1C,sCAAsC;QACtC,MAAM;QAGN,6BAA6B;QAC7B,MAAM;IAOR,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAEO,MAAM,kBAAkB,OAAO,OAAe,WAAmB,IAAY,CAAC,EAAE;IACrF,IAAI;QACF,iCAAiC;QACjC,MAAM,aAAa,aAAa;QAEhC,MAAM,cAAmB;YACvB;YACA,YAAY;YACZ;QACF;QAEA,oDAAoD;QACpD,IAAI,YAAY;YACd,YAAY,UAAU,GAAG;QAC3B;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;YACzD,YAAY;QACd;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,MAAM,eAAe,OAAO,WAAmB,QAAgB,IAAI,EAAE,SAAiB,CAAC;IAC5F,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,uBAAuB;YACrD,YAAY;YACZ;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAEO,MAAM,kBAAkB,OAAO,WAAmB;IACvD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,0BAA0B;YAC1D,MAAM;gBACJ,YAAY;gBACZ,SAAS;YACX;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;uCAEe"}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/services/fileUploadService.ts"], "sourcesContent": ["/**\r\n * Format file size to human-readable format\r\n * @param {number} bytes - File size in bytes\r\n * @returns {string} - Formatted file size (e.g., \"2.5 MB\")\r\n */\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Upload a CSV file to FAISS via the backend\r\n * @param {File} file - The CSV file to upload\r\n * @param {string} clientEmail - Client email identifier\r\n * @param {string} indexName - Name for the FAISS index\r\n * @param {string} updateMode - Update mode ('update' or 'new') (optional)\r\n * @param {AbortSignal} signal - AbortSignal for cancelling the upload (optional)\r\n * @param {Function} onProgress - Callback function for upload progress (optional)\r\n * @param {string} embedModel - Name of the embedding model to use (optional)\r\n * @returns {Promise} - Promise that resolves with the server response including upload_id\r\n */\r\nexport const uploadCSVToFaiss = async (\r\n  file: File,\r\n  clientEmail: string,\r\n  indexName: string,\r\n  updateMode?: string | null,\r\n  signal?: AbortSignal,\r\n  onProgress?: (progress: number) => void,\r\n  embedModel?: string\r\n): Promise<any> => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // Validate file type\r\n      if (file.type !== 'text/csv') {\r\n        reject(new Error('Only CSV files are supported for FAISS upload'));\r\n        return;\r\n      }\r\n\r\n      // Create a new FormData instance\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n\r\n      // Add client information to form data if provided\r\n      if (clientEmail) {\r\n        formData.append('client', clientEmail);\r\n      }\r\n\r\n      // Add index name to form data\r\n      if (indexName) {\r\n        formData.append('index_name', indexName);\r\n      }\r\n\r\n      // Add index name to form data\r\n      if (indexName) {\r\n        formData.append('index_name', indexName);\r\n      }\r\n\r\n      // Add update mode to form data if provided\r\n      if (updateMode) {\r\n        formData.append('update_mode', updateMode);\r\n      }\r\n\r\n      // Add embedding model to form data if provided\r\n      if (embedModel) {\r\n        formData.append('embed_model', embedModel);\r\n      }\r\n\r\n      // Create a new XMLHttpRequest and connect abort signal\r\n      const xhr = new XMLHttpRequest();\r\n\r\n      // Handle abort signal for client-side cancellation\r\n      if (signal) {\r\n        signal.onabort = () => {\r\n          xhr.abort();\r\n          // Instead of rejecting with an error, resolve with a cancellation object\r\n          // This prevents the error from appearing in the console\r\n          resolve({\r\n            success: false,\r\n            cancelled: true,\r\n            message: 'Upload cancelled by user'\r\n          });\r\n        };\r\n      }\r\n\r\n      // Configure the request to our backend endpoint\r\n      xhr.open('POST', 'http://localhost:5010/api/upload-csv', true);\r\n      // Add authentication header only (no Content-Type for FormData)\r\n      xhr.setRequestHeader('xxxid', 'FAISS');\r\n\r\n      // Track upload progress if callback provided\r\n      if (onProgress) {\r\n        xhr.upload.onprogress = (event) => {\r\n          if (event.lengthComputable) {\r\n            const progress = Math.round((event.loaded / event.total) * 100);\r\n            onProgress(progress);\r\n          }\r\n        };\r\n      }\r\n\r\n      // Handle response\r\n      xhr.onload = () => {\r\n        if (xhr.status >= 200 && xhr.status < 300) {\r\n          try {\r\n            const response = JSON.parse(xhr.responseText);\r\n            resolve(response);\r\n          } catch (error) {\r\n            resolve({\r\n              success: true,\r\n              message: 'CSV file uploaded successfully to FAISS',\r\n              indexName: indexName // Use the user-provided index name\r\n            });\r\n          }\r\n        } else {\r\n          reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));\r\n        }\r\n      };\r\n\r\n      // Handle network errors\r\n      xhr.onerror = () => {\r\n        reject(new Error('Network error occurred while uploading CSV file'));\r\n      };\r\n\r\n      // Send the request\r\n      xhr.send(formData);\r\n    } catch (error) {\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n// Compatibility alias for existing code\r\nexport const uploadCSVToPinecone = uploadCSVToFaiss;\r\n\r\n/**\r\n * Upload a single file to the server\r\n * @param {File} file - The file to upload\r\n * @param {Function} onProgress - Callback function for upload progress\r\n * @returns {Promise} - Promise that resolves with the server response\r\n */\r\nexport const uploadFile = async (\r\n  file: File,\r\n  onProgress?: (progress: number) => void\r\n): Promise<any> => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // Create a new FormData instance\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n\r\n      // Create a new XMLHttpRequest\r\n      const xhr = new XMLHttpRequest();\r\n\r\n      // Configure the request\r\n      xhr.open('POST', 'http://localhost:5010/api/upload', true);\r\n\r\n      // Track upload progress\r\n      xhr.upload.onprogress = (event) => {\r\n        if (event.lengthComputable && onProgress) {\r\n          const progress = Math.round((event.loaded / event.total) * 100);\r\n          onProgress(progress);\r\n        }\r\n      };\r\n\r\n      // Handle response\r\n      xhr.onload = () => {\r\n        if (xhr.status >= 200 && xhr.status < 300) {\r\n          try {\r\n            const response = JSON.parse(xhr.responseText);\r\n            resolve(response);\r\n          } catch (error) {\r\n            resolve({\r\n              success: true,\r\n              message: 'File uploaded successfully',\r\n              fileName: file.name\r\n            });\r\n          }\r\n        } else {\r\n          reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));\r\n        }\r\n      };\r\n\r\n      // Handle network errors\r\n      xhr.onerror = () => {\r\n        reject(new Error('Network error occurred while uploading file'));\r\n      };\r\n\r\n      // Send the request\r\n      xhr.send(formData);\r\n    } catch (error) {\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Upload multiple files to the server\r\n * @param {File[]} files - Array of files to upload\r\n * @param {Function} onProgress - Callback function for upload progress\r\n * @returns {Promise} - Promise that resolves with an array of server responses\r\n */\r\nexport const uploadMultipleFiles = async (\r\n  files: File[],\r\n  onProgress?: (fileName: string, progress: number) => void\r\n): Promise<any[]> => {\r\n  const uploadPromises = files.map((file) => {\r\n    return uploadFile(file, (progress) => {\r\n      if (onProgress) {\r\n        onProgress(file.name, progress);\r\n      }\r\n    });\r\n  });\r\n\r\n  return Promise.all(uploadPromises);\r\n};\r\n\r\n/**\r\n * Check if a file type is allowed\r\n * @param {string} fileType - MIME type of the file\r\n * @param {string[]} allowedTypes - Array of allowed MIME types\r\n * @returns {boolean} - True if the file type is allowed\r\n */\r\nexport const isFileTypeAllowed = (fileType: string, allowedTypes: string[]): boolean => {\r\n  return allowedTypes.includes(fileType);\r\n};\r\n\r\n/**\r\n * Check if a file size is within the limit\r\n * @param {number} fileSize - Size of the file in bytes\r\n * @param {number} maxSizeMB - Maximum allowed size in MB\r\n * @returns {boolean} - True if the file size is within the limit\r\n */\r\nexport const isFileSizeValid = (fileSize: number, maxSizeMB: number): boolean => {\r\n  const maxSizeBytes = maxSizeMB * 1024 * 1024;\r\n  return fileSize <= maxSizeBytes;\r\n};\r\n\r\n/**\r\n * List all CSV files stored in the database\r\n * @param {string} clientEmail - Optional client email to filter by\r\n * @returns {Promise} - Promise that resolves with the list of CSV files\r\n */\r\nexport const listCSVFiles = async (clientEmail?: string): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/list-csv-files', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        client_email: clientEmail\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error listing CSV files:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get CSV data from the database\r\n * @param {string} indexName - Name of the index to get data for\r\n * @param {number} limit - Maximum number of rows to return (default: 100)\r\n * @param {number} offset - Number of rows to skip (default: 0)\r\n * @returns {Promise} - Promise that resolves with the CSV data\r\n */\r\nexport const getCSVData = async (indexName: string, limit: number = 100, offset: number = 0): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/get-csv-data', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        index_name: indexName,\r\n        limit,\r\n        offset\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error getting CSV data:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get available embedding models\r\n * @returns {Promise} - Promise that resolves with the list of available embedding models\r\n */\r\nexport const getEmbeddingModels = async (): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/list-embedding-models', {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      }\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error getting embedding models:', error);\r\n\r\n    // Return fallback data when backend is not available\r\n    return {\r\n      success: true,\r\n      models: {\r\n        \"all-MiniLM-L6-v2\": {\r\n          \"name\": \"all-MiniLM-L6-v2\",\r\n          \"description\": \"Sentence Transformers model for semantic similarity\",\r\n          \"dimensions\": 384\r\n        },\r\n        \"all-mpnet-base-v2\": {\r\n          \"name\": \"all-mpnet-base-v2\",\r\n          \"description\": \"High-quality sentence embeddings\",\r\n          \"dimensions\": 768\r\n        },\r\n        \"paraphrase-MiniLM-L6-v2\": {\r\n          \"name\": \"paraphrase-MiniLM-L6-v2\",\r\n          \"description\": \"Paraphrase detection model\",\r\n          \"dimensions\": 384\r\n        }\r\n      },\r\n      default_model: \"all-MiniLM-L6-v2\"\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * Check if an error or response is a cancellation\r\n * @param {any} errorOrResponse - Error object or response from upload/cancel functions\r\n * @returns {boolean} - True if the error/response indicates a cancellation\r\n */\r\nexport const isCancellation = (errorOrResponse: any): boolean => {\r\n  // Check for our custom cancellation response\r\n  if (errorOrResponse && errorOrResponse.cancelled === true) {\r\n    return true;\r\n  }\r\n\r\n  // Check for error message containing cancellation text\r\n  if (errorOrResponse instanceof Error) {\r\n    const errorMessage = errorOrResponse.message.toLowerCase();\r\n    return errorMessage.includes('cancel') ||\r\n           errorMessage.includes('abort') ||\r\n           errorMessage.includes('user interrupt');\r\n  }\r\n\r\n  // Check for response with cancellation status\r\n  if (errorOrResponse && errorOrResponse.status === 'cancelled') {\r\n    return true;\r\n  }\r\n\r\n  // Check for response with error_type indicating cancellation\r\n  if (errorOrResponse && errorOrResponse.error_type === 'upload_cancelled') {\r\n    return true;\r\n  }\r\n\r\n  return false;\r\n};\r\n\r\n/**\r\n * Fetch emails from the API\r\n * @returns {Promise<string[]>} - Promise that resolves with an array of email addresses\r\n */\r\nexport const fetchEmails = async (): Promise<string[]> => {\r\n  try {\r\n    const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eSearch', {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'xxxid': 'QUKTYWK'\r\n      }\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    if (data.statusCode === 200 && Array.isArray(data.source)) {\r\n      // Parse each JSON string in the source array and extract emails\r\n      const emails = data.source.map((jsonStr: string) => {\r\n        try {\r\n          const userObj = JSON.parse(jsonStr);\r\n          return userObj.email || '';\r\n        } catch (error) {\r\n          console.error('Error parsing JSON:', error);\r\n          return '';\r\n        }\r\n      }).filter(Boolean); // Remove empty strings\r\n\r\n      return emails;\r\n    }\r\n\r\n    return [];\r\n  } catch (error) {\r\n    console.error('Error fetching emails:', error);\r\n    return [];\r\n  }\r\n};\r\n\r\n/**\r\n * Create PINE Collection Entry for index storage\r\n * @param {string} embedModel - Embedding model name (stored as api_key)\r\n * @param {string} indexName - Index name\r\n * @param {string} clientEmail - Client email\r\n * @returns {Promise<any>} - Promise that resolves with the server response\r\n */\r\nexport const createPineCollectionEntry = async (\r\n  embedModel: string,\r\n  indexName: string,\r\n  clientEmail: string\r\n): Promise<any> => {\r\n  try {\r\n    console.log(`Creating PINE collection entry: embedModel=${embedModel}, indexName=${indexName}, clientEmail=${clientEmail}`);\r\n\r\n    const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eCreateCol?colname=PINE', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'xxxid': 'PINE'\r\n      },\r\n      body: JSON.stringify({\r\n        api_key: embedModel,    // Store embedding model name as api_key\r\n        index_name: indexName,  // Store provided index name\r\n        client: clientEmail     // Store email as client\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(`Server error: ${response.status} - ${errorText}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    console.log('PINE collection entry created successfully:', result);\r\n    return result;\r\n  } catch (error) {\r\n    console.error('Error creating PINE collection entry:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get all indexes for a specific email from PINE collection\r\n * @param {string} clientEmail - Client email to filter by\r\n * @returns {Promise<any[]>} - Promise that resolves with array of index data\r\n */\r\nexport const getIndexesByEmail = async (clientEmail: string): Promise<any[]> => {\r\n  try {\r\n    console.log(`Fetching indexes for email: ${clientEmail}`);\r\n\r\n    const response = await fetch(\r\n      `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&filtercount=1&f1_field=client_S&f1_op=eq&f1_value=${encodeURIComponent(clientEmail)}`,\r\n      {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'xxxid': 'PINE'\r\n        }\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log('PINE collection response for email:', data);\r\n\r\n    if (data.statusCode === 200 && Array.isArray(data.source)) {\r\n      // Parse each JSON string in the source array\r\n      const indexes = data.source.map((jsonStr: string, index: number) => {\r\n        try {\r\n          const indexObj = JSON.parse(jsonStr);\r\n          return {\r\n            _id: indexObj._id?.$oid || indexObj._id || `pine-item-${index}`,\r\n            email: indexObj.client || clientEmail,\r\n            index_name: indexObj.index_name || 'N/A',\r\n            embed_model: indexObj.api_key || 'N/A', // api_key contains the embedding model\r\n            source: 'PINE' as const,\r\n            originalData: indexObj\r\n          };\r\n        } catch (error) {\r\n          console.error('Error parsing PINE index JSON:', error);\r\n          return null;\r\n        }\r\n      }).filter((item: any) => item !== null);\r\n\r\n      return indexes;\r\n    }\r\n\r\n    return [];\r\n  } catch (error) {\r\n    console.error('Error fetching indexes by email:', error);\r\n    return [];\r\n  }\r\n};\r\n\r\n/**\r\n * Check if a FAISS index exists\r\n * @param {string} indexName - Name of the index to check\r\n * @param {string} client - Client email or identifier (optional)\r\n * @param {string} embedModel - Name of the embedding model to use (optional)\r\n * @returns {Promise<{exists: boolean, embedding_model?: string}>} - Promise that resolves with info about the index\r\n */\r\nexport const checkIndexExists = async (\r\n  indexName: string,\r\n  client?: string,\r\n  embedModel?: string\r\n): Promise<{exists: boolean, embedding_model?: string}> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/check-index', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        index_name: indexName,\r\n        client: client,\r\n        embed_model: embedModel\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    if (data.success) {\r\n      return {\r\n        exists: data.exists,\r\n        embedding_model: data.embedding_model\r\n      };\r\n    }\r\n\r\n    return { exists: false };\r\n  } catch (error) {\r\n    console.error('Error checking if index exists:', error);\r\n    return { exists: false };\r\n  }\r\n};\r\n\r\n/**\r\n * Cancel an ongoing upload\r\n * @param {string} uploadId - The ID of the upload to cancel\r\n * @param {AbortController} abortController - Optional AbortController to abort the HTTP request\r\n * @returns {Promise<any>} - Promise that resolves with the server response\r\n */\r\nexport const cancelUpload = async (uploadId: string, abortController?: AbortController): Promise<any> => {\r\n  try {\r\n    // First, abort the HTTP request if an AbortController is provided\r\n    if (abortController) {\r\n      try {\r\n        abortController.abort();\r\n        console.log('HTTP request aborted');\r\n      } catch (abortError) {\r\n        // Don't log this as an error since it's expected behavior\r\n        console.log('Note: AbortController already used or not applicable');\r\n        // Continue with server-side cancellation even if client-side abort fails\r\n      }\r\n    }\r\n\r\n    // Then, send a cancellation request to the server\r\n    console.log(`Sending cancellation request for upload ${uploadId}`);\r\n    const response = await fetch('http://localhost:5010/api/cancel-upload', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        upload_id: uploadId\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(`Server error: ${response.status} - ${errorText}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log('Cancellation response:', data);\r\n\r\n    // Verify cancellation by checking status\r\n    try {\r\n      const statusResponse = await checkUploadStatus(uploadId);\r\n      console.log('Status after cancellation:', statusResponse);\r\n    } catch (statusError) {\r\n      console.error('Error checking status after cancellation:', statusError);\r\n      // Continue even if status check fails\r\n    }\r\n\r\n    return data;\r\n  } catch (error) {\r\n    console.error('Error cancelling upload:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Check the status of an ongoing upload\r\n * @param {string} uploadId - The ID of the upload to check\r\n * @param {boolean} silent - Whether to suppress console errors (default: false)\r\n * @returns {Promise<any>} - Promise that resolves with the upload status\r\n */\r\nexport const checkUploadStatus = async (uploadId: string, silent: boolean = false): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/upload-status', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        upload_id: uploadId\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(`Server error: ${response.status} - ${errorText}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    // Log cancellation status if detected\r\n    if (data.success && data.cancelled) {\r\n      console.log(`Upload ${uploadId} is marked as cancelled. Status: ${data.status}`);\r\n    }\r\n\r\n    return data;\r\n  } catch (error) {\r\n    if (!silent) {\r\n      console.error('Error checking upload status:', error);\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;;;;AACM,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAaO,MAAM,mBAAmB,OAC9B,MACA,aACA,WACA,YACA,QACA,YACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,qBAAqB;YACrB,IAAI,KAAK,IAAI,KAAK,YAAY;gBAC5B,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,iCAAiC;YACjC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,kDAAkD;YAClD,IAAI,aAAa;gBACf,SAAS,MAAM,CAAC,UAAU;YAC5B;YAEA,8BAA8B;YAC9B,IAAI,WAAW;gBACb,SAAS,MAAM,CAAC,cAAc;YAChC;YAEA,8BAA8B;YAC9B,IAAI,WAAW;gBACb,SAAS,MAAM,CAAC,cAAc;YAChC;YAEA,2CAA2C;YAC3C,IAAI,YAAY;gBACd,SAAS,MAAM,CAAC,eAAe;YACjC;YAEA,+CAA+C;YAC/C,IAAI,YAAY;gBACd,SAAS,MAAM,CAAC,eAAe;YACjC;YAEA,uDAAuD;YACvD,MAAM,MAAM,IAAI;YAEhB,mDAAmD;YACnD,IAAI,QAAQ;gBACV,OAAO,OAAO,GAAG;oBACf,IAAI,KAAK;oBACT,yEAAyE;oBACzE,wDAAwD;oBACxD,QAAQ;wBACN,SAAS;wBACT,WAAW;wBACX,SAAS;oBACX;gBACF;YACF;YAEA,gDAAgD;YAChD,IAAI,IAAI,CAAC,QAAQ,wCAAwC;YACzD,gEAAgE;YAChE,IAAI,gBAAgB,CAAC,SAAS;YAE9B,6CAA6C;YAC7C,IAAI,YAAY;gBACd,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;oBACvB,IAAI,MAAM,gBAAgB,EAAE;wBAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;wBAC3D,WAAW;oBACb;gBACF;YACF;YAEA,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,QAAQ;4BACN,SAAS;4BACT,SAAS;4BACT,WAAW,UAAU,mCAAmC;wBAC1D;oBACF;gBACF,OAAO;oBACL,OAAO,IAAI,MAAM,CAAC,cAAc,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,UAAU,EAAE;gBAClE;YACF;YAEA,wBAAwB;YACxB,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,mBAAmB;YACnB,IAAI,IAAI,CAAC;QACX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAGO,MAAM,sBAAsB;AAQ5B,MAAM,aAAa,OACxB,MACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,iCAAiC;YACjC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,8BAA8B;YAC9B,MAAM,MAAM,IAAI;YAEhB,wBAAwB;YACxB,IAAI,IAAI,CAAC,QAAQ,oCAAoC;YAErD,wBAAwB;YACxB,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;gBACvB,IAAI,MAAM,gBAAgB,IAAI,YAAY;oBACxC,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;oBAC3D,WAAW;gBACb;YACF;YAEA,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,QAAQ;4BACN,SAAS;4BACT,SAAS;4BACT,UAAU,KAAK,IAAI;wBACrB;oBACF;gBACF,OAAO;oBACL,OAAO,IAAI,MAAM,CAAC,cAAc,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,UAAU,EAAE;gBAClE;YACF;YAEA,wBAAwB;YACxB,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,mBAAmB;YACnB,IAAI,IAAI,CAAC;QACX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAQO,MAAM,sBAAsB,OACjC,OACA;IAEA,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC;QAChC,OAAO,WAAW,MAAM,CAAC;YACvB,IAAI,YAAY;gBACd,WAAW,KAAK,IAAI,EAAE;YACxB;QACF;IACF;IAEA,OAAO,QAAQ,GAAG,CAAC;AACrB;AAQO,MAAM,oBAAoB,CAAC,UAAkB;IAClD,OAAO,aAAa,QAAQ,CAAC;AAC/B;AAQO,MAAM,kBAAkB,CAAC,UAAkB;IAChD,MAAM,eAAe,YAAY,OAAO;IACxC,OAAO,YAAY;AACrB;AAOO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,4CAA4C;YACvE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AASO,MAAM,aAAa,OAAO,WAAmB,QAAgB,GAAG,EAAE,SAAiB,CAAC;IACzF,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,0CAA0C;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,YAAY;gBACZ;gBACA;YACF;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,mDAAmD;YAC9E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,qDAAqD;QACrD,OAAO;YACL,SAAS;YACT,QAAQ;gBACN,oBAAoB;oBAClB,QAAQ;oBACR,eAAe;oBACf,cAAc;gBAChB;gBACA,qBAAqB;oBACnB,QAAQ;oBACR,eAAe;oBACf,cAAc;gBAChB;gBACA,2BAA2B;oBACzB,QAAQ;oBACR,eAAe;oBACf,cAAc;gBAChB;YACF;YACA,eAAe;QACjB;IACF;AACF;AAOO,MAAM,iBAAiB,CAAC;IAC7B,6CAA6C;IAC7C,IAAI,mBAAmB,gBAAgB,SAAS,KAAK,MAAM;QACzD,OAAO;IACT;IAEA,uDAAuD;IACvD,IAAI,2BAA2B,OAAO;QACpC,MAAM,eAAe,gBAAgB,OAAO,CAAC,WAAW;QACxD,OAAO,aAAa,QAAQ,CAAC,aACtB,aAAa,QAAQ,CAAC,YACtB,aAAa,QAAQ,CAAC;IAC/B;IAEA,8CAA8C;IAC9C,IAAI,mBAAmB,gBAAgB,MAAM,KAAK,aAAa;QAC7D,OAAO;IACT;IAEA,6DAA6D;IAC7D,IAAI,mBAAmB,gBAAgB,UAAU,KAAK,oBAAoB;QACxE,OAAO;IACT;IAEA,OAAO;AACT;AAMO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,qDAAqD;YAChF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS;YACX;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE;QACpD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,UAAU,KAAK,OAAO,MAAM,OAAO,CAAC,KAAK,MAAM,GAAG;YACzD,gEAAgE;YAChE,MAAM,SAAS,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC9B,IAAI;oBACF,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,OAAO,QAAQ,KAAK,IAAI;gBAC1B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,OAAO;gBACT;YACF,GAAG,MAAM,CAAC,UAAU,uBAAuB;YAE3C,OAAO;QACT;QAEA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,EAAE;IACX;AACF;AASO,MAAM,4BAA4B,OACvC,YACA,WACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,WAAW,YAAY,EAAE,UAAU,cAAc,EAAE,aAAa;QAE1H,MAAM,WAAW,MAAM,MAAM,qEAAqE;YAChG,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS;YACX;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,SAAS;gBACT,YAAY;gBACZ,QAAQ,YAAgB,wBAAwB;YAClD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACnE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,+CAA+C;QAC3D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF;AAOO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,aAAa;QAExD,MAAM,WAAW,MAAM,MACrB,CAAC,4HAA4H,EAAE,mBAAmB,cAAc,EAChK;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS;YACX;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE;QACpD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,IAAI,KAAK,UAAU,KAAK,OAAO,MAAM,OAAO,CAAC,KAAK,MAAM,GAAG;YACzD,6CAA6C;YAC7C,MAAM,UAAU,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,SAAiB;gBAChD,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,OAAO;wBACL,KAAK,SAAS,GAAG,EAAE,QAAQ,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,OAAO;wBAC/D,OAAO,SAAS,MAAM,IAAI;wBAC1B,YAAY,SAAS,UAAU,IAAI;wBACnC,aAAa,SAAS,OAAO,IAAI;wBACjC,QAAQ;wBACR,cAAc;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,OAAO;gBACT;YACF,GAAG,MAAM,CAAC,CAAC,OAAc,SAAS;YAElC,OAAO;QACT;QAEA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,EAAE;IACX;AACF;AASO,MAAM,mBAAmB,OAC9B,WACA,QACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,yCAAyC;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,YAAY;gBACZ,QAAQ;gBACR,aAAa;YACf;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE;QACpD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,OAAO,EAAE;YAChB,OAAO;gBACL,QAAQ,KAAK,MAAM;gBACnB,iBAAiB,KAAK,eAAe;YACvC;QACF;QAEA,OAAO;YAAE,QAAQ;QAAM;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,QAAQ;QAAM;IACzB;AACF;AAQO,MAAM,eAAe,OAAO,UAAkB;IACnD,IAAI;QACF,kEAAkE;QAClE,IAAI,iBAAiB;YACnB,IAAI;gBACF,gBAAgB,KAAK;gBACrB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,YAAY;gBACnB,0DAA0D;gBAC1D,QAAQ,GAAG,CAAC;YACZ,yEAAyE;YAC3E;QACF;QAEA,kDAAkD;QAClD,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU;QACjE,MAAM,WAAW,MAAM,MAAM,2CAA2C;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,WAAW;YACb;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,yCAAyC;QACzC,IAAI;YACF,MAAM,iBAAiB,MAAM,kBAAkB;YAC/C,QAAQ,GAAG,CAAC,8BAA8B;QAC5C,EAAE,OAAO,aAAa;YACpB,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,sCAAsC;QACxC;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAQO,MAAM,oBAAoB,OAAO,UAAkB,SAAkB,KAAK;IAC/E,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,2CAA2C;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,WAAW;YACb;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,sCAAsC;QACtC,IAAI,KAAK,OAAO,IAAI,KAAK,SAAS,EAAE;YAClC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,SAAS,iCAAiC,EAAE,KAAK,MAAM,EAAE;QACjF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,IAAI,CAAC,QAAQ;YACX,QAAQ,KAAK,CAAC,iCAAiC;QACjD;QACA,MAAM;IACR;AACF"}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/services/dataManagementService.ts"], "sourcesContent": ["/**\r\n * Data Management Service for FAISS Index Data\r\n * Handles fetching, filtering, and exporting JSON data from FAISS indexes\r\n */\r\n\r\nexport interface IndexDataRow {\r\n  id: string;\r\n  [key: string]: any;\r\n}\r\n\r\nexport interface DataFetchResponse {\r\n  success: boolean;\r\n  data: IndexDataRow[];\r\n  total: number;\r\n  error?: string;\r\n}\r\n\r\nexport interface ExportOptions {\r\n  format: 'csv' | 'json';\r\n  filename?: string;\r\n  filteredData?: IndexDataRow[];\r\n}\r\n\r\n/**\r\n * Fetch JSON data for a specific FAISS index\r\n * @param indexName - Name of the FAISS index\r\n * @param limit - Maximum number of rows to fetch (default: 1000)\r\n * @param offset - Number of rows to skip (default: 0)\r\n * @returns Promise with data fetch response\r\n */\r\nexport const fetchIndexData = async (\r\n  indexName: string,\r\n  limit: number = 1000,\r\n  offset: number = 0\r\n): Promise<DataFetchResponse> => {\r\n  try {\r\n    // First try to get the JSON metadata file directly\r\n    const response = await fetch(`http://localhost:5010/api/get-faiss-metadata/${indexName}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      }\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    \r\n    if (result.success) {\r\n      // Transform the data to ensure each row has a unique ID\r\n      const transformedData = result.data.map((row: any, index: number) => ({\r\n        id: row.id || `row_${offset + index}`,\r\n        ...row\r\n      }));\r\n\r\n      return {\r\n        success: true,\r\n        data: transformedData,\r\n        total: result.total || transformedData.length\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        data: [],\r\n        total: 0,\r\n        error: result.error || 'Failed to fetch data'\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error('Error fetching index data:', error);\r\n    return {\r\n      success: false,\r\n      data: [],\r\n      total: 0,\r\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * Delete specific rows from a FAISS index\r\n * @param indexName - Name of the FAISS index\r\n * @param rowIds - Array of row IDs to delete\r\n * @returns Promise with deletion result\r\n */\r\nexport const deleteIndexRows = async (\r\n  indexName: string,\r\n  rowIds: string[]\r\n): Promise<{ success: boolean; deletedCount: number; error?: string }> => {\r\n  try {\r\n    const response = await fetch(`http://localhost:5010/api/delete-faiss-rows/${indexName}`, {\r\n      method: 'DELETE',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        row_ids: rowIds\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    \r\n    if (result.success) {\r\n      return {\r\n        success: true,\r\n        deletedCount: result.deleted_count || rowIds.length\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        deletedCount: 0,\r\n        error: result.error || 'Failed to delete rows'\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error('Error deleting index rows:', error);\r\n    return {\r\n      success: false,\r\n      deletedCount: 0,\r\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * Export data to CSV format\r\n * @param data - Array of data rows to export\r\n * @param filename - Optional filename for the export\r\n */\r\nexport const exportToCSV = (data: IndexDataRow[], filename?: string): void => {\r\n  if (data.length === 0) {\r\n    console.warn('No data to export');\r\n    return;\r\n  }\r\n\r\n  // Get all unique keys from the data\r\n  const allKeys = Array.from(new Set(data.flatMap(row => Object.keys(row))));\r\n  \r\n  // Create CSV header\r\n  const csvHeader = allKeys.join(',');\r\n  \r\n  // Create CSV rows\r\n  const csvRows = data.map(row => {\r\n    return allKeys.map(key => {\r\n      const value = row[key];\r\n      // Handle values that might contain commas or quotes\r\n      if (typeof value === 'string' && (value.includes(',') || value.includes('\"') || value.includes('\\n'))) {\r\n        return `\"${value.replace(/\"/g, '\"\"')}\"`;\r\n      }\r\n      return value || '';\r\n    }).join(',');\r\n  });\r\n\r\n  // Combine header and rows\r\n  const csvContent = [csvHeader, ...csvRows].join('\\n');\r\n  \r\n  // Create and download the file\r\n  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n  const link = document.createElement('a');\r\n  const url = URL.createObjectURL(blob);\r\n  \r\n  link.setAttribute('href', url);\r\n  link.setAttribute('download', filename || `index_data_${new Date().toISOString().split('T')[0]}.csv`);\r\n  link.style.visibility = 'hidden';\r\n  \r\n  document.body.appendChild(link);\r\n  link.click();\r\n  document.body.removeChild(link);\r\n  \r\n  URL.revokeObjectURL(url);\r\n};\r\n\r\n/**\r\n * Export data to JSON format\r\n * @param data - Array of data rows to export\r\n * @param filename - Optional filename for the export\r\n */\r\nexport const exportToJSON = (data: IndexDataRow[], filename?: string): void => {\r\n  if (data.length === 0) {\r\n    console.warn('No data to export');\r\n    return;\r\n  }\r\n\r\n  const jsonContent = JSON.stringify(data, null, 2);\r\n  \r\n  // Create and download the file\r\n  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });\r\n  const link = document.createElement('a');\r\n  const url = URL.createObjectURL(blob);\r\n  \r\n  link.setAttribute('href', url);\r\n  link.setAttribute('download', filename || `index_data_${new Date().toISOString().split('T')[0]}.json`);\r\n  link.style.visibility = 'hidden';\r\n  \r\n  document.body.appendChild(link);\r\n  link.click();\r\n  document.body.removeChild(link);\r\n  \r\n  URL.revokeObjectURL(url);\r\n};\r\n\r\n/**\r\n * Filter data based on search term across all fields\r\n * @param data - Array of data rows to filter\r\n * @param searchTerm - Search term to filter by\r\n * @returns Filtered array of data rows\r\n */\r\nexport const filterData = (data: IndexDataRow[], searchTerm: string): IndexDataRow[] => {\r\n  if (!searchTerm.trim()) {\r\n    return data;\r\n  }\r\n\r\n  const searchLower = searchTerm.toLowerCase().trim();\r\n  \r\n  return data.filter(row => {\r\n    return Object.values(row).some(value => {\r\n      if (value === null || value === undefined) return false;\r\n      return String(value).toLowerCase().includes(searchLower);\r\n    });\r\n  });\r\n};\r\n\r\n/**\r\n * Get unique values for a specific column (for filter dropdowns)\r\n * @param data - Array of data rows\r\n * @param columnKey - Key of the column to get unique values for\r\n * @returns Array of unique values\r\n */\r\nexport const getUniqueColumnValues = (data: IndexDataRow[], columnKey: string): string[] => {\r\n  const values = data\r\n    .map(row => row[columnKey])\r\n    .filter(value => value !== null && value !== undefined)\r\n    .map(value => String(value));\r\n  \r\n  return Array.from(new Set(values)).sort();\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AA2BM,MAAM,iBAAiB,OAC5B,WACA,QAAgB,IAAI,EACpB,SAAiB,CAAC;IAElB,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,MAAM,CAAC,6CAA6C,EAAE,WAAW,EAAE;YACxF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,OAAO,OAAO,EAAE;YAClB,wDAAwD;YACxD,MAAM,kBAAkB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,QAAkB,CAAC;oBACpE,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,OAAO;oBACrC,GAAG,GAAG;gBACR,CAAC;YAED,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,OAAO,OAAO,KAAK,IAAI,gBAAgB,MAAM;YAC/C;QACF,OAAO;YACL,OAAO;gBACL,SAAS;gBACT,MAAM,EAAE;gBACR,OAAO;gBACP,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,SAAS;YACT,MAAM,EAAE;YACR,OAAO;YACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAQO,MAAM,kBAAkB,OAC7B,WACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,4CAA4C,EAAE,WAAW,EAAE;YACvF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,SAAS;YACX;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO;gBACL,SAAS;gBACT,cAAc,OAAO,aAAa,IAAI,OAAO,MAAM;YACrD;QACF,OAAO;YACL,OAAO;gBACL,SAAS;gBACT,cAAc;gBACd,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,SAAS;YACT,cAAc;YACd,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAOO,MAAM,cAAc,CAAC,MAAsB;IAChD,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,QAAQ,IAAI,CAAC;QACb;IACF;IAEA,oCAAoC;IACpC,MAAM,UAAU,MAAM,IAAI,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,CAAA,MAAO,OAAO,IAAI,CAAC;IAEnE,oBAAoB;IACpB,MAAM,YAAY,QAAQ,IAAI,CAAC;IAE/B,kBAAkB;IAClB,MAAM,UAAU,KAAK,GAAG,CAAC,CAAA;QACvB,OAAO,QAAQ,GAAG,CAAC,CAAA;YACjB,MAAM,QAAQ,GAAG,CAAC,IAAI;YACtB,oDAAoD;YACpD,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,CAAC,QAAQ,MAAM,QAAQ,CAAC,QAAQ,MAAM,QAAQ,CAAC,KAAK,GAAG;gBACrG,OAAO,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;YACzC;YACA,OAAO,SAAS;QAClB,GAAG,IAAI,CAAC;IACV;IAEA,0BAA0B;IAC1B,MAAM,aAAa;QAAC;WAAc;KAAQ,CAAC,IAAI,CAAC;IAEhD,+BAA+B;IAC/B,MAAM,OAAO,IAAI,KAAK;QAAC;KAAW,EAAE;QAAE,MAAM;IAA0B;IACtE,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,MAAM,MAAM,IAAI,eAAe,CAAC;IAEhC,KAAK,YAAY,CAAC,QAAQ;IAC1B,KAAK,YAAY,CAAC,YAAY,YAAY,CAAC,WAAW,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACpG,KAAK,KAAK,CAAC,UAAU,GAAG;IAExB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAE1B,IAAI,eAAe,CAAC;AACtB;AAOO,MAAM,eAAe,CAAC,MAAsB;IACjD,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,QAAQ,IAAI,CAAC;QACb;IACF;IAEA,MAAM,cAAc,KAAK,SAAS,CAAC,MAAM,MAAM;IAE/C,+BAA+B;IAC/B,MAAM,OAAO,IAAI,KAAK;QAAC;KAAY,EAAE;QAAE,MAAM;IAAkC;IAC/E,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,MAAM,MAAM,IAAI,eAAe,CAAC;IAEhC,KAAK,YAAY,CAAC,QAAQ;IAC1B,KAAK,YAAY,CAAC,YAAY,YAAY,CAAC,WAAW,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;IACrG,KAAK,KAAK,CAAC,UAAU,GAAG;IAExB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAE1B,IAAI,eAAe,CAAC;AACtB;AAQO,MAAM,aAAa,CAAC,MAAsB;IAC/C,IAAI,CAAC,WAAW,IAAI,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,cAAc,WAAW,WAAW,GAAG,IAAI;IAEjD,OAAO,KAAK,MAAM,CAAC,CAAA;QACjB,OAAO,OAAO,MAAM,CAAC,KAAK,IAAI,CAAC,CAAA;YAC7B,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;YAClD,OAAO,OAAO,OAAO,WAAW,GAAG,QAAQ,CAAC;QAC9C;IACF;AACF;AAQO,MAAM,wBAAwB,CAAC,MAAsB;IAC1D,MAAM,SAAS,KACZ,GAAG,CAAC,CAAA,MAAO,GAAG,CAAC,UAAU,EACzB,MAAM,CAAC,CAAA,QAAS,UAAU,QAAQ,UAAU,WAC5C,GAAG,CAAC,CAAA,QAAS,OAAO;IAEvB,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;AACzC"}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1105, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/modals/EditDataModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, useMemo } from \"react\";\r\nimport {\r\n  PiX,\r\n  PiMagnifyingGlass,\r\n  PiTrash,\r\n  PiCheckSquare,\r\n  PiSquare,\r\n  PiTable,\r\n  PiWarning,\r\n  PiSpinner,\r\n  PiFileText,\r\n  PiFileCsv,\r\n  PiCaretLeft,\r\n  PiCaretRight,\r\n  PiFunnel,\r\n  PiGlobe,\r\n  PiTag\r\n} from \"react-icons/pi\";\r\nimport {\r\n  fetchIndexData,\r\n  deleteIndexRows,\r\n  exportToCSV,\r\n  exportToJSON,\r\n  filterData,\r\n  getUniqueColumnValues,\r\n  IndexDataRow\r\n} from '../../services/dataManagementService';\r\n\r\ninterface IndexData {\r\n  _id: string;\r\n  email: string;\r\n  index_name: string;\r\n  embed_model?: string;\r\n  api_key?: string;\r\n  source: 'FAISS' | 'PINE';\r\n  [key: string]: any;\r\n}\r\n\r\ninterface EditDataModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  indexData: IndexData | null;\r\n}\r\n\r\nconst EditDataModal: React.FC<EditDataModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  indexData\r\n}) => {\r\n  const [data, setData] = useState<IndexDataRow[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\r\n\r\n  // Filter states\r\n  const [urlFilter, setUrlFilter] = useState(\"\");\r\n  const [categoryFilter, setCategoryFilter] = useState(\"\");\r\n  const [showFilters, setShowFilters] = useState(false);\r\n\r\n  // Pagination state\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [pageSize] = useState(20); // Fixed page size, can be made configurable\r\n  const [totalRows, setTotalRows] = useState(0);\r\n  const [allData, setAllData] = useState<IndexDataRow[]>([]); // Store all data for search/export\r\n\r\n  // Enhanced filtering function that includes URL and category filters\r\n  const applyAllFilters = (dataToFilter: IndexDataRow[]) => {\r\n    let filtered = dataToFilter;\r\n\r\n    // Apply search term filter\r\n    if (searchTerm.trim()) {\r\n      filtered = filterData(filtered, searchTerm);\r\n    }\r\n\r\n    // Apply URL filter\r\n    if (urlFilter.trim()) {\r\n      filtered = filtered.filter(row => {\r\n        const url = row.url || '';\r\n        return String(url).toLowerCase().includes(urlFilter.toLowerCase());\r\n      });\r\n    }\r\n\r\n    // Apply category filter\r\n    if (categoryFilter.trim()) {\r\n      filtered = filtered.filter(row => {\r\n        const category = row.category || '';\r\n        return String(category).toLowerCase().includes(categoryFilter.toLowerCase());\r\n      });\r\n    }\r\n\r\n    return filtered;\r\n  };\r\n\r\n  // Filter data based on all filters (for current page)\r\n  const filteredData = useMemo(() => {\r\n    return applyAllFilters(data);\r\n  }, [data, searchTerm, urlFilter, categoryFilter]);\r\n\r\n  // For search functionality, we need to work with all data\r\n  const allFilteredData = useMemo(() => {\r\n    return applyAllFilters(allData);\r\n  }, [allData, searchTerm, urlFilter, categoryFilter]);\r\n\r\n  // Calculate pagination values (after filtered data is calculated)\r\n  const totalPages = searchTerm\r\n    ? Math.ceil(allFilteredData.length / pageSize)\r\n    : Math.ceil(totalRows / pageSize);\r\n  const hasNextPage = currentPage < totalPages;\r\n  const hasPrevPage = currentPage > 1;\r\n\r\n  // Load data when modal opens or page changes\r\n  useEffect(() => {\r\n    if (isOpen && indexData) {\r\n      loadData();\r\n    }\r\n  }, [isOpen, indexData, currentPage]);\r\n\r\n  // Reset pagination when filters change\r\n  useEffect(() => {\r\n    if (searchTerm || urlFilter || categoryFilter) {\r\n      setCurrentPage(1);\r\n    }\r\n  }, [searchTerm, urlFilter, categoryFilter]);\r\n\r\n  // Clear state when modal closes\r\n  useEffect(() => {\r\n    if (!isOpen) {\r\n      setData([]);\r\n      setAllData([]);\r\n      setSearchTerm(\"\");\r\n      setUrlFilter(\"\");\r\n      setCategoryFilter(\"\");\r\n      setShowFilters(false);\r\n      setSelectedRows(new Set());\r\n      setError(null);\r\n      setShowDeleteConfirm(false);\r\n      setCurrentPage(1);\r\n      setTotalRows(0);\r\n    }\r\n  }, [isOpen]);\r\n\r\n  const loadData = async () => {\r\n    if (!indexData) return;\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Calculate offset for pagination\r\n      const offset = (currentPage - 1) * pageSize;\r\n\r\n      // If we have any filters, we need to load all data first for proper filtering\r\n      if (searchTerm || urlFilter || categoryFilter) {\r\n        // Load all data for filtering\r\n        const allResult = await fetchIndexData(indexData.index_name, 10000, 0); // Large limit to get all data\r\n\r\n        if (allResult.success) {\r\n          setAllData(allResult.data);\r\n          const filtered = applyAllFilters(allResult.data);\r\n\r\n          // Get the current page of filtered data\r\n          const paginatedData = filtered.slice(offset, offset + pageSize);\r\n          setData(paginatedData);\r\n          setTotalRows(filtered.length);\r\n        } else {\r\n          setError(allResult.error || 'Failed to load data');\r\n        }\r\n      } else {\r\n        // Load paginated data directly\r\n        const result = await fetchIndexData(indexData.index_name, pageSize, offset);\r\n\r\n        if (result.success) {\r\n          setData(result.data);\r\n          setTotalRows(result.total);\r\n\r\n          // Also load all data for export functionality (only on first page load)\r\n          if (currentPage === 1) {\r\n            const allResult = await fetchIndexData(indexData.index_name, 10000, 0);\r\n            if (allResult.success) {\r\n              setAllData(allResult.data);\r\n            }\r\n          }\r\n        } else {\r\n          setError(result.error || 'Failed to load data');\r\n        }\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Unknown error occurred');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Pagination navigation functions\r\n  const handleNextPage = () => {\r\n    if (hasNextPage) {\r\n      setCurrentPage(prev => prev + 1);\r\n    }\r\n  };\r\n\r\n  const handlePrevPage = () => {\r\n    if (hasPrevPage) {\r\n      setCurrentPage(prev => prev - 1);\r\n    }\r\n  };\r\n\r\n  const handleRowSelect = (rowId: string) => {\r\n    const newSelected = new Set(selectedRows);\r\n    if (newSelected.has(rowId)) {\r\n      newSelected.delete(rowId);\r\n    } else {\r\n      newSelected.add(rowId);\r\n    }\r\n    setSelectedRows(newSelected);\r\n  };\r\n\r\n  const handleSelectAll = () => {\r\n    if (selectedRows.size === filteredData.length) {\r\n      setSelectedRows(new Set());\r\n    } else {\r\n      setSelectedRows(new Set(filteredData.map(row => row.id)));\r\n    }\r\n  };\r\n\r\n  const handleDeleteSelected = async () => {\r\n    if (!indexData || selectedRows.size === 0) return;\r\n\r\n    setIsDeleting(true);\r\n    \r\n    try {\r\n      const result = await deleteIndexRows(indexData.index_name, Array.from(selectedRows));\r\n      \r\n      if (result.success) {\r\n        // Remove deleted rows from local state\r\n        setData(prevData => prevData.filter(row => !selectedRows.has(row.id)));\r\n        setSelectedRows(new Set());\r\n        setShowDeleteConfirm(false);\r\n      } else {\r\n        setError(result.error || 'Failed to delete rows');\r\n      }\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Unknown error occurred');\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  const handleExportCSV = () => {\r\n    const dataToExport = searchTerm ? allFilteredData : allData;\r\n    const filename = `${indexData?.index_name || 'index'}_data_${new Date().toISOString().split('T')[0]}.csv`;\r\n    exportToCSV(dataToExport, filename);\r\n  };\r\n\r\n  const handleExportJSON = () => {\r\n    const dataToExport = searchTerm ? allFilteredData : allData;\r\n    const filename = `${indexData?.index_name || 'index'}_data_${new Date().toISOString().split('T')[0]}.json`;\r\n    exportToJSON(dataToExport, filename);\r\n  };\r\n\r\n  // Get unique values for filters\r\n  const uniqueUrls = useMemo(() => {\r\n    return getUniqueColumnValues(allData, 'url').slice(0, 20); // Limit to 20 for performance\r\n  }, [allData]);\r\n\r\n  const uniqueCategories = useMemo(() => {\r\n    return getUniqueColumnValues(allData, 'category').slice(0, 20); // Limit to 20 for performance\r\n  }, [allData]);\r\n\r\n  // Get column headers from the first row of data (prefer allData if available)\r\n  // This useMemo must be called before any early returns to maintain hook order\r\n  const columns = useMemo(() => {\r\n    if (data.length > 0) return Object.keys(data[0]);\r\n    if (allData.length > 0) return Object.keys(allData[0]);\r\n    return [];\r\n  }, [data, allData]);\r\n\r\n  if (!isOpen || !indexData) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-n800 rounded-lg w-full max-w-7xl max-h-[90vh] flex flex-col\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-n600\">\r\n          <div className=\"flex items-center\">\r\n            <PiTable className=\"text-blue-500 text-2xl mr-3\" />\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold text-n700 dark:text-n20\">\r\n                Edit Data: {indexData.index_name}\r\n              </h3>\r\n              <p className=\"text-sm text-n500 dark:text-n40\">\r\n                {indexData.email}\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"p-2 hover:bg-gray-100 dark:hover:bg-n700 rounded-md transition-colors\"\r\n          >\r\n            <PiX className=\"text-xl text-n500 dark:text-n40\" />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Controls */}\r\n        <div className=\"p-6 border-b border-gray-200 dark:border-n600\">\r\n          <div className=\"flex flex-col gap-4\">\r\n            {/* Search and Filter Toggle */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\r\n              {/* Search */}\r\n              <div className=\"relative flex-1 max-w-md\">\r\n                <PiMagnifyingGlass className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-n400 dark:text-n500\" />\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search across all fields...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 transition-all duration-200\"\r\n                />\r\n              </div>\r\n\r\n              {/* Filter Toggle Button */}\r\n              <button\r\n                onClick={() => setShowFilters(!showFilters)}\r\n                className={`px-4 py-2 rounded-lg border transition-all duration-200 flex items-center gap-2 ${\r\n                  showFilters || urlFilter || categoryFilter\r\n                    ? 'bg-primaryColor text-white border-primaryColor shadow-md'\r\n                    : 'bg-white dark:bg-n800 text-n700 dark:text-n20 border-gray-300 dark:border-n600 hover:bg-gray-50 dark:hover:bg-n700'\r\n                }`}\r\n              >\r\n                <PiFunnel className=\"text-sm\" />\r\n                Filters\r\n                {(urlFilter || categoryFilter) && (\r\n                  <span className=\"bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full\">\r\n                    {[urlFilter, categoryFilter].filter(Boolean).length}\r\n                  </span>\r\n                )}\r\n              </button>\r\n            </div>\r\n\r\n            {/* Advanced Filters */}\r\n            {showFilters && (\r\n              <div className=\"bg-gray-50 dark:bg-n700 rounded-lg p-4 border border-gray-200 dark:border-n600\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  {/* URL Filter */}\r\n                  <div className=\"relative\">\r\n                    <label className=\"block text-sm font-medium text-n700 dark:text-white mb-2\">\r\n                      <PiGlobe className=\"inline mr-2 text-primaryColor\" />\r\n                      Filter by URL\r\n                    </label>\r\n                    <div className=\"relative\">\r\n                      <input\r\n                        type=\"text\"\r\n                        placeholder=\"Enter URL to filter...\"\r\n                        value={urlFilter}\r\n                        onChange={(e) => setUrlFilter(e.target.value)}\r\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 transition-all duration-200\"\r\n                      />\r\n                      {urlFilter && (\r\n                        <button\r\n                          onClick={() => setUrlFilter(\"\")}\r\n                          className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-n400 hover:text-n600 dark:hover:text-n300 transition-colors\"\r\n                        >\r\n                          <PiX />\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                    {uniqueUrls.length > 0 && (\r\n                      <div className=\"mt-2\">\r\n                        <p className=\"text-xs text-n500 dark:text-n400 mb-1\">Common URLs:</p>\r\n                        <div className=\"flex flex-wrap gap-1\">\r\n                          {uniqueUrls.slice(0, 5).map((url, index) => (\r\n                            <button\r\n                              key={index}\r\n                              onClick={() => setUrlFilter(url)}\r\n                              className=\"text-xs px-2 py-1 bg-white dark:bg-n800 border border-gray-300 dark:border-n600 rounded text-n700 dark:text-n20 hover:bg-primaryColor hover:text-white hover:border-primaryColor transition-all duration-200\"\r\n                              title={url}\r\n                            >\r\n                              {url.length > 20 ? `${url.substring(0, 20)}...` : url}\r\n                            </button>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Category Filter */}\r\n                  <div className=\"relative\">\r\n                    <label className=\"block text-sm font-medium text-n700 dark:text-n20 mb-2\">\r\n                      <PiTag className=\"inline mr-2 text-primaryColor\" />\r\n                      Filter by Category\r\n                    </label>\r\n                    <div className=\"relative\">\r\n                      <input\r\n                        type=\"text\"\r\n                        placeholder=\"Enter category to filter...\"\r\n                        value={categoryFilter}\r\n                        onChange={(e) => setCategoryFilter(e.target.value)}\r\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 transition-all duration-200\"\r\n                      />\r\n                      {categoryFilter && (\r\n                        <button\r\n                          onClick={() => setCategoryFilter(\"\")}\r\n                          className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-n400 hover:text-n600 dark:hover:text-n300 transition-colors\"\r\n                        >\r\n                          <PiX />\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                    {uniqueCategories.length > 0 && (\r\n                      <div className=\"mt-2\">\r\n                        <p className=\"text-xs text-n500 dark:text-n400 mb-1\">Available Categories:</p>\r\n                        <div className=\"flex flex-wrap gap-1\">\r\n                          {uniqueCategories.map((category, index) => (\r\n                            <button\r\n                              key={index}\r\n                              onClick={() => setCategoryFilter(category)}\r\n                              className=\"text-xs px-2 py-1 bg-white dark:bg-n800 border border-gray-300 dark:border-n600 rounded text-n700 dark:text-n20 hover:bg-primaryColor hover:text-white hover:border-primaryColor transition-all duration-200\"\r\n                            >\r\n                              {category}\r\n                            </button>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Clear All Filters */}\r\n                {(urlFilter || categoryFilter) && (\r\n                  <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-n600\">\r\n                    <button\r\n                      onClick={() => {\r\n                        setUrlFilter(\"\");\r\n                        setCategoryFilter(\"\");\r\n                      }}\r\n                      className=\"text-sm text-primaryColor hover:bg-gray-50 dark:hover:bg-gray-800/10 px-2 py-1 rounded-md transition-colors flex items-center gap-1\"\r\n                    >\r\n                      <PiX className=\"text-xs\" />\r\n                      Clear all filters\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex gap-2 flex-wrap mt-4\">\r\n            {selectedRows.size > 0 && (\r\n              <button\r\n                onClick={() => setShowDeleteConfirm(true)}\r\n                className=\"px-4 py-2 bg-errorColor text-white rounded-lg hover:bg-errorColor/90 transition-all duration-200 flex items-center shadow-sm hover:shadow-md\"\r\n              >\r\n                <PiTrash className=\"mr-2\" />\r\n                Delete ({selectedRows.size})\r\n              </button>\r\n            )}\r\n\r\n            <button\r\n              onClick={handleExportCSV}\r\n              disabled={totalRows === 0}\r\n              className=\"px-4 py-2 bg-successColor text-white rounded-lg hover:bg-successColor/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-sm hover:shadow-md\"\r\n            >\r\n              <PiFileCsv className=\"mr-2\" />\r\n              Export CSV\r\n            </button>\r\n\r\n            <button\r\n              onClick={handleExportJSON}\r\n              disabled={totalRows === 0}\r\n              className=\"px-4 py-2 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-sm hover:shadow-md\"\r\n            >\r\n              <PiFileText className=\"mr-2\" />\r\n              Export JSON\r\n            </button>\r\n          </div>\r\n\r\n          {/* Results info */}\r\n          <div className=\"mt-4 flex flex-col sm:flex-row gap-2 sm:gap-0 justify-between items-start sm:items-center\">\r\n            <div className=\"text-sm text-n500 dark:text-n40\">\r\n              {(searchTerm || urlFilter || categoryFilter) ? (\r\n                <div className=\"space-y-1\">\r\n                  <div>Showing {filteredData.length} of {allFilteredData.length} filtered results (Page {currentPage})</div>\r\n                  <div className=\"flex flex-wrap gap-2 text-xs\">\r\n                    {searchTerm && (\r\n                      <span className=\"bg-primaryColor/10 text-primaryColor px-2 py-1 rounded-full\">\r\n                        Search: \"{searchTerm}\"\r\n                      </span>\r\n                    )}\r\n                    {urlFilter && (\r\n                      <span className=\"bg-infoColor/10 text-infoColor px-2 py-1 rounded-full\">\r\n                        URL: \"{urlFilter}\"\r\n                      </span>\r\n                    )}\r\n                    {categoryFilter && (\r\n                      <span className=\"bg-warningColor/10 text-warningColor px-2 py-1 rounded-full\">\r\n                        Category: \"{categoryFilter}\"\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <>\r\n                  Showing {((currentPage - 1) * pageSize) + 1}-{Math.min(currentPage * pageSize, totalRows)} of {totalRows} rows\r\n                </>\r\n              )}\r\n            </div>\r\n\r\n            {/* Pagination Controls */}\r\n            {(((searchTerm || urlFilter || categoryFilter) && Math.ceil(allFilteredData.length / pageSize) > 1) || (!(searchTerm || urlFilter || categoryFilter) && totalPages > 1)) && (\r\n              <div className=\"flex items-center gap-2\">\r\n                <button\r\n                  onClick={handlePrevPage}\r\n                  disabled={!hasPrevPage || loading}\r\n                  className={`px-3 py-2 text-sm rounded-lg border transition-colors flex items-center gap-1 ${\r\n                    !hasPrevPage || loading\r\n                      ? 'bg-gray-100 dark:bg-n700 text-n400 dark:text-n500 border-gray-200 dark:border-n600 cursor-not-allowed'\r\n                      : 'bg-white dark:bg-n800 text-n700 dark:text-n20 border-gray-300 dark:border-n600 hover:bg-gray-50 dark:hover:bg-gray-800/10'\r\n                  }`}\r\n                >\r\n                  <PiCaretLeft />\r\n                  Previous\r\n                </button>\r\n\r\n                <span className=\"text-sm text-n500 dark:text-n40 px-2\">\r\n                  Page {currentPage} of {(searchTerm || urlFilter || categoryFilter) ? Math.ceil(allFilteredData.length / pageSize) : totalPages}\r\n                </span>\r\n\r\n                <button\r\n                  onClick={handleNextPage}\r\n                  disabled={!hasNextPage || loading}\r\n                  className={`px-3 py-2 text-sm rounded-lg border transition-colors flex items-center gap-1 ${\r\n                    !hasNextPage || loading\r\n                      ? 'bg-gray-100 dark:bg-n700 text-n400 dark:text-n500 border-gray-200 dark:border-n600 cursor-not-allowed'\r\n                      : 'bg-white dark:bg-n800 text-n700 dark:text-n20 border-gray-300 dark:border-n600 hover:bg-gray-50 dark:hover:bg-gray-800/10'\r\n                  }`}\r\n                >\r\n                  Next\r\n                  <PiCaretRight />\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"flex-1 overflow-hidden\">\r\n          {loading ? (\r\n            <div className=\"flex items-center justify-center h-64\">\r\n              <div className=\"text-center\">\r\n                <PiSpinner className=\"animate-spin text-4xl text-primaryColor mx-auto mb-4\" />\r\n                <p className=\"text-n600 dark:text-n30 font-medium\">Loading data...</p>\r\n                <div className=\"mt-2 w-32 h-1 bg-gray-200 dark:bg-n600 rounded-full mx-auto overflow-hidden\">\r\n                  <div className=\"h-full bg-primaryColor rounded-full animate-pulse\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"flex items-center justify-center h-64\">\r\n              <div className=\"text-center max-w-md\">\r\n                <PiWarning className=\"text-4xl text-errorColor mx-auto mb-4\" />\r\n                <p className=\"text-errorColor dark:text-errorColor mb-4 font-medium\">{error}</p>\r\n                <button\r\n                  onClick={loadData}\r\n                  className=\"px-6 py-2 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 transition-all duration-200 shadow-sm hover:shadow-md\"\r\n                >\r\n                  Try Again\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : totalRows === 0 ? (\r\n            <div className=\"flex items-center justify-center h-64\">\r\n              <div className=\"text-center\">\r\n                <PiTable className=\"text-4xl text-n300 dark:text-n600 mx-auto mb-4\" />\r\n                <p className=\"text-n600 dark:text-n30 font-medium\">\r\n                  {(searchTerm || urlFilter || categoryFilter)\r\n                    ? 'No data matches your filters'\r\n                    : 'No data found for this index'\r\n                  }\r\n                </p>\r\n                {(searchTerm || urlFilter || categoryFilter) && (\r\n                  <button\r\n                    onClick={() => {\r\n                      setSearchTerm(\"\");\r\n                      setUrlFilter(\"\");\r\n                      setCategoryFilter(\"\");\r\n                    }}\r\n                    className=\"mt-3 text-sm text-primaryColor hover:bg-gray-50 dark:hover:bg-gray-800/10 px-3 py-2 rounded-md transition-colors flex items-center gap-1\"\r\n                  >\r\n                    <PiX className=\"text-xs\" />\r\n                    Clear all filters\r\n                  </button>\r\n                )}\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"overflow-auto h-full\">\r\n              <table className=\"w-full\">\r\n                <thead className=\"bg-gray-50 dark:bg-n700 sticky top-0 z-10 shadow-sm\">\r\n                  <tr>\r\n                    <th className=\"px-4 py-3 text-left border-b border-gray-200 dark:border-n600\">\r\n                      <button\r\n                        onClick={handleSelectAll}\r\n                        className=\"p-1 hover:bg-gray-200 dark:hover:bg-n600 rounded transition-colors duration-200\"\r\n                      >\r\n                        {selectedRows.size === filteredData.length && filteredData.length > 0 ? (\r\n                          <PiCheckSquare className=\"text-primaryColor\" />\r\n                        ) : (\r\n                          <PiSquare className=\"text-n400 dark:text-n500 hover:text-primaryColor transition-colors duration-200\" />\r\n                        )}\r\n                      </button>\r\n                    </th>\r\n                    {columns.map((column) => (\r\n                      <th\r\n                        key={column}\r\n                        className=\"px-4 py-3 text-left text-xs font-semibold text-n600 dark:text-n30 uppercase tracking-wider border-b border-gray-200 dark:border-n600\"\r\n                      >\r\n                        {column.replace(/_/g, ' ')}\r\n                      </th>\r\n                    ))}\r\n                  </tr>\r\n                </thead>\r\n                <tbody className=\"bg-white dark:bg-n800 divide-y divide-gray-200 dark:divide-n600\">\r\n                  {filteredData.map((row, index) => (\r\n                    <tr\r\n                      key={row.id}\r\n                      className={`${\r\n                        selectedRows.has(row.id)\r\n                          ? 'bg-primaryColor/5 dark:bg-primaryColor/10 border-l-2 border-primaryColor'\r\n                          : index % 2 === 0\r\n                            ? 'bg-white dark:bg-n800'\r\n                            : 'bg-gray-25 dark:bg-n800/50'\r\n                      }`}\r\n                    >\r\n                      <td className=\"px-4 py-3\">\r\n                        <button\r\n                          onClick={() => handleRowSelect(row.id)}\r\n                          className=\"p-1 hover:bg-gray-200 dark:hover:bg-n600 rounded transition-all duration-200 hover:scale-110\"\r\n                        >\r\n                          {selectedRows.has(row.id) ? (\r\n                            <PiCheckSquare className=\"text-primaryColor\" />\r\n                          ) : (\r\n                            <PiSquare className=\"text-n400 dark:text-n500 hover:text-primaryColor transition-colors duration-200\" />\r\n                          )}\r\n                        </button>\r\n                      </td>\r\n                      {columns.map((column) => (\r\n                        <td\r\n                          key={column}\r\n                          className=\"px-4 py-3 text-sm text-n700 dark:text-n20 max-w-xs truncate\"\r\n                          title={String(row[column] || '')}\r\n                        >\r\n                          <span className={`${\r\n                            column === 'url' ? 'text-infoColor hover:underline cursor-pointer' :\r\n                            column === 'category' ? 'text-warningColor font-medium' :\r\n                            column === 'title' ? 'font-medium text-n700 dark:text-n20' :\r\n                            'text-n700 dark:text-n20'\r\n                          }`}>\r\n                            {String(row[column] || '')}\r\n                          </span>\r\n                        </td>\r\n                      ))}\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Delete Confirmation Modal */}\r\n        {showDeleteConfirm && (\r\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm\">\r\n            <div className=\"bg-white dark:bg-n800 rounded-xl p-6 max-w-md w-full mx-4 shadow-2xl border border-gray-200 dark:border-n600\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"p-2 bg-errorColor/10 rounded-full mr-3\">\r\n                  <PiWarning className=\"text-errorColor text-2xl\" />\r\n                </div>\r\n                <h4 className=\"text-lg font-semibold text-n700 dark:text-n20\">\r\n                  Confirm Deletion\r\n                </h4>\r\n              </div>\r\n              <p className=\"text-n600 dark:text-n30 mb-6 leading-relaxed\">\r\n                Are you sure you want to delete <span className=\"font-semibold text-errorColor\">{selectedRows.size}</span> selected row{selectedRows.size !== 1 ? 's' : ''}?\r\n                <br />\r\n                <span className=\"text-sm text-n500 dark:text-n400\">This action cannot be undone.</span>\r\n              </p>\r\n              <div className=\"flex justify-end space-x-3\">\r\n                <button\r\n                  onClick={() => setShowDeleteConfirm(false)}\r\n                  disabled={isDeleting}\r\n                  className=\"px-6 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n700 rounded-lg hover:bg-gray-300 dark:hover:bg-n600 disabled:opacity-50 transition-all duration-200\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  onClick={handleDeleteSelected}\r\n                  disabled={isDeleting}\r\n                  className=\"px-6 py-2 text-sm font-medium text-white bg-errorColor hover:bg-errorColor/90 rounded-lg disabled:opacity-50 flex items-center transition-all duration-200 shadow-sm hover:shadow-md\"\r\n                >\r\n                  {isDeleting ? (\r\n                    <>\r\n                      <PiSpinner className=\"animate-spin mr-2\" />\r\n                      Deleting...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <PiTrash className=\"mr-2\" />\r\n                      Delete\r\n                    </>\r\n                  )}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EditDataModal;\r\n"], "names": [], "mappings": ";;;;AACA;AAkBA;AAjBA;AAFA;;;;;AA6CA,MAAM,gBAA8C,CAAC,EACnD,MAAM,EACN,OAAO,EACP,SAAS,EACV;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,gBAAgB;IAChB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,mBAAmB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,4CAA4C;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE,GAAG,mCAAmC;IAE/F,qEAAqE;IACrE,MAAM,kBAAkB,CAAC;QACvB,IAAI,WAAW;QAEf,2BAA2B;QAC3B,IAAI,WAAW,IAAI,IAAI;YACrB,WAAW,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD,EAAE,UAAU;QAClC;QAEA,mBAAmB;QACnB,IAAI,UAAU,IAAI,IAAI;YACpB,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,MAAM,MAAM,IAAI,GAAG,IAAI;gBACvB,OAAO,OAAO,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU,WAAW;YACjE;QACF;QAEA,wBAAwB;QACxB,IAAI,eAAe,IAAI,IAAI;YACzB,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,MAAM,WAAW,IAAI,QAAQ,IAAI;gBACjC,OAAO,OAAO,UAAU,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW;YAC3E;QACF;QAEA,OAAO;IACT;IAEA,sDAAsD;IACtD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,OAAO,gBAAgB;IACzB,GAAG;QAAC;QAAM;QAAY;QAAW;KAAe;IAEhD,0DAA0D;IAC1D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,OAAO,gBAAgB;IACzB,GAAG;QAAC;QAAS;QAAY;QAAW;KAAe;IAEnD,kEAAkE;IAClE,MAAM,aAAa,aACf,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG,YACnC,KAAK,IAAI,CAAC,YAAY;IAC1B,MAAM,cAAc,cAAc;IAClC,MAAM,cAAc,cAAc;IAElC,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,WAAW;YACvB;QACF;IACF,GAAG;QAAC;QAAQ;QAAW;KAAY;IAEnC,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,aAAa,gBAAgB;YAC7C,eAAe;QACjB;IACF,GAAG;QAAC;QAAY;QAAW;KAAe;IAE1C,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ;YACX,QAAQ,EAAE;YACV,WAAW,EAAE;YACb,cAAc;YACd,aAAa;YACb,kBAAkB;YAClB,eAAe;YACf,gBAAgB,IAAI;YACpB,SAAS;YACT,qBAAqB;YACrB,eAAe;YACf,aAAa;QACf;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,WAAW;QACf,IAAI,CAAC,WAAW;QAEhB,WAAW;QACX,SAAS;QAET,IAAI;YACF,kCAAkC;YAClC,MAAM,SAAS,CAAC,cAAc,CAAC,IAAI;YAEnC,8EAA8E;YAC9E,IAAI,cAAc,aAAa,gBAAgB;gBAC7C,8BAA8B;gBAC9B,MAAM,YAAY,MAAM,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,UAAU,EAAE,OAAO,IAAI,8BAA8B;gBAEtG,IAAI,UAAU,OAAO,EAAE;oBACrB,WAAW,UAAU,IAAI;oBACzB,MAAM,WAAW,gBAAgB,UAAU,IAAI;oBAE/C,wCAAwC;oBACxC,MAAM,gBAAgB,SAAS,KAAK,CAAC,QAAQ,SAAS;oBACtD,QAAQ;oBACR,aAAa,SAAS,MAAM;gBAC9B,OAAO;oBACL,SAAS,UAAU,KAAK,IAAI;gBAC9B;YACF,OAAO;gBACL,+BAA+B;gBAC/B,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,UAAU,EAAE,UAAU;gBAEpE,IAAI,OAAO,OAAO,EAAE;oBAClB,QAAQ,OAAO,IAAI;oBACnB,aAAa,OAAO,KAAK;oBAEzB,wEAAwE;oBACxE,IAAI,gBAAgB,GAAG;wBACrB,MAAM,YAAY,MAAM,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,UAAU,EAAE,OAAO;wBACpE,IAAI,UAAU,OAAO,EAAE;4BACrB,WAAW,UAAU,IAAI;wBAC3B;oBACF;gBACF,OAAO;oBACL,SAAS,OAAO,KAAK,IAAI;gBAC3B;YACF;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,kCAAkC;IAClC,MAAM,iBAAiB;QACrB,IAAI,aAAa;YACf,eAAe,CAAA,OAAQ,OAAO;QAChC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,aAAa;YACf,eAAe,CAAA,OAAQ,OAAO;QAChC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,QAAQ;YAC1B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,IAAI,aAAa,IAAI,KAAK,aAAa,MAAM,EAAE;YAC7C,gBAAgB,IAAI;QACtB,OAAO;YACL,gBAAgB,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;QACxD;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,aAAa,aAAa,IAAI,KAAK,GAAG;QAE3C,cAAc;QAEd,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,UAAU,UAAU,EAAE,MAAM,IAAI,CAAC;YAEtE,IAAI,OAAO,OAAO,EAAE;gBAClB,uCAAuC;gBACvC,QAAQ,CAAA,WAAY,SAAS,MAAM,CAAC,CAAA,MAAO,CAAC,aAAa,GAAG,CAAC,IAAI,EAAE;gBACnE,gBAAgB,IAAI;gBACpB,qBAAqB;YACvB,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,eAAe,aAAa,kBAAkB;QACpD,MAAM,WAAW,GAAG,WAAW,cAAc,QAAQ,MAAM,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACzG,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE,cAAc;IAC5B;IAEA,MAAM,mBAAmB;QACvB,MAAM,eAAe,aAAa,kBAAkB;QACpD,MAAM,WAAW,GAAG,WAAW,cAAc,QAAQ,MAAM,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;QAC1G,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE,cAAc;IAC7B;IAEA,gCAAgC;IAChC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,OAAO,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,OAAO,KAAK,CAAC,GAAG,KAAK,8BAA8B;IAC3F,GAAG;QAAC;KAAQ;IAEZ,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,OAAO,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,YAAY,KAAK,CAAC,GAAG,KAAK,8BAA8B;IAChG,GAAG;QAAC;KAAQ;IAEZ,8EAA8E;IAC9E,8EAA8E;IAC9E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACtB,IAAI,KAAK,MAAM,GAAG,GAAG,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;QAC/C,IAAI,QAAQ,MAAM,GAAG,GAAG,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;QACrD,OAAO,EAAE;IACX,GAAG;QAAC;QAAM;KAAQ;IAElB,IAAI,CAAC,UAAU,CAAC,WAAW,OAAO;IAElC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAAgD;gDAChD,UAAU,UAAU;;;;;;;sDAElC,8OAAC;4CAAE,WAAU;sDACV,UAAU,KAAK;;;;;;;;;;;;;;;;;;sCAItB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8IAAA,CAAA,oBAAiB;oDAAC,WAAU;;;;;;8DAC7B,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;sDAKd,8OAAC;4CACC,SAAS,IAAM,eAAe,CAAC;4CAC/B,WAAW,CAAC,gFAAgF,EAC1F,eAAe,aAAa,iBACxB,6DACA,sHACJ;;8DAEF,8OAAC,8IAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;gDAE/B,CAAC,aAAa,cAAc,mBAC3B,8OAAC;oDAAK,WAAU;8DACb;wDAAC;wDAAW;qDAAe,CAAC,MAAM,CAAC,SAAS,MAAM;;;;;;;;;;;;;;;;;;gCAO1D,6BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;;8EACf,8OAAC,8IAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAkC;;;;;;;sEAGvD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,MAAK;oEACL,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oEAC5C,WAAU;;;;;;gEAEX,2BACC,8OAAC;oEACC,SAAS,IAAM,aAAa;oEAC5B,WAAU;8EAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;;;;;;;;;;;;;;;;wDAIT,WAAW,MAAM,GAAG,mBACnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAwC;;;;;;8EACrD,8OAAC;oEAAI,WAAU;8EACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAChC,8OAAC;4EAEC,SAAS,IAAM,aAAa;4EAC5B,WAAU;4EACV,OAAO;sFAEN,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;2EAL7C;;;;;;;;;;;;;;;;;;;;;;8DAcjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;;8EACf,8OAAC,8IAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAkC;;;;;;;sEAGrD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,MAAK;oEACL,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oEACjD,WAAU;;;;;;gEAEX,gCACC,8OAAC;oEACC,SAAS,IAAM,kBAAkB;oEACjC,WAAU;8EAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;;;;;;;;;;;;;;;;wDAIT,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAwC;;;;;;8EACrD,8OAAC;oEAAI,WAAU;8EACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,8OAAC;4EAEC,SAAS,IAAM,kBAAkB;4EACjC,WAAU;sFAET;2EAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAclB,CAAC,aAAa,cAAc,mBAC3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,SAAS;oDACP,aAAa;oDACb,kBAAkB;gDACpB;gDACA,WAAU;;kEAEV,8OAAC,8IAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAUvC,8OAAC;4BAAI,WAAU;;gCACZ,aAAa,IAAI,GAAG,mBACnB,8OAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;;sDAEV,8OAAC,8IAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAS;wCACnB,aAAa,IAAI;wCAAC;;;;;;;8CAI/B,8OAAC;oCACC,SAAS;oCACT,UAAU,cAAc;oCACxB,WAAU;;sDAEV,8OAAC,8IAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAS;;;;;;;8CAIhC,8OAAC;oCACC,SAAS;oCACT,UAAU,cAAc;oCACxB,WAAU;;sDAEV,8OAAC,8IAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAS;;;;;;;;;;;;;sCAMnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,AAAC,cAAc,aAAa,+BAC3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAI;oDAAS,aAAa,MAAM;oDAAC;oDAAK,gBAAgB,MAAM;oDAAC;oDAAyB;oDAAY;;;;;;;0DACnG,8OAAC;gDAAI,WAAU;;oDACZ,4BACC,8OAAC;wDAAK,WAAU;;4DAA8D;4DAClE;4DAAW;;;;;;;oDAGxB,2BACC,8OAAC;wDAAK,WAAU;;4DAAwD;4DAC/D;4DAAU;;;;;;;oDAGpB,gCACC,8OAAC;wDAAK,WAAU;;4DAA8D;4DAChE;4DAAe;;;;;;;;;;;;;;;;;;6DAMnC;;4CAAE;4CACU,CAAC,cAAc,CAAC,IAAI,WAAY;4CAAE;4CAAE,KAAK,GAAG,CAAC,cAAc,UAAU;4CAAW;4CAAK;4CAAU;;;;;;;;gCAM9G,CAAC,AAAC,CAAC,cAAc,aAAa,cAAc,KAAK,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG,YAAY,KAAO,CAAC,CAAC,cAAc,aAAa,cAAc,KAAK,aAAa,CAAE,mBACrK,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,UAAU,CAAC,eAAe;4CAC1B,WAAW,CAAC,8EAA8E,EACxF,CAAC,eAAe,UACZ,0GACA,6HACJ;;8DAEF,8OAAC,8IAAA,CAAA,cAAW;;;;;gDAAG;;;;;;;sDAIjB,8OAAC;4CAAK,WAAU;;gDAAuC;gDAC/C;gDAAY;gDAAM,cAAc,aAAa,iBAAkB,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG,YAAY;;;;;;;sDAGtH,8OAAC;4CACC,SAAS;4CACT,UAAU,CAAC,eAAe;4CAC1B,WAAW,CAAC,8EAA8E,EACxF,CAAC,eAAe,UACZ,0GACA,6HACJ;;gDACH;8DAEC,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQvB,8OAAC;oBAAI,WAAU;8BACZ,wBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAE,WAAU;8CAAsC;;;;;;8CACnD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;+BAInB,sBACF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAE,WAAU;8CAAyD;;;;;;8CACtE,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;+BAKH,cAAc,kBAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAE,WAAU;8CACV,AAAC,cAAc,aAAa,iBACzB,iCACA;;;;;;gCAGL,CAAC,cAAc,aAAa,cAAc,mBACzC,8OAAC;oCACC,SAAS;wCACP,cAAc;wCACd,aAAa;wCACb,kBAAkB;oCACpB;oCACA,WAAU;;sDAEV,8OAAC,8IAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;6CAOnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDACC,SAAS;oDACT,WAAU;8DAET,aAAa,IAAI,KAAK,aAAa,MAAM,IAAI,aAAa,MAAM,GAAG,kBAClE,8OAAC,8IAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;6EAEzB,8OAAC,8IAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;4CAIzB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oDAEC,WAAU;8DAET,OAAO,OAAO,CAAC,MAAM;mDAHjB;;;;;;;;;;;;;;;;8CAQb,8OAAC;oCAAM,WAAU;8CACd,aAAa,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC;4CAEC,WAAW,GACT,aAAa,GAAG,CAAC,IAAI,EAAE,IACnB,6EACA,QAAQ,MAAM,IACZ,0BACA,8BACN;;8DAEF,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDACC,SAAS,IAAM,gBAAgB,IAAI,EAAE;wDACrC,WAAU;kEAET,aAAa,GAAG,CAAC,IAAI,EAAE,kBACtB,8OAAC,8IAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;iFAEzB,8OAAC,8IAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;gDAIzB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wDAEC,WAAU;wDACV,OAAO,OAAO,GAAG,CAAC,OAAO,IAAI;kEAE7B,cAAA,8OAAC;4DAAK,WAAW,GACf,WAAW,QAAQ,kDACnB,WAAW,aAAa,kCACxB,WAAW,UAAU,wCACrB,2BACA;sEACC,OAAO,GAAG,CAAC,OAAO,IAAI;;;;;;uDAVpB;;;;;;2CAvBJ,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;gBA8CxB,mCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;;;;;;;0CAIhE,8OAAC;gCAAE,WAAU;;oCAA+C;kDAC1B,8OAAC;wCAAK,WAAU;kDAAiC,aAAa,IAAI;;;;;;oCAAQ;oCAAc,aAAa,IAAI,KAAK,IAAI,MAAM;oCAAG;kDAC3J,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;0CAErD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,UAAU;wCACV,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,2BACC;;8DACE,8OAAC,8IAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAsB;;yEAI7C;;8DACE,8OAAC,8IAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlD;uCAEe"}}, {"offset": {"line": 2357, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2363, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/showdeleteindex.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect, useMemo } from \"react\";\r\nimport {\r\n  PiTrash,\r\n  PiDatabase,\r\n  PiEnvelope,\r\n  PiWarning,\r\n  PiList,\r\n  PiMagnifyingGlass,\r\n  PiX,\r\n  PiCheck,\r\n  PiCheckSquare,\r\n  PiSquare,\r\n  PiCheckCircle,\r\n  PiInfo,\r\n  PiTag,\r\n  PiShieldCheck,\r\n  PiArrowsClockwise,\r\n  PiCursor,\r\n  PiHandPointing,\r\n  PiPencil,\r\n  PiTable,\r\n  PiDownloadSimple,\r\n  PiSignOut,\r\n  PiUserCircle\r\n} from \"react-icons/pi\";\r\nimport AdminSidebar from './adminsidebar';\r\nimport { deleteFaissIndex } from '../services/api';\r\nimport { getIndexesByEmail, fetchEmails } from '../services/fileUploadService';\r\n// @ts-ignore\r\nimport EditDataModal from './modals/EditDataModal';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface IndexData {\r\n  _id: string;\r\n  email: string;\r\n  index_name: string;\r\n  embed_model?: string;\r\n  api_key?: string;\r\n  source: 'FAISS' | 'PINE';\r\n  [key: string]: any;\r\n}\r\n\r\ninterface BulkDeleteProgress {\r\n  [itemId: string]: {\r\n    status: 'pending' | 'deleting' | 'success' | 'error';\r\n    message?: string;\r\n  };\r\n}\r\n\r\ninterface DeleteModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  itemData: IndexData | null;\r\n  selectedItems?: IndexData[];\r\n  isDeleting: boolean;\r\n  isBulkDelete?: boolean;\r\n  bulkProgress?: BulkDeleteProgress;\r\n}\r\n\r\nconst DeleteConfirmationModal: React.FC<DeleteModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n  itemData,\r\n  selectedItems = [],\r\n  isDeleting,\r\n  isBulkDelete = false,\r\n  bulkProgress = {}\r\n}) => {\r\n  if (!isOpen || (!itemData && !isBulkDelete)) return null;\r\n\r\n  const isMultiple = isBulkDelete && selectedItems.length > 0;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white dark:bg-n800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <PiWarning className=\"text-red-500 text-2xl mr-3\" />\r\n          <h3 className=\"text-lg font-semibold text-n700 dark:text-n20\">\r\n            {isMultiple ? `Confirm Bulk Deletion (${selectedItems.length} items)` : 'Confirm Deletion'}\r\n          </h3>\r\n        </div>\r\n\r\n        <div className=\"mb-6\">\r\n          <p className=\"text-n600 dark:text-n30 mb-4\">\r\n            Are you sure you want to delete {isMultiple ? 'these indexes' : 'this index'}? This action will:\r\n          </p>\r\n          <ul className=\"list-disc list-inside text-sm text-n500 dark:text-n40 space-y-1\">\r\n            <li>Remove the record{isMultiple ? 's' : ''} from PINE collection database</li>\r\n            <li>Delete the entire FAISS index director{isMultiple ? 'ies' : 'y'} and all files</li>\r\n            <li>Permanently remove all vector embeddings and metadata</li>\r\n            <li>This action cannot be undone</li>\r\n          </ul>\r\n\r\n          {/* Single item details */}\r\n          {!isMultiple && itemData && (\r\n            <div className=\"mt-4 p-3 bg-gray-50 dark:bg-n700 rounded-md\">\r\n              <p className=\"text-sm font-medium text-n700 dark:text-n20\">\r\n                Category: {itemData.index_name}\r\n              </p>\r\n              <p className=\"text-sm text-n500 dark:text-n40\">\r\n                Email: {itemData.email}\r\n              </p>\r\n              <p className=\"text-sm text-n500 dark:text-n40\">\r\n                Model: {itemData.embed_model || 'N/A'}\r\n              </p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Multiple items list */}\r\n          {isMultiple && (\r\n            <div className=\"mt-4 space-y-2 max-h-60 overflow-y-auto\">\r\n              {selectedItems.map((item) => {\r\n                const progress = bulkProgress[item._id];\r\n                return (\r\n                  <div key={item._id} className=\"p-3 bg-gray-50 dark:bg-n700 rounded-md\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex-1\">\r\n                        <p className=\"text-sm font-medium text-n700 dark:text-n20\">\r\n                          {item.index_name}\r\n                        </p>\r\n                        <p className=\"text-xs text-n500 dark:text-n40\">\r\n                          {item.email}\r\n                        </p>\r\n                      </div>\r\n                      {progress && (\r\n                        <div className=\"flex items-center ml-2\">\r\n                          {progress.status === 'pending' && (\r\n                            <span className=\"text-xs text-gray-500\">Pending</span>\r\n                          )}\r\n                          {progress.status === 'deleting' && (\r\n                            <div className=\"flex items-center\">\r\n                              <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500 mr-1\"></div>\r\n                              <span className=\"text-xs text-blue-600\">Deleting...</span>\r\n                            </div>\r\n                          )}\r\n                          {progress.status === 'success' && (\r\n                            <div className=\"flex items-center\">\r\n                              <PiCheck className=\"text-green-500 text-sm mr-1\" />\r\n                              <span className=\"text-xs text-green-600\">Success</span>\r\n                            </div>\r\n                          )}\r\n                          {progress.status === 'error' && (\r\n                            <div className=\"flex items-center\">\r\n                              <PiX className=\"text-red-500 text-sm mr-1\" />\r\n                              <span className=\"text-xs text-red-600\">Error</span>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    {progress?.message && (\r\n                      <p className=\"text-xs text-n500 dark:text-n40 mt-1\">\r\n                        {progress.message}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex justify-end space-x-3\">\r\n          <button\r\n            onClick={onClose}\r\n            disabled={isDeleting}\r\n            className=\"px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n700 rounded-md hover:bg-gray-300 dark:hover:bg-n600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            {isDeleting ? 'Close' : 'Cancel'}\r\n          </button>\r\n          {!isDeleting && (\r\n            <button\r\n              onClick={onConfirm}\r\n              className=\"px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md flex items-center\"\r\n            >\r\n              <PiTrash className=\"mr-2\" />\r\n              Delete {isMultiple ? `${selectedItems.length} Items` : 'Item'}\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Logout Confirmation Modal Component\r\nconst LogoutConfirmationModal: React.FC<{\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  isProcessing: boolean;\r\n  userName: string;\r\n}> = ({ isOpen, onClose, onConfirm, isProcessing, userName }) => {\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white dark:bg-n800 rounded-lg p-6 max-w-md w-full mx-4\">\r\n        <div className=\"flex items-center mb-4\">\r\n          <PiSignOut className=\"text-red-500 text-2xl mr-3\" />\r\n          <h3 className=\"text-lg font-semibold text-n700 dark:text-n20\">\r\n            Confirm Logout\r\n          </h3>\r\n        </div>\r\n\r\n        <div className=\"mb-6\">\r\n          <p className=\"text-n600 dark:text-n30 mb-2\">\r\n            Are you sure you want to log out, {userName}?\r\n          </p>\r\n          <p className=\"text-sm text-n500 dark:text-n40\">\r\n            You will be redirected to the sign-in page and will need to log in again to access your data.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"flex justify-end space-x-3\">\r\n          <button\r\n            onClick={onClose}\r\n            disabled={isProcessing}\r\n            className=\"px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n700 rounded-md hover:bg-gray-300 dark:hover:bg-n600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button\r\n            onClick={onConfirm}\r\n            disabled={isProcessing}\r\n            className=\"px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md flex items-center disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            {isProcessing ? (\r\n              <>\r\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                Logging out...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <PiSignOut className=\"mr-2\" />\r\n                Log Out\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ShowDeleteIndex: React.FC = () => {\r\n  const router = useRouter();\r\n  const [indexData, setIndexData] = useState<IndexData[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [deleteModal, setDeleteModal] = useState({\r\n    isOpen: false,\r\n    item: null as IndexData | null\r\n  });\r\n  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState<string>(\"\");\r\n  const [emails, setEmails] = useState<string[]>([]);\r\n  const [selectedEmail, setSelectedEmail] = useState<string>(\"\");\r\n  \r\n  // User authentication state\r\n  const [currentUser, setCurrentUser] = useState<{\r\n    name: string;\r\n    email: string;\r\n  } | null>(null);\r\n  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);\r\n  const [isLogoutProcessing, setIsLogoutProcessing] = useState(false);\r\n\r\n  // Multi-select state\r\n  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());\r\n  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);\r\n  const [bulkDeleteModal, setBulkDeleteModal] = useState({\r\n    isOpen: false,\r\n    items: [] as IndexData[]\r\n  });\r\n  const [bulkDeleteProgress, setBulkDeleteProgress] = useState<BulkDeleteProgress>({});\r\n  const [isBulkDeleting, setIsBulkDeleting] = useState(false);\r\n\r\n  // Edit Data Modal state\r\n  const [editDataModal, setEditDataModal] = useState({\r\n    isOpen: false,\r\n    indexData: null as IndexData | null\r\n  });\r\n\r\n  // Notification state\r\n  const [notification, setNotification] = useState<{\r\n    type: 'success' | 'error' | 'info';\r\n    message: string;\r\n    isVisible: boolean;\r\n  }>({\r\n    type: 'info',\r\n    message: '',\r\n    isVisible: false\r\n  });\r\n\r\n  // Notification helper function\r\n  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {\r\n    setNotification({ type, message, isVisible: true });\r\n    setTimeout(() => {\r\n      setNotification(prev => ({ ...prev, isVisible: false }));\r\n    }, 5000);\r\n  };\r\n\r\n  // Initialize user data from localStorage\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      const userEmail = localStorage.getItem('user_email');\r\n      const sessionData = sessionStorage.getItem('resultUser');\r\n      \r\n      if (userEmail) {\r\n        let userName = 'User';\r\n        \r\n        // Try to get user name from session storage\r\n        if (sessionData) {\r\n          try {\r\n            const userData = JSON.parse(sessionData);\r\n            userName = userData.name || 'User';\r\n          } catch (error) {\r\n            console.error('Error parsing user session data:', error);\r\n          }\r\n        }\r\n        \r\n        setCurrentUser({\r\n          name: userName,\r\n          email: userEmail\r\n        });\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Handle logout functionality\r\n  const handleLogout = () => {\r\n    if (isLogoutProcessing) {\r\n      console.log(\"Logout already in progress, ignoring...\");\r\n      return;\r\n    }\r\n\r\n    setIsLogoutProcessing(true);\r\n    console.log(\"Logout initiated from Index Management\");\r\n\r\n    try {\r\n      // Clear all storage\r\n      console.log(\"Clearing all storage...\");\r\n      sessionStorage.clear();\r\n      localStorage.clear();\r\n\r\n      // Close any open modals\r\n      setShowLogoutConfirm(false);\r\n\r\n      // Show logout notification\r\n      showNotification('info', 'Logging out...');\r\n\r\n      // Force redirect to sign-in page\r\n      console.log(\"Redirecting to sign-in page...\");\r\n      setTimeout(() => {\r\n        window.location.href = \"/sign-in\";\r\n      }, 100);\r\n      \r\n    } catch (error) {\r\n      console.error(\"Error during logout:\", error);\r\n      // Even if there's an error, try to redirect\r\n      setTimeout(() => {\r\n        window.location.href = \"/sign-in\";\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n  // Handle logout confirmation\r\n  const handleLogoutClick = () => {\r\n    setShowLogoutConfirm(true);\r\n  };\r\n\r\n  // Filter data based on search term\r\n  const filteredData = useMemo(() => {\r\n    if (!searchTerm.trim()) {\r\n      return indexData;\r\n    }\r\n\r\n    const searchLower = searchTerm.toLowerCase().trim();\r\n    return indexData.filter((item: IndexData) => {\r\n      return (\r\n        item.email.toLowerCase().includes(searchLower) ||\r\n        item.index_name.toLowerCase().includes(searchLower) ||\r\n        (item.embed_model && item.embed_model.toLowerCase().includes(searchLower))\r\n      );\r\n    });\r\n  }, [indexData, searchTerm]);\r\n\r\n  // Clear search\r\n  const clearSearch = () => {\r\n    setSearchTerm(\"\");\r\n    showNotification('info', 'Search cleared');\r\n  };\r\n\r\n  // Fetch emails and index data\r\n  const fetchIndexData = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      // First, fetch all emails\r\n      const emailList = await fetchEmails();\r\n      setEmails(emailList);\r\n\r\n      let allIndexData: IndexData[] = [];\r\n\r\n      // If a specific email is selected, fetch indexes for that email\r\n      if (selectedEmail) {\r\n        console.log(`Fetching indexes for selected email: ${selectedEmail}`);\r\n        const emailIndexes = await getIndexesByEmail(selectedEmail);\r\n        allIndexData = [...allIndexData, ...emailIndexes];\r\n      } else {\r\n        // If no email is selected, fetch indexes for all emails\r\n        console.log('Fetching indexes for all emails...');\r\n        for (const email of emailList) {\r\n          try {\r\n            const emailIndexes = await getIndexesByEmail(email);\r\n            allIndexData = [...allIndexData, ...emailIndexes];\r\n          } catch (error) {\r\n            console.error(`Error fetching indexes for email ${email}:`, error);\r\n            // Continue with other emails even if one fails\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log('All fetched index data:', allIndexData);\r\n      setIndexData(allIndexData);\r\n\r\n      // Show success notification if data was fetched successfully\r\n      if (allIndexData.length > 0) {\r\n        showNotification('success', `Successfully loaded ${allIndexData.length} indexes`);\r\n      } else {\r\n        showNotification('info', 'No indexes found');\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching index data:', err);\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch data';\r\n      setError(errorMessage);\r\n      showNotification('error', `Failed to load data: ${errorMessage}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Delete function - deletes both from PINE collection and FAISS directory\r\n  const handleDelete = async (item: IndexData) => {\r\n    try {\r\n      setDeleteLoading(item._id);\r\n\r\n      // Use the original _id format if it exists, otherwise use the processed _id\r\n      const resourceId = item._id?.includes('$oid') ? item._id : item._id;\r\n      console.log('Deleting item with resourceId:', resourceId);\r\n      console.log('Full item data:', item);\r\n\r\n      let pineDeleteSuccess = false;\r\n      let faissDirectoryDeleteSuccess = false;\r\n      let pineError = null;\r\n      let faissDirectoryError = null;\r\n\r\n      // Step 1: Delete from PINE collection\r\n      try {\r\n        console.log('Step 1: Deleting from PINE collection...');\r\n        const pineResponse = await fetch(\r\n          `https://dev-commonmannit.mannit.co/mannit/eDeleteWCol?resourceId=${resourceId}&ColName=PINE`,\r\n          {\r\n            method: 'DELETE',\r\n            headers: {\r\n              'Accept': 'application/json',\r\n              'Content-Type': 'application/json',\r\n              'xxxid': 'PINE'\r\n            }\r\n          }\r\n        );\r\n\r\n        if (pineResponse.ok) {\r\n          console.log('PINE collection entry deleted successfully');\r\n          pineDeleteSuccess = true;\r\n        } else {\r\n          const errorText = await pineResponse.text();\r\n          console.error('PINE collection delete API error:', errorText);\r\n          pineError = `PINE collection deletion failed: ${pineResponse.status} - ${errorText}`;\r\n        }\r\n      } catch (err) {\r\n        console.error('Error deleting from PINE collection:', err);\r\n        pineError = `PINE collection deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;\r\n      }\r\n\r\n      // Step 2: Delete FAISS directory (only if we have an index_name)\r\n      if (item.index_name && item.index_name !== 'N/A') {\r\n        try {\r\n          console.log(`Step 2: Deleting FAISS directory for index: ${item.index_name}...`);\r\n          const faissResult = await deleteFaissIndex(item.index_name);\r\n          console.log('FAISS directory delete response:', faissResult);\r\n\r\n          if (faissResult.success) {\r\n            faissDirectoryDeleteSuccess = true;\r\n          } else {\r\n            faissDirectoryError = `FAISS directory deletion failed: ${faissResult.error}`;\r\n          }\r\n        } catch (err) {\r\n          console.error('Error deleting FAISS directory:', err);\r\n          faissDirectoryError = `FAISS directory deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;\r\n        }\r\n      } else {\r\n        console.log('Step 2: Skipping FAISS directory deletion - no valid index name');\r\n        faissDirectoryDeleteSuccess = true; // Consider it successful if there's nothing to delete\r\n      }\r\n\r\n      // Determine overall success and handle results\r\n      const overallSuccess = pineDeleteSuccess && faissDirectoryDeleteSuccess;\r\n\r\n      if (overallSuccess) {\r\n        console.log('✅ Successfully deleted both PINE collection entry and FAISS directory');\r\n        // Remove item from local state\r\n        setIndexData(prevData => prevData.filter(dataItem => dataItem._id !== item._id));\r\n        // Close modal\r\n        setDeleteModal({ isOpen: false, item: null });\r\n        // Show success notification\r\n        showNotification('success', `Successfully deleted index \"${item.index_name}\"`);\r\n      } else {\r\n        // Partial or complete failure\r\n        let errorMessage = 'Deletion completed with issues:\\n';\r\n\r\n        if (pineDeleteSuccess) {\r\n          errorMessage += '✅ PINE collection entry deleted successfully\\n';\r\n        } else {\r\n          errorMessage += `❌ PINE collection deletion failed: ${pineError}\\n`;\r\n        }\r\n\r\n        if (faissDirectoryDeleteSuccess) {\r\n          errorMessage += '✅ FAISS directory deleted successfully';\r\n        } else {\r\n          errorMessage += `❌ FAISS directory deletion failed: ${faissDirectoryError}`;\r\n        }\r\n\r\n        console.error('Partial deletion result:', errorMessage);\r\n\r\n        // If PINE collection was deleted successfully, still remove from UI\r\n        if (pineDeleteSuccess) {\r\n          setIndexData(prevData => prevData.filter(dataItem => dataItem._id !== item._id));\r\n          setDeleteModal({ isOpen: false, item: null });\r\n        }\r\n\r\n        // Show error to user\r\n        setError(errorMessage);\r\n        showNotification('error', 'Deletion completed with some issues. Check the details above.');\r\n      }\r\n\r\n    } catch (err) {\r\n      console.error('Error in delete process:', err);\r\n      setError(err instanceof Error ? err.message : 'Failed to delete item');\r\n    } finally {\r\n      setDeleteLoading(null);\r\n    }\r\n  };\r\n\r\n  // Load data on component mount and when selectedEmail changes\r\n  useEffect(() => {\r\n    fetchIndexData();\r\n  }, [selectedEmail]);\r\n\r\n  // Clear selections when data changes\r\n  useEffect(() => {\r\n    setSelectedItems(new Set());\r\n  }, [indexData]);\r\n\r\n  // Listen for FAISS index updates\r\n  useEffect(() => {\r\n    const handleFaissIndexUpdate = () => {\r\n      console.log('FAISS index updated, refreshing data...');\r\n      fetchIndexData();\r\n    };\r\n\r\n    window.addEventListener('faissIndexUpdated', handleFaissIndexUpdate);\r\n\r\n    return () => {\r\n      window.removeEventListener('faissIndexUpdated', handleFaissIndexUpdate);\r\n    };\r\n  }, []);\r\n\r\n  const openDeleteModal = (item: IndexData) => {\r\n    setDeleteModal({ isOpen: true, item });\r\n  };\r\n\r\n  const closeDeleteModal = () => {\r\n    setDeleteModal({ isOpen: false, item: null });\r\n  };\r\n\r\n  const confirmDelete = () => {\r\n    if (deleteModal.item) {\r\n      handleDelete(deleteModal.item);\r\n    }\r\n  };\r\n\r\n  const toggleSidebar = () => {\r\n    setSidebarOpen(!sidebarOpen);\r\n  };\r\n\r\n  // Edit Data Modal functions\r\n  const openEditDataModal = (item: IndexData) => {\r\n    setEditDataModal({ isOpen: true, indexData: item });\r\n    showNotification('info', `Loading data for index \"${item.index_name}\"`);\r\n  };\r\n\r\n  const closeEditDataModal = () => {\r\n    setEditDataModal({ isOpen: false, indexData: null });\r\n  };\r\n\r\n  // Multi-select functions\r\n  const toggleMultiSelectMode = () => {\r\n    const newMode = !isMultiSelectMode;\r\n    setIsMultiSelectMode(newMode);\r\n    setSelectedItems(new Set());\r\n\r\n    if (newMode) {\r\n      showNotification('info', 'Multi-select mode enabled. Click checkboxes to select items for bulk operations.');\r\n    } else {\r\n      showNotification('info', 'Multi-select mode disabled.');\r\n    }\r\n  };\r\n\r\n  const toggleItemSelection = (itemId: string) => {\r\n    const newSelected = new Set(selectedItems);\r\n    if (newSelected.has(itemId)) {\r\n      newSelected.delete(itemId);\r\n    } else {\r\n      newSelected.add(itemId);\r\n    }\r\n    setSelectedItems(newSelected);\r\n  };\r\n\r\n  const selectAllItems = () => {\r\n    const allIds = new Set(filteredData.map(item => item._id));\r\n    setSelectedItems(allIds);\r\n    showNotification('info', `Selected all ${filteredData.length} visible items`);\r\n  };\r\n\r\n  const deselectAllItems = () => {\r\n    setSelectedItems(new Set());\r\n    showNotification('info', 'Deselected all items');\r\n  };\r\n\r\n  const openBulkDeleteModal = () => {\r\n    const itemsToDelete = filteredData.filter(item => selectedItems.has(item._id));\r\n    setBulkDeleteModal({ isOpen: true, items: itemsToDelete });\r\n  };\r\n\r\n  const closeBulkDeleteModal = () => {\r\n    setBulkDeleteModal({ isOpen: false, items: [] });\r\n    setBulkDeleteProgress({});\r\n  };\r\n\r\n  // Bulk delete function\r\n  const handleBulkDelete = async () => {\r\n    if (bulkDeleteModal.items.length === 0) return;\r\n\r\n    setIsBulkDeleting(true);\r\n\r\n    // Initialize progress for all items\r\n    const initialProgress: BulkDeleteProgress = {};\r\n    bulkDeleteModal.items.forEach(item => {\r\n      initialProgress[item._id] = { status: 'pending' };\r\n    });\r\n    setBulkDeleteProgress(initialProgress);\r\n\r\n    const deletedItems: string[] = [];\r\n\r\n    // Process each item sequentially to avoid overwhelming the server\r\n    for (const item of bulkDeleteModal.items) {\r\n      try {\r\n        // Update status to deleting\r\n        setBulkDeleteProgress(prev => ({\r\n          ...prev,\r\n          [item._id]: { status: 'deleting', message: 'Deleting...' }\r\n        }));\r\n\r\n        // Use the same delete logic as single item delete\r\n        const resourceId = item._id?.includes('$oid') ? item._id : item._id;\r\n\r\n        let pineDeleteSuccess = false;\r\n        let faissDirectoryDeleteSuccess = false;\r\n        let pineError = null;\r\n        let faissDirectoryError = null;\r\n\r\n        // Step 1: Delete from PINE collection\r\n        try {\r\n          const pineResponse = await fetch(\r\n            `https://dev-commonmannit.mannit.co/mannit/eDeleteWCol?resourceId=${resourceId}&ColName=PINE`,\r\n            {\r\n              method: 'DELETE',\r\n              headers: {\r\n                'Accept': 'application/json',\r\n                'Content-Type': 'application/json',\r\n                'xxxid': 'PINE'\r\n              }\r\n            }\r\n          );\r\n\r\n          if (pineResponse.ok) {\r\n            pineDeleteSuccess = true;\r\n          } else {\r\n            const errorText = await pineResponse.text();\r\n            pineError = `PINE deletion failed: ${pineResponse.status} - ${errorText}`;\r\n          }\r\n        } catch (err) {\r\n          pineError = `PINE deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;\r\n        }\r\n\r\n        // Step 2: Delete FAISS directory\r\n        if (item.index_name && item.index_name !== 'N/A') {\r\n          try {\r\n            const faissResult = await deleteFaissIndex(item.index_name);\r\n            if (faissResult.success) {\r\n              faissDirectoryDeleteSuccess = true;\r\n            } else {\r\n              faissDirectoryError = `FAISS deletion failed: ${faissResult.error}`;\r\n            }\r\n          } catch (err) {\r\n            faissDirectoryError = `FAISS deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;\r\n          }\r\n        } else {\r\n          faissDirectoryDeleteSuccess = true;\r\n        }\r\n\r\n        // Update progress based on results\r\n        const overallSuccess = pineDeleteSuccess && faissDirectoryDeleteSuccess;\r\n\r\n        if (overallSuccess) {\r\n          setBulkDeleteProgress(prev => ({\r\n            ...prev,\r\n            [item._id]: { status: 'success', message: 'Successfully deleted' }\r\n          }));\r\n          deletedItems.push(item._id);\r\n        } else {\r\n          let errorMessage = '';\r\n          if (!pineDeleteSuccess) errorMessage += pineError + ' ';\r\n          if (!faissDirectoryDeleteSuccess) errorMessage += faissDirectoryError;\r\n\r\n          setBulkDeleteProgress(prev => ({\r\n            ...prev,\r\n            [item._id]: { status: 'error', message: errorMessage.trim() }\r\n          }));\r\n        }\r\n\r\n      } catch (err) {\r\n        setBulkDeleteProgress(prev => ({\r\n          ...prev,\r\n          [item._id]: {\r\n            status: 'error',\r\n            message: err instanceof Error ? err.message : 'Unknown error'\r\n          }\r\n        }));\r\n      }\r\n    }\r\n\r\n    // Remove successfully deleted items from the UI\r\n    if (deletedItems.length > 0) {\r\n      setIndexData(prevData => prevData.filter(item => !deletedItems.includes(item._id)));\r\n      setSelectedItems(new Set());\r\n      showNotification('success', `Successfully deleted ${deletedItems.length} of ${bulkDeleteModal.items.length} selected indexes`);\r\n    }\r\n\r\n    setIsBulkDeleting(false);\r\n\r\n    // Show error notification if some items failed\r\n    const failedCount = bulkDeleteModal.items.length - deletedItems.length;\r\n    if (failedCount > 0) {\r\n      showNotification('error', `${failedCount} items failed to delete. Check the details in the modal.`);\r\n    }\r\n\r\n    // Auto-close modal after a delay if all items were successful\r\n    const allSuccessful = bulkDeleteModal.items.every(item =>\r\n      deletedItems.includes(item._id)\r\n    );\r\n\r\n    if (allSuccessful) {\r\n      setTimeout(() => {\r\n        closeBulkDeleteModal();\r\n      }, 2000);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50 dark:bg-gray-900\">\r\n        <AdminSidebar\r\n          currentView=\"show\"\r\n          isOpen={sidebarOpen}\r\n          onToggle={toggleSidebar}\r\n        />\r\n        <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`}>\r\n          {/* Mobile header with hamburger */}\r\n          <div className=\"lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4\">\r\n            <button\r\n              onClick={toggleSidebar}\r\n              className=\"text-gray-600 dark:text-gray-300 hover:text-primaryColor\"\r\n            >\r\n              <PiList className=\"text-xl\" />\r\n            </button>\r\n          </div>\r\n          <div className=\"container mx-auto px-4 py-8\">\r\n            <div className=\"text-center\">\r\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primaryColor mx-auto mb-4\"></div>\r\n              <p className=\"text-n600 dark:text-n30\">Loading index data...</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex h-screen bg-gray-50 dark:bg-gray-900\">\r\n        <AdminSidebar\r\n          currentView=\"show\"\r\n          isOpen={sidebarOpen}\r\n          onToggle={toggleSidebar}\r\n        />\r\n        <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`}>\r\n          {/* Mobile header with hamburger */}\r\n          <div className=\"lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4\">\r\n            <button\r\n              onClick={toggleSidebar}\r\n              className=\"text-gray-600 dark:text-gray-300 hover:text-primaryColor\"\r\n            >\r\n              <PiList className=\"text-xl\" />\r\n            </button>\r\n          </div>\r\n          <div className=\"container mx-auto px-4 py-8\">\r\n            <div className=\"text-center\">\r\n              <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6\">\r\n                <PiWarning className=\"text-red-500 text-3xl mx-auto mb-4\" />\r\n                <h3 className=\"text-lg font-semibold text-red-700 dark:text-red-400 mb-2\">\r\n                  Error Loading Data\r\n                </h3>\r\n                <p className=\"text-red-600 dark:text-red-300 mb-4\">{error}</p>\r\n                <button\r\n                  onClick={fetchIndexData}\r\n                  className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\r\n                >\r\n                  Try Again\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50 dark:bg-gray-900\">\r\n      <AdminSidebar\r\n        currentView=\"show\"\r\n        isOpen={sidebarOpen}\r\n        onToggle={toggleSidebar}\r\n      />\r\n      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`}>\r\n        {/* Mobile header with hamburger */}\r\n        <div className=\"lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4\">\r\n          <button\r\n            onClick={toggleSidebar}\r\n            className=\"text-gray-600 dark:text-gray-300 hover:text-primaryColor\"\r\n          >\r\n            <PiList className=\"text-xl\" />\r\n          </button>\r\n        </div>\r\n        <div className=\"flex-1 overflow-auto\">\r\n          <div className=\"container mx-auto px-4 py-8\">\r\n            {/* Header */}\r\n            <div className=\"mb-8\">\r\n              {/* Header with User Info and Signout */}\r\n              <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6\">\r\n                <div className=\"text-center lg:text-left mb-4 lg:mb-0\">\r\n                  <h1 className=\"text-3xl font-bold text-n700 dark:text-n20 mb-2\">\r\n                    Index Management\r\n                  </h1>\r\n                  <p className=\"text-n600 dark:text-n30\">\r\n                    Manage your FAISS indexes stored in PINE collection.\r\n                  </p>\r\n                </div>\r\n                \r\n                {/* User Info and Signout Button */}\r\n                {currentUser && (\r\n                  <div className=\"flex items-center justify-center lg:justify-end space-x-4\">\r\n                    <div className=\"flex items-center space-x-2 text-sm text-n600 dark:text-n30\">\r\n                      <PiUserCircle className=\"text-lg\" />\r\n                      <div className=\"text-right\">\r\n                        <p className=\"font-medium text-n700 dark:text-n20\">{currentUser.name}</p>\r\n                        <p className=\"text-xs text-n500 dark:text-n40\">{currentUser.email}</p>\r\n                      </div>\r\n                    </div>\r\n                    <button\r\n                      onClick={handleLogoutClick}\r\n                      className=\"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 bg-red-50 hover:bg-red-100 dark:bg-red-900/20 dark:hover:bg-red-900/30 rounded-lg transition-colors duration-200\"\r\n                      title=\"Sign out\"\r\n                    >\r\n                      <PiSignOut className=\"text-lg\" />\r\n                      <span className=\"hidden sm:inline\">Sign Out</span>\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              \r\n              {/* Description */}\r\n              <div className=\"text-center\">\r\n                <p className=\"text-n600 dark:text-n30 max-w-2xl mx-auto text-sm\">\r\n                  Deleting an index will remove both the PINE collection entry and the entire FAISS directory with all associated files.\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Email Filter */}\r\n            <div className=\"mb-6\">\r\n              <div className=\"max-w-md mx-auto\">\r\n                <label htmlFor=\"email-filter\" className=\"block text-sm font-medium text-n700 dark:text-n20 mb-2\">\r\n                  Filter by Email:\r\n                </label>\r\n                <select\r\n                  id=\"email-filter\"\r\n                  value={selectedEmail}\r\n                  onChange={(e) => setSelectedEmail(e.target.value)}\r\n                  className=\"w-full p-3 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20\"\r\n                >\r\n                  <option value=\"\">-- All Emails --</option>\r\n                  {emails.map((email, index) => (\r\n                    <option key={index} value={email}>\r\n                      {email}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Search Bar */}\r\n            <div className=\"mb-6\">\r\n              <div className=\"max-w-md mx-auto relative\">\r\n                <div className=\"relative\">\r\n                  <PiMagnifyingGlass className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-n400 dark:text-n500\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"Search by email, index name, or embedding model...\"\r\n                    value={searchTerm}\r\n                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                    className=\"w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 placeholder-n400 dark:placeholder-n500\"\r\n                  />\r\n                  {searchTerm && (\r\n                    <button\r\n                      onClick={clearSearch}\r\n                      className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-n400 dark:text-n500 hover:text-n600 dark:hover:text-n300 transition-colors\"\r\n                    >\r\n                      <PiX />\r\n                    </button>\r\n                  )}\r\n                </div>\r\n                {searchTerm && (\r\n                  <div className=\"mt-2 text-sm text-n500 dark:text-n40 text-center\">\r\n                    Found {filteredData.length} result{filteredData.length !== 1 ? 's' : ''} for \"{searchTerm}\"\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Control Panel */}\r\n            <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6\">\r\n              {/* Multi-select controls */}\r\n              <div className=\"flex items-center gap-4\">\r\n                <button\r\n                  onClick={toggleMultiSelectMode}\r\n                  className={`px-4 py-2 rounded-md transition-all duration-200 flex items-center font-medium ${\r\n                    isMultiSelectMode\r\n                      ? 'bg-blue-500 text-white hover:bg-blue-600 shadow-md'\r\n                      : 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-800/30 dark:hover:to-indigo-800/30 hover:shadow-sm'\r\n                  }`}\r\n                >\r\n                  {isMultiSelectMode ? (\r\n                    <>\r\n                      <PiX className=\"mr-2 w-4 h-4\" />\r\n                      Exit Multi-Select\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <PiCursor className=\"mr-2 w-4 h-4\" />\r\n                      Enable Multi-Select\r\n                    </>\r\n                  )}\r\n                </button>\r\n\r\n                {isMultiSelectMode && filteredData.length > 0 && (\r\n                  <>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <button\r\n                        onClick={selectedItems.size === filteredData.length ? deselectAllItems : selectAllItems}\r\n                        className=\"text-sm px-3 py-1 rounded-md bg-primaryColor/10 text-primaryColor hover:bg-primaryColor/20 transition-colors flex items-center gap-1 font-medium\"\r\n                      >\r\n                        {selectedItems.size === filteredData.length ? (\r\n                          <>\r\n                            <PiSquare className=\"w-3 h-3\" />\r\n                            Deselect All\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <PiHandPointing className=\"w-3 h-3\" />\r\n                            Select All\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                      {selectedItems.size > 0 && (\r\n                        <span className=\"text-sm text-n500 dark:text-n40\">\r\n                          ({selectedItems.size} selected)\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n\r\n                    {selectedItems.size > 0 && (\r\n                      <button\r\n                        onClick={openBulkDeleteModal}\r\n                        className=\"px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center\"\r\n                      >\r\n                        <PiTrash className=\"mr-2\" />\r\n                        Delete Selected ({selectedItems.size})\r\n                      </button>\r\n                    )}\r\n                  </>\r\n                )}\r\n              </div>\r\n\r\n              {/* Export and Refresh buttons */}\r\n              <div className=\"flex gap-2\">\r\n                {indexData.length > 0 && (\r\n                  <button\r\n                    onClick={() => {\r\n                      const dataToExport = filteredData.map(item => ({\r\n                        email: item.email,\r\n                        index_name: item.index_name,\r\n                        embed_model: item.embed_model || 'N/A',\r\n                        source: item.source\r\n                      }));\r\n\r\n                      const csvContent = [\r\n                        'Email,Index Name,Embedding Model,Source',\r\n                        ...dataToExport.map(item =>\r\n                          `\"${item.email}\",\"${item.index_name}\",\"${item.embed_model}\",\"${item.source}\"`\r\n                        )\r\n                      ].join('\\n');\r\n\r\n                      const blob = new Blob([csvContent], { type: 'text/csv' });\r\n                      const url = URL.createObjectURL(blob);\r\n                      const link = document.createElement('a');\r\n                      link.href = url;\r\n                      link.download = `indexes_export_${new Date().toISOString().split('T')[0]}.csv`;\r\n                      link.click();\r\n                      URL.revokeObjectURL(url);\r\n\r\n                      showNotification('success', `Exported ${dataToExport.length} indexes to CSV`);\r\n                    }}\r\n                    className=\"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center\"\r\n                  >\r\n                    <PiDownloadSimple className=\"mr-2\" />\r\n                    Export CSV\r\n                  </button>\r\n                )}\r\n\r\n                <button\r\n                  onClick={fetchIndexData}\r\n                  disabled={loading}\r\n                  className=\"px-4 py-2 bg-primaryColor text-white rounded-md hover:bg-primaryColor/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center\"\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                      Loading...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <PiArrowsClockwise className=\"mr-2\" />\r\n                      Refresh Data\r\n                    </>\r\n                  )}\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Data Grid */}\r\n            {indexData.length === 0 ? (\r\n              <div className=\"text-center py-12\">\r\n                <PiDatabase className=\"text-6xl text-n300 dark:text-n600 mx-auto mb-4\" />\r\n                <h3 className=\"text-xl font-semibold text-n600 dark:text-n30 mb-2\">\r\n                  No Data Found\r\n                </h3>\r\n                <p className=\"text-n500 dark:text-n40\">\r\n                  No indexes found in the collections.\r\n                </p>\r\n              </div>\r\n            ) : filteredData.length === 0 ? (\r\n              <div className=\"text-center py-12\">\r\n                <PiMagnifyingGlass className=\"text-6xl text-n300 dark:text-n600 mx-auto mb-4\" />\r\n                <h3 className=\"text-xl font-semibold text-n600 dark:text-n30 mb-2\">\r\n                  No Results Found\r\n                </h3>\r\n                <p className=\"text-n500 dark:text-n40 mb-4\">\r\n                  No entries match your search term \"{searchTerm}\".\r\n                </p>\r\n                <button\r\n                  onClick={clearSearch}\r\n                  className=\"px-4 py-2 bg-primaryColor text-white rounded-md hover:bg-primaryColor/90 transition-colors\"\r\n                >\r\n                  Clear Search\r\n                </button>\r\n              </div>\r\n            ) : (\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n                {filteredData.map((item) => (\r\n                  <div\r\n                    key={item._id}\r\n                    className={`border rounded-lg overflow-hidden hover:shadow-md transition-all duration-300 bg-white dark:bg-n800 ${\r\n                      isMultiSelectMode && selectedItems.has(item._id)\r\n                        ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800'\r\n                        : 'border-primaryColor/20 hover:border-primaryColor/50'\r\n                    }`}\r\n                  >\r\n                    <div className=\"p-6\">\r\n                      {/* Header */}\r\n                      <div className=\"flex items-center justify-between mb-4\">\r\n                        <div className=\"flex items-center\">\r\n                          {isMultiSelectMode && (\r\n                            <button\r\n                              onClick={() => toggleItemSelection(item._id)}\r\n                              className=\"mr-3 p-1 rounded hover:bg-gray-100 dark:hover:bg-n700 transition-colors\"\r\n                            >\r\n                              {selectedItems.has(item._id) ? (\r\n                                <PiCheckSquare className=\"text-blue-500 text-lg\" />\r\n                              ) : (\r\n                                <PiSquare className=\"text-n400 dark:text-n500 text-lg\" />\r\n                              )}\r\n                            </button>\r\n                          )}\r\n                          <PiDatabase className=\"text-primaryColor text-xl mr-2\" />\r\n                          <h3 className=\"font-semibold text-n700 dark:text-n20\">\r\n                            Index Entry\r\n                          </h3>\r\n                        </div>\r\n                        <div className=\"flex gap-2\">\r\n                          <span className=\"text-xs px-2 py-1 rounded-full bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 flex items-center gap-1\">\r\n                            <PiShieldCheck className=\"w-3 h-3\" />\r\n                            PINE\r\n                          </span>\r\n                          <span className=\"text-xs bg-primaryColor/10 text-primaryColor px-2 py-1 rounded-full flex items-center gap-1\">\r\n                            <PiCheckCircle className=\"w-3 h-3\" />\r\n                            Active\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Content */}\r\n                      <div className=\"space-y-3 mb-6\">\r\n                        <div className=\"flex items-start\">\r\n                          <PiEnvelope className=\"text-green-500 dark:text-green-400 mt-1 mr-3 flex-shrink-0\" />\r\n                          <div>\r\n                            <p className=\"text-xs text-n500 dark:text-n40 uppercase tracking-wide\">\r\n                              Email\r\n                            </p>\r\n                            <p className=\"text-sm font-medium text-n700 dark:text-n20 break-all\">\r\n                              {item.email}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex items-start\">\r\n                          <PiTag className=\"text-blue-500 dark:text-blue-400 mt-1 mr-3 flex-shrink-0\" />\r\n                          <div>\r\n                            <p className=\"text-xs text-n500 dark:text-n40 uppercase tracking-wide\">\r\n                              Index Name\r\n                            </p>\r\n                            <p className=\"text-sm font-medium text-n700 dark:text-n20 break-all\">\r\n                              {item.index_name}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n\r\n                        {item.embed_model && (\r\n                          <div className=\"flex items-start\">\r\n                            <PiInfo className=\"text-purple-500 dark:text-purple-400 mt-1 mr-3 flex-shrink-0\" />\r\n                            <div>\r\n                              <p className=\"text-xs text-n500 dark:text-n40 uppercase tracking-wide\">\r\n                                Embedding Model\r\n                              </p>\r\n                              <p className=\"text-sm font-mono text-n700 dark:text-n20 break-all\">\r\n                                {item.embed_model}\r\n                              </p>\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n\r\n                      {/* Action Buttons - Hidden in multi-select mode */}\r\n                      {!isMultiSelectMode && (\r\n                        <div className=\"space-y-3\">\r\n                          <button\r\n                            onClick={() => openEditDataModal(item)}\r\n                            className=\"w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors font-medium flex items-center justify-center\"\r\n                          >\r\n                            <PiTable className=\"mr-2\" />\r\n                            Edit Data\r\n                          </button>\r\n                          <button\r\n                            onClick={() => openDeleteModal(item)}\r\n                            disabled={deleteLoading === item._id}\r\n                            className=\"w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:bg-gray-300 dark:disabled:bg-gray-700 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center\"\r\n                          >\r\n                            {deleteLoading === item._id ? (\r\n                              <>\r\n                                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                                Deleting...\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <PiTrash className=\"mr-2\" />\r\n                                Delete Index\r\n                              </>\r\n                            )}\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Multi-select info */}\r\n                      {isMultiSelectMode && (\r\n                        <div className=\"text-center text-sm text-n500 dark:text-n40\">\r\n                          {selectedItems.has(item._id) ? 'Selected for deletion' : 'Click checkbox to select'}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n\r\n            {/* Delete Confirmation Modal */}\r\n            <DeleteConfirmationModal\r\n              isOpen={deleteModal.isOpen}\r\n              onClose={closeDeleteModal}\r\n              onConfirm={confirmDelete}\r\n              itemData={deleteModal.item}\r\n              isDeleting={deleteLoading !== null}\r\n            />\r\n\r\n            {/* Bulk Delete Modal */}\r\n            <DeleteConfirmationModal\r\n              isOpen={bulkDeleteModal.isOpen}\r\n              onClose={closeBulkDeleteModal}\r\n              onConfirm={handleBulkDelete}\r\n              itemData={null}\r\n              selectedItems={bulkDeleteModal.items}\r\n              isDeleting={isBulkDeleting}\r\n              isBulkDelete={true}\r\n              bulkProgress={bulkDeleteProgress}\r\n            />\r\n\r\n            {/* Edit Data Modal */}\r\n            <EditDataModal\r\n              isOpen={editDataModal.isOpen}\r\n              onClose={closeEditDataModal}\r\n              indexData={editDataModal.indexData}\r\n            />\r\n\r\n            {/* Notification Toast */}\r\n            {notification.isVisible && (\r\n              <div className={`fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg transition-all duration-300 transform ${\r\n                notification.isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'\r\n              } ${\r\n                notification.type === 'success'\r\n                  ? 'bg-green-500 text-white'\r\n                  : notification.type === 'error'\r\n                  ? 'bg-red-500 text-white'\r\n                  : 'bg-blue-500 text-white'\r\n              }`}>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center\">\r\n                    {notification.type === 'success' && <PiCheckCircle className=\"mr-2 text-lg\" />}\r\n                    {notification.type === 'error' && <PiWarning className=\"mr-2 text-lg\" />}\r\n                    {notification.type === 'info' && <PiInfo className=\"mr-2 text-lg\" />}\r\n                    <span className=\"text-sm font-medium\">{notification.message}</span>\r\n                  </div>\r\n                  <button\r\n                    onClick={() => setNotification(prev => ({ ...prev, isVisible: false }))}\r\n                    className=\"ml-4 text-white hover:text-gray-200 transition-colors\"\r\n                  >\r\n                    <PiX className=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Logout Confirmation Modal */}\r\n      <LogoutConfirmationModal\r\n        isOpen={showLogoutConfirm}\r\n        onClose={() => setShowLogoutConfirm(false)}\r\n        onConfirm={handleLogout}\r\n        isProcessing={isLogoutProcessing}\r\n        userName={currentUser?.name || 'User'}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ShowDeleteIndex;"], "names": [], "mappings": ";;;;AACA;AAyBA;AACA;AACA;AACA,aAAa;AACb;AACA;AA7BA;AAFA;;;;;;;;;AA6DA,MAAM,0BAAsD,CAAC,EAC3D,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,gBAAgB,EAAE,EAClB,UAAU,EACV,eAAe,KAAK,EACpB,eAAe,CAAC,CAAC,EAClB;IACC,IAAI,CAAC,UAAW,CAAC,YAAY,CAAC,cAAe,OAAO;IAEpD,MAAM,aAAa,gBAAgB,cAAc,MAAM,GAAG;IAE1D,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8IAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAG,WAAU;sCACX,aAAa,CAAC,uBAAuB,EAAE,cAAc,MAAM,CAAC,OAAO,CAAC,GAAG;;;;;;;;;;;;8BAI5E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAA+B;gCACT,aAAa,kBAAkB;gCAAa;;;;;;;sCAE/E,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;;wCAAG;wCAAkB,aAAa,MAAM;wCAAG;;;;;;;8CAC5C,8OAAC;;wCAAG;wCAAuC,aAAa,QAAQ;wCAAI;;;;;;;8CACpE,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;wBAIL,CAAC,cAAc,0BACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAA8C;wCAC9C,SAAS,UAAU;;;;;;;8CAEhC,8OAAC;oCAAE,WAAU;;wCAAkC;wCACrC,SAAS,KAAK;;;;;;;8CAExB,8OAAC;oCAAE,WAAU;;wCAAkC;wCACrC,SAAS,WAAW,IAAI;;;;;;;;;;;;;wBAMrC,4BACC,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC;gCAClB,MAAM,WAAW,YAAY,CAAC,KAAK,GAAG,CAAC;gCACvC,qBACE,8OAAC;oCAAmB,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,KAAK,UAAU;;;;;;sEAElB,8OAAC;4DAAE,WAAU;sEACV,KAAK,KAAK;;;;;;;;;;;;gDAGd,0BACC,8OAAC;oDAAI,WAAU;;wDACZ,SAAS,MAAM,KAAK,2BACnB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;wDAEzC,SAAS,MAAM,KAAK,4BACnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;wDAG3C,SAAS,MAAM,KAAK,2BACnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8IAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;;;;;;;wDAG5C,SAAS,MAAM,KAAK,yBACnB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8IAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAuB;;;;;;;;;;;;;;;;;;;;;;;;wCAMhD,UAAU,yBACT,8OAAC;4CAAE,WAAU;sDACV,SAAS,OAAO;;;;;;;mCAtCb,KAAK,GAAG;;;;;4BA2CtB;;;;;;;;;;;;8BAKN,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,aAAa,UAAU;;;;;;wBAEzB,CAAC,4BACA,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,8IAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAS;gCACpB,aAAa,GAAG,cAAc,MAAM,CAAC,MAAM,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;AAOrE;AAEA,sCAAsC;AACtC,MAAM,0BAMD,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE;IAC1D,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8IAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAG,WAAU;sCAAgD;;;;;;;;;;;;8BAKhE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAA+B;gCACP;gCAAS;;;;;;;sCAE9C,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;8BAKjD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,6BACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCAAuE;;6DAIxF;;kDACE,8OAAC,8IAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAS;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;AAEA,MAAM,kBAA4B;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,QAAQ;QACR,MAAM;IACR;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,4BAA4B;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGnC;IACV,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,qBAAqB;IACrB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,QAAQ;QACR,OAAO,EAAE;IACX;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,CAAC;IAClF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,wBAAwB;IACxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,QAAQ;QACR,WAAW;IACb;IAEA,qBAAqB;IACrB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAI5C;QACD,MAAM;QACN,SAAS;QACT,WAAW;IACb;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC,MAAoC;QAC5D,gBAAgB;YAAE;YAAM;YAAS,WAAW;QAAK;QACjD,WAAW;YACT,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC;QACxD,GAAG;IACL;IAEA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAsBnC;IACF,GAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,eAAe;QACnB,IAAI,oBAAoB;YACtB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,sBAAsB;QACtB,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,oBAAoB;YACpB,QAAQ,GAAG,CAAC;YACZ,eAAe,KAAK;YACpB,aAAa,KAAK;YAElB,wBAAwB;YACxB,qBAAqB;YAErB,2BAA2B;YAC3B,iBAAiB,QAAQ;YAEzB,iCAAiC;YACjC,QAAQ,GAAG,CAAC;YACZ,WAAW;gBACT,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,4CAA4C;YAC5C,WAAW;gBACT,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,GAAG;QACL;IACF;IAEA,6BAA6B;IAC7B,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,mCAAmC;IACnC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,OAAO;QACT;QAEA,MAAM,cAAc,WAAW,WAAW,GAAG,IAAI;QACjD,OAAO,UAAU,MAAM,CAAC,CAAC;YACvB,OACE,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACtC,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;QAEjE;IACF,GAAG;QAAC;QAAW;KAAW;IAE1B,eAAe;IACf,MAAM,cAAc;QAClB,cAAc;QACd,iBAAiB,QAAQ;IAC3B;IAEA,8BAA8B;IAC9B,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,SAAS;YAET,0BAA0B;YAC1B,MAAM,YAAY,MAAM,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;YAClC,UAAU;YAEV,IAAI,eAA4B,EAAE;YAElC,gEAAgE;YAChE,IAAI,eAAe;gBACjB,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,eAAe;gBACnE,MAAM,eAAe,MAAM,CAAA,GAAA,6HAAA,CAAA,oBAAiB,AAAD,EAAE;gBAC7C,eAAe;uBAAI;uBAAiB;iBAAa;YACnD,OAAO;gBACL,wDAAwD;gBACxD,QAAQ,GAAG,CAAC;gBACZ,KAAK,MAAM,SAAS,UAAW;oBAC7B,IAAI;wBACF,MAAM,eAAe,MAAM,CAAA,GAAA,6HAAA,CAAA,oBAAiB,AAAD,EAAE;wBAC7C,eAAe;+BAAI;+BAAiB;yBAAa;oBACnD,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,MAAM,CAAC,CAAC,EAAE;oBAC5D,+CAA+C;oBACjD;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YACvC,aAAa;YAEb,6DAA6D;YAC7D,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,iBAAiB,WAAW,CAAC,oBAAoB,EAAE,aAAa,MAAM,CAAC,QAAQ,CAAC;YAClF,OAAO;gBACL,iBAAiB,QAAQ;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,iBAAiB,SAAS,CAAC,qBAAqB,EAAE,cAAc;QAClE,SAAU;YACR,WAAW;QACb;IACF;IAEA,0EAA0E;IAC1E,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,iBAAiB,KAAK,GAAG;YAEzB,4EAA4E;YAC5E,MAAM,aAAa,KAAK,GAAG,EAAE,SAAS,UAAU,KAAK,GAAG,GAAG,KAAK,GAAG;YACnE,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,IAAI,oBAAoB;YACxB,IAAI,8BAA8B;YAClC,IAAI,YAAY;YAChB,IAAI,sBAAsB;YAE1B,sCAAsC;YACtC,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,eAAe,MAAM,MACzB,CAAC,iEAAiE,EAAE,WAAW,aAAa,CAAC,EAC7F;oBACE,QAAQ;oBACR,SAAS;wBACP,UAAU;wBACV,gBAAgB;wBAChB,SAAS;oBACX;gBACF;gBAGF,IAAI,aAAa,EAAE,EAAE;oBACnB,QAAQ,GAAG,CAAC;oBACZ,oBAAoB;gBACtB,OAAO;oBACL,MAAM,YAAY,MAAM,aAAa,IAAI;oBACzC,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,YAAY,CAAC,iCAAiC,EAAE,aAAa,MAAM,CAAC,GAAG,EAAE,WAAW;gBACtF;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,YAAY,CAAC,gCAAgC,EAAE,eAAe,QAAQ,IAAI,OAAO,GAAG,iBAAiB;YACvG;YAEA,iEAAiE;YACjE,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU,KAAK,OAAO;gBAChD,IAAI;oBACF,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,KAAK,UAAU,CAAC,GAAG,CAAC;oBAC/E,MAAM,cAAc,MAAM,CAAA,GAAA,+GAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU;oBAC1D,QAAQ,GAAG,CAAC,oCAAoC;oBAEhD,IAAI,YAAY,OAAO,EAAE;wBACvB,8BAA8B;oBAChC,OAAO;wBACL,sBAAsB,CAAC,iCAAiC,EAAE,YAAY,KAAK,EAAE;oBAC/E;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,sBAAsB,CAAC,gCAAgC,EAAE,eAAe,QAAQ,IAAI,OAAO,GAAG,iBAAiB;gBACjH;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,8BAA8B,MAAM,sDAAsD;YAC5F;YAEA,+CAA+C;YAC/C,MAAM,iBAAiB,qBAAqB;YAE5C,IAAI,gBAAgB;gBAClB,QAAQ,GAAG,CAAC;gBACZ,+BAA+B;gBAC/B,aAAa,CAAA,WAAY,SAAS,MAAM,CAAC,CAAA,WAAY,SAAS,GAAG,KAAK,KAAK,GAAG;gBAC9E,cAAc;gBACd,eAAe;oBAAE,QAAQ;oBAAO,MAAM;gBAAK;gBAC3C,4BAA4B;gBAC5B,iBAAiB,WAAW,CAAC,4BAA4B,EAAE,KAAK,UAAU,CAAC,CAAC,CAAC;YAC/E,OAAO;gBACL,8BAA8B;gBAC9B,IAAI,eAAe;gBAEnB,IAAI,mBAAmB;oBACrB,gBAAgB;gBAClB,OAAO;oBACL,gBAAgB,CAAC,mCAAmC,EAAE,UAAU,EAAE,CAAC;gBACrE;gBAEA,IAAI,6BAA6B;oBAC/B,gBAAgB;gBAClB,OAAO;oBACL,gBAAgB,CAAC,mCAAmC,EAAE,qBAAqB;gBAC7E;gBAEA,QAAQ,KAAK,CAAC,4BAA4B;gBAE1C,oEAAoE;gBACpE,IAAI,mBAAmB;oBACrB,aAAa,CAAA,WAAY,SAAS,MAAM,CAAC,CAAA,WAAY,SAAS,GAAG,KAAK,KAAK,GAAG;oBAC9E,eAAe;wBAAE,QAAQ;wBAAO,MAAM;oBAAK;gBAC7C;gBAEA,qBAAqB;gBACrB,SAAS;gBACT,iBAAiB,SAAS;YAC5B;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAc;IAElB,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB,IAAI;IACvB,GAAG;QAAC;KAAU;IAEd,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,OAAO,gBAAgB,CAAC,qBAAqB;QAE7C,OAAO;YACL,OAAO,mBAAmB,CAAC,qBAAqB;QAClD;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,eAAe;YAAE,QAAQ;YAAM;QAAK;IACtC;IAEA,MAAM,mBAAmB;QACvB,eAAe;YAAE,QAAQ;YAAO,MAAM;QAAK;IAC7C;IAEA,MAAM,gBAAgB;QACpB,IAAI,YAAY,IAAI,EAAE;YACpB,aAAa,YAAY,IAAI;QAC/B;IACF;IAEA,MAAM,gBAAgB;QACpB,eAAe,CAAC;IAClB;IAEA,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;YAAE,QAAQ;YAAM,WAAW;QAAK;QACjD,iBAAiB,QAAQ,CAAC,wBAAwB,EAAE,KAAK,UAAU,CAAC,CAAC,CAAC;IACxE;IAEA,MAAM,qBAAqB;QACzB,iBAAiB;YAAE,QAAQ;YAAO,WAAW;QAAK;IACpD;IAEA,yBAAyB;IACzB,MAAM,wBAAwB;QAC5B,MAAM,UAAU,CAAC;QACjB,qBAAqB;QACrB,iBAAiB,IAAI;QAErB,IAAI,SAAS;YACX,iBAAiB,QAAQ;QAC3B,OAAO;YACL,iBAAiB,QAAQ;QAC3B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,SAAS;YAC3B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,iBAAiB;IACnB;IAEA,MAAM,iBAAiB;QACrB,MAAM,SAAS,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;QACxD,iBAAiB;QACjB,iBAAiB,QAAQ,CAAC,aAAa,EAAE,aAAa,MAAM,CAAC,cAAc,CAAC;IAC9E;IAEA,MAAM,mBAAmB;QACvB,iBAAiB,IAAI;QACrB,iBAAiB,QAAQ;IAC3B;IAEA,MAAM,sBAAsB;QAC1B,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,OAAQ,cAAc,GAAG,CAAC,KAAK,GAAG;QAC5E,mBAAmB;YAAE,QAAQ;YAAM,OAAO;QAAc;IAC1D;IAEA,MAAM,uBAAuB;QAC3B,mBAAmB;YAAE,QAAQ;YAAO,OAAO,EAAE;QAAC;QAC9C,sBAAsB,CAAC;IACzB;IAEA,uBAAuB;IACvB,MAAM,mBAAmB;QACvB,IAAI,gBAAgB,KAAK,CAAC,MAAM,KAAK,GAAG;QAExC,kBAAkB;QAElB,oCAAoC;QACpC,MAAM,kBAAsC,CAAC;QAC7C,gBAAgB,KAAK,CAAC,OAAO,CAAC,CAAA;YAC5B,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG;gBAAE,QAAQ;YAAU;QAClD;QACA,sBAAsB;QAEtB,MAAM,eAAyB,EAAE;QAEjC,kEAAkE;QAClE,KAAK,MAAM,QAAQ,gBAAgB,KAAK,CAAE;YACxC,IAAI;gBACF,4BAA4B;gBAC5B,sBAAsB,CAAA,OAAQ,CAAC;wBAC7B,GAAG,IAAI;wBACP,CAAC,KAAK,GAAG,CAAC,EAAE;4BAAE,QAAQ;4BAAY,SAAS;wBAAc;oBAC3D,CAAC;gBAED,kDAAkD;gBAClD,MAAM,aAAa,KAAK,GAAG,EAAE,SAAS,UAAU,KAAK,GAAG,GAAG,KAAK,GAAG;gBAEnE,IAAI,oBAAoB;gBACxB,IAAI,8BAA8B;gBAClC,IAAI,YAAY;gBAChB,IAAI,sBAAsB;gBAE1B,sCAAsC;gBACtC,IAAI;oBACF,MAAM,eAAe,MAAM,MACzB,CAAC,iEAAiE,EAAE,WAAW,aAAa,CAAC,EAC7F;wBACE,QAAQ;wBACR,SAAS;4BACP,UAAU;4BACV,gBAAgB;4BAChB,SAAS;wBACX;oBACF;oBAGF,IAAI,aAAa,EAAE,EAAE;wBACnB,oBAAoB;oBACtB,OAAO;wBACL,MAAM,YAAY,MAAM,aAAa,IAAI;wBACzC,YAAY,CAAC,sBAAsB,EAAE,aAAa,MAAM,CAAC,GAAG,EAAE,WAAW;oBAC3E;gBACF,EAAE,OAAO,KAAK;oBACZ,YAAY,CAAC,qBAAqB,EAAE,eAAe,QAAQ,IAAI,OAAO,GAAG,iBAAiB;gBAC5F;gBAEA,iCAAiC;gBACjC,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU,KAAK,OAAO;oBAChD,IAAI;wBACF,MAAM,cAAc,MAAM,CAAA,GAAA,+GAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,UAAU;wBAC1D,IAAI,YAAY,OAAO,EAAE;4BACvB,8BAA8B;wBAChC,OAAO;4BACL,sBAAsB,CAAC,uBAAuB,EAAE,YAAY,KAAK,EAAE;wBACrE;oBACF,EAAE,OAAO,KAAK;wBACZ,sBAAsB,CAAC,sBAAsB,EAAE,eAAe,QAAQ,IAAI,OAAO,GAAG,iBAAiB;oBACvG;gBACF,OAAO;oBACL,8BAA8B;gBAChC;gBAEA,mCAAmC;gBACnC,MAAM,iBAAiB,qBAAqB;gBAE5C,IAAI,gBAAgB;oBAClB,sBAAsB,CAAA,OAAQ,CAAC;4BAC7B,GAAG,IAAI;4BACP,CAAC,KAAK,GAAG,CAAC,EAAE;gCAAE,QAAQ;gCAAW,SAAS;4BAAuB;wBACnE,CAAC;oBACD,aAAa,IAAI,CAAC,KAAK,GAAG;gBAC5B,OAAO;oBACL,IAAI,eAAe;oBACnB,IAAI,CAAC,mBAAmB,gBAAgB,YAAY;oBACpD,IAAI,CAAC,6BAA6B,gBAAgB;oBAElD,sBAAsB,CAAA,OAAQ,CAAC;4BAC7B,GAAG,IAAI;4BACP,CAAC,KAAK,GAAG,CAAC,EAAE;gCAAE,QAAQ;gCAAS,SAAS,aAAa,IAAI;4BAAG;wBAC9D,CAAC;gBACH;YAEF,EAAE,OAAO,KAAK;gBACZ,sBAAsB,CAAA,OAAQ,CAAC;wBAC7B,GAAG,IAAI;wBACP,CAAC,KAAK,GAAG,CAAC,EAAE;4BACV,QAAQ;4BACR,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;wBAChD;oBACF,CAAC;YACH;QACF;QAEA,gDAAgD;QAChD,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,aAAa,CAAA,WAAY,SAAS,MAAM,CAAC,CAAA,OAAQ,CAAC,aAAa,QAAQ,CAAC,KAAK,GAAG;YAChF,iBAAiB,IAAI;YACrB,iBAAiB,WAAW,CAAC,qBAAqB,EAAE,aAAa,MAAM,CAAC,IAAI,EAAE,gBAAgB,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAC/H;QAEA,kBAAkB;QAElB,+CAA+C;QAC/C,MAAM,cAAc,gBAAgB,KAAK,CAAC,MAAM,GAAG,aAAa,MAAM;QACtE,IAAI,cAAc,GAAG;YACnB,iBAAiB,SAAS,GAAG,YAAY,wDAAwD,CAAC;QACpG;QAEA,8DAA8D;QAC9D,MAAM,gBAAgB,gBAAgB,KAAK,CAAC,KAAK,CAAC,CAAA,OAChD,aAAa,QAAQ,CAAC,KAAK,GAAG;QAGhC,IAAI,eAAe;YACjB,WAAW;gBACT;YACF,GAAG;QACL;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,2HAAA,CAAA,UAAY;oBACX,aAAY;oBACZ,QAAQ;oBACR,UAAU;;;;;;8BAEZ,8OAAC;oBAAI,WAAW,CAAC,mCAAmC,EAAE,cAAc,aAAa,YAAY;;sCAE3F,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,8IAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAGtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMnD;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,2HAAA,CAAA,UAAY;oBACX,aAAY;oBACZ,QAAQ;oBACR,UAAU;;;;;;8BAEZ,8OAAC;oBAAI,WAAW,CAAC,mCAAmC,EAAE,cAAc,aAAa,YAAY;;sCAE3F,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,8IAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAGtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8IAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,8OAAC;4CAAE,WAAU;sDAAuC;;;;;;sDACpD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,2HAAA,CAAA,UAAY;gBACX,aAAY;gBACZ,QAAQ;gBACR,UAAU;;;;;;0BAEZ,8OAAC;gBAAI,WAAW,CAAC,mCAAmC,EAAE,cAAc,aAAa,YAAY;;kCAE3F,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,8IAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAGtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAkD;;;;;;sEAGhE,8OAAC;4DAAE,WAAU;sEAA0B;;;;;;;;;;;;gDAMxC,6BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8IAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;8EACxB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAuC,YAAY,IAAI;;;;;;sFACpE,8OAAC;4EAAE,WAAU;sFAAmC,YAAY,KAAK;;;;;;;;;;;;;;;;;;sEAGrE,8OAAC;4DACC,SAAS;4DACT,WAAU;4DACV,OAAM;;8EAEN,8OAAC,8IAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,8OAAC;oEAAK,WAAU;8EAAmB;;;;;;;;;;;;;;;;;;;;;;;;sDAO3C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAoD;;;;;;;;;;;;;;;;;8CAOrE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAAyD;;;;;;0DAGjG,8OAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;4DAAmB,OAAO;sEACxB;2DADU;;;;;;;;;;;;;;;;;;;;;;8CASrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8IAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;kEAC7B,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;oDAEX,4BACC,8OAAC;wDACC,SAAS;wDACT,WAAU;kEAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;;;;;;;;;;;;;;;;4CAIT,4BACC,8OAAC;gDAAI,WAAU;;oDAAmD;oDACzD,aAAa,MAAM;oDAAC;oDAAQ,aAAa,MAAM,KAAK,IAAI,MAAM;oDAAG;oDAAO;oDAAW;;;;;;;;;;;;;;;;;;8CAOlG,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,WAAW,CAAC,+EAA+E,EACzF,oBACI,uDACA,uRACJ;8DAED,kCACC;;0EACE,8OAAC,8IAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;qFAIlC;;0EACE,8OAAC,8IAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;gDAM1C,qBAAqB,aAAa,MAAM,GAAG,mBAC1C;;sEACE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,SAAS,cAAc,IAAI,KAAK,aAAa,MAAM,GAAG,mBAAmB;oEACzE,WAAU;8EAET,cAAc,IAAI,KAAK,aAAa,MAAM,iBACzC;;0FACE,8OAAC,8IAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAAY;;qGAIlC;;0FACE,8OAAC,8IAAA,CAAA,iBAAc;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;gEAK3C,cAAc,IAAI,GAAG,mBACpB,8OAAC;oEAAK,WAAU;;wEAAkC;wEAC9C,cAAc,IAAI;wEAAC;;;;;;;;;;;;;wDAK1B,cAAc,IAAI,GAAG,mBACpB,8OAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,8OAAC,8IAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAS;gEACV,cAAc,IAAI;gEAAC;;;;;;;;;;;;;;;sDAQ/C,8OAAC;4CAAI,WAAU;;gDACZ,UAAU,MAAM,GAAG,mBAClB,8OAAC;oDACC,SAAS;wDACP,MAAM,eAAe,aAAa,GAAG,CAAC,CAAA,OAAQ,CAAC;gEAC7C,OAAO,KAAK,KAAK;gEACjB,YAAY,KAAK,UAAU;gEAC3B,aAAa,KAAK,WAAW,IAAI;gEACjC,QAAQ,KAAK,MAAM;4DACrB,CAAC;wDAED,MAAM,aAAa;4DACjB;+DACG,aAAa,GAAG,CAAC,CAAA,OAClB,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,KAAK,UAAU,CAAC,GAAG,EAAE,KAAK,WAAW,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC;yDAEhF,CAAC,IAAI,CAAC;wDAEP,MAAM,OAAO,IAAI,KAAK;4DAAC;yDAAW,EAAE;4DAAE,MAAM;wDAAW;wDACvD,MAAM,MAAM,IAAI,eAAe,CAAC;wDAChC,MAAM,OAAO,SAAS,aAAa,CAAC;wDACpC,KAAK,IAAI,GAAG;wDACZ,KAAK,QAAQ,GAAG,CAAC,eAAe,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;wDAC9E,KAAK,KAAK;wDACV,IAAI,eAAe,CAAC;wDAEpB,iBAAiB,WAAW,CAAC,SAAS,EAAE,aAAa,MAAM,CAAC,eAAe,CAAC;oDAC9E;oDACA,WAAU;;sEAEV,8OAAC,8IAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;wDAAS;;;;;;;8DAKzC,8OAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;8DAET,wBACC;;0EACE,8OAAC;gEAAI,WAAU;;;;;;4DAAuE;;qFAIxF;;0EACE,8OAAC,8IAAA,CAAA,oBAAiB;gEAAC,WAAU;;;;;;4DAAS;;;;;;;;;;;;;;;;;;;;gCAS/C,UAAU,MAAM,KAAK,kBACpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8IAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAG,WAAU;sDAAqD;;;;;;sDAGnE,8OAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;2CAIvC,aAAa,MAAM,KAAK,kBAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8IAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,8OAAC;4CAAG,WAAU;sDAAqD;;;;;;sDAGnE,8OAAC;4CAAE,WAAU;;gDAA+B;gDACN;gDAAW;;;;;;;sDAEjD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;yDAKH,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;4CAEC,WAAW,CAAC,oGAAoG,EAC9G,qBAAqB,cAAc,GAAG,CAAC,KAAK,GAAG,IAC3C,4DACA,uDACJ;sDAEF,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEACZ,mCACC,8OAAC;wEACC,SAAS,IAAM,oBAAoB,KAAK,GAAG;wEAC3C,WAAU;kFAET,cAAc,GAAG,CAAC,KAAK,GAAG,kBACzB,8OAAC,8IAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;iGAEzB,8OAAC,8IAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;;;;;;kFAI1B,8OAAC,8IAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,8OAAC;wEAAG,WAAU;kFAAwC;;;;;;;;;;;;0EAIxD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,8IAAA,CAAA,gBAAa;gFAAC,WAAU;;;;;;4EAAY;;;;;;;kFAGvC,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,8IAAA,CAAA,gBAAa;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;;;;;;;;kEAO3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,8IAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAA0D;;;;;;0FAGvE,8OAAC;gFAAE,WAAU;0FACV,KAAK,KAAK;;;;;;;;;;;;;;;;;;0EAKjB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,8IAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAA0D;;;;;;0FAGvE,8OAAC;gFAAE,WAAU;0FACV,KAAK,UAAU;;;;;;;;;;;;;;;;;;4DAKrB,KAAK,WAAW,kBACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,8IAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FAA0D;;;;;;0FAGvE,8OAAC;gFAAE,WAAU;0FACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;oDAQ1B,CAAC,mCACA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,kBAAkB;gEACjC,WAAU;;kFAEV,8OAAC,8IAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAS;;;;;;;0EAG9B,8OAAC;gEACC,SAAS,IAAM,gBAAgB;gEAC/B,UAAU,kBAAkB,KAAK,GAAG;gEACpC,WAAU;0EAET,kBAAkB,KAAK,GAAG,iBACzB;;sFACE,8OAAC;4EAAI,WAAU;;;;;;wEAAuE;;iGAIxF;;sFACE,8OAAC,8IAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAAS;;;;;;;;;;;;;;oDASrC,mCACC,8OAAC;wDAAI,WAAU;kEACZ,cAAc,GAAG,CAAC,KAAK,GAAG,IAAI,0BAA0B;;;;;;;;;;;;2CAlH1D,KAAK,GAAG;;;;;;;;;;8CA4HrB,8OAAC;oCACC,QAAQ,YAAY,MAAM;oCAC1B,SAAS;oCACT,WAAW;oCACX,UAAU,YAAY,IAAI;oCAC1B,YAAY,kBAAkB;;;;;;8CAIhC,8OAAC;oCACC,QAAQ,gBAAgB,MAAM;oCAC9B,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe,gBAAgB,KAAK;oCACpC,YAAY;oCACZ,cAAc;oCACd,cAAc;;;;;;8CAIhB,8OAAC,sIAAA,CAAA,UAAa;oCACZ,QAAQ,cAAc,MAAM;oCAC5B,SAAS;oCACT,WAAW,cAAc,SAAS;;;;;;gCAInC,aAAa,SAAS,kBACrB,8OAAC;oCAAI,WAAW,CAAC,iGAAiG,EAChH,aAAa,SAAS,GAAG,8BAA8B,6BACxD,CAAC,EACA,aAAa,IAAI,KAAK,YAClB,4BACA,aAAa,IAAI,KAAK,UACtB,0BACA,0BACJ;8CACA,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDACZ,aAAa,IAAI,KAAK,2BAAa,8OAAC,8IAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAC5D,aAAa,IAAI,KAAK,yBAAW,8OAAC,8IAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACtD,aAAa,IAAI,KAAK,wBAAU,8OAAC,8IAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEACnD,8OAAC;wDAAK,WAAU;kEAAuB,aAAa,OAAO;;;;;;;;;;;;0DAE7D,8OAAC;gDACC,SAAS,IAAM,gBAAgB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,WAAW;wDAAM,CAAC;gDACrE,WAAU;0DAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7B,8OAAC;gBACC,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,WAAW;gBACX,cAAc;gBACd,UAAU,aAAa,QAAQ;;;;;;;;;;;;AAIvC;uCAEe"}}, {"offset": {"line": 4554, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4560, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/app/show-index/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport ShowDeleteIndex from '@/components/showdeleteindex';\r\n\r\nconst ShowIndexPage: React.FC = () => {\r\n  return <ShowDeleteIndex />;\r\n};\r\n\r\nexport default ShowIndexPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,gBAA0B;IAC9B,qBAAO,8OAAC,8HAAA,CAAA,UAAe;;;;;AACzB;uCAEe"}}, {"offset": {"line": 4576, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}