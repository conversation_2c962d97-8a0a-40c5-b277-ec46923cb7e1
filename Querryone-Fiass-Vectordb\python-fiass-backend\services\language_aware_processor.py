"""
Language-Aware Query Processing System

This module implements a comprehensive language-aware query processing system that:
1. Detects the language of incoming user queries
2. Determines the primary language used in CSV file data
3. Implements intelligent query processing logic based on language matching
4. Handles translation-based processing when languages differ
5. Maintains user's preferred response language throughout the process

Key Features:
- Language detection for both queries and CSV data
- Direct processing for matching South Indian languages (Tamil, Telugu, Kannada)
- Translation-based processing for language mismatches
- Comprehensive error handling and edge case management
- Integration with existing translation services
"""

import os
import json
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import re

# Import existing services
try:
    from .translation_service import translation_service
    TRANSLATION_AVAILABLE = True
except ImportError:
    print("⚠️ Translation service not available")
    TRANSLATION_AVAILABLE = False

try:
    from .language_utils import (
        detect_language_from_text, 
        detect_language_from_dataframe,
        is_language_text,
        get_language_statistics,
        get_supported_languages
    )
    LANGUAGE_UTILS_AVAILABLE = True
except ImportError:
    print("⚠️ Language utils not available")
    LANGUAGE_UTILS_AVAILABLE = False


class LanguageAwareProcessor:
    """
    Main class for language-aware query processing.
    
    This class handles the complete workflow of language detection,
    query processing, and response translation based on language matching logic.
    """
    
    # South Indian languages that support direct processing
    SOUTH_INDIAN_LANGUAGES = ['Tamil', 'Telugu', 'Kannada']
    
    # Language code mappings
    LANGUAGE_CODE_MAP = {
        'Tamil': 'ta',
        'Telugu': 'te', 
        'Kannada': 'kn',
        'Hindi': 'hi',
        'English': 'en',
        'Arabic': 'ar',
        'Chinese': 'zh'
    }
    
    def __init__(self):
        """Initialize the language-aware processor."""
        self.translation_service = translation_service if TRANSLATION_AVAILABLE else None
        self.cache = {}  # Simple cache for language detection results
        
    def detect_query_language(self, query: str) -> Dict[str, Any]:
        """
        Detect the language of the incoming user query.
        
        Args:
            query: User's query text
            
        Returns:
            Dict containing language detection results
        """
        if not query or not query.strip():
            return {
                'language': 'English',
                'language_code': 'en',
                'confidence': 0.0,
                'method': 'default'
            }
        
        # Check cache first
        cache_key = f"query_lang_{hash(query)}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        result = {
            'language': 'English',
            'language_code': 'en', 
            'confidence': 0.0,
            'method': 'unicode_pattern'
        }
        
        try:
            if LANGUAGE_UTILS_AVAILABLE:
                # Use language utils for detection
                detected_language = detect_language_from_text(query)
                language_stats = get_language_statistics(query)
                
                result['language'] = detected_language
                result['language_code'] = self.LANGUAGE_CODE_MAP.get(detected_language, 'en')
                result['statistics'] = language_stats
                
                # Calculate confidence based on character ratio
                if language_stats and detected_language in language_stats:
                    result['confidence'] = language_stats[detected_language]
                
            elif self.translation_service:
                # Fallback to translation service detection
                detected_code = self.translation_service.detect_language(query)
                result['language_code'] = detected_code
                result['language'] = self._code_to_language_name(detected_code)
                result['method'] = 'translation_service'
                
            else:
                # Basic Unicode pattern detection as last resort
                result.update(self._basic_language_detection(query))
                
        except Exception as e:
            print(f"❌ Error in query language detection: {e}")
            result['error'] = str(e)
        
        # Cache the result
        self.cache[cache_key] = result
        return result
    
    def detect_csv_language(self, csv_data: str = None, dataframe: pd.DataFrame = None, 
                           index_name: str = None) -> Dict[str, Any]:
        """
        Determine the primary language used in CSV file data.
        
        Args:
            csv_data: Raw CSV data as string
            dataframe: Pandas DataFrame (alternative to csv_data)
            index_name: Name of the index for caching
            
        Returns:
            Dict containing CSV language detection results
        """
        result = {
            'language': 'English',
            'language_code': 'en',
            'confidence': 0.0,
            'method': 'default',
            'sample_size': 0
        }
        
        try:
            # Check cache first if index_name provided
            if index_name:
                cache_key = f"csv_lang_{index_name}"
                if cache_key in self.cache:
                    return self.cache[cache_key]
            
            # Convert csv_data to DataFrame if needed
            if dataframe is None and csv_data:
                import io
                dataframe = pd.read_csv(io.StringIO(csv_data))
            
            if dataframe is None or dataframe.empty:
                return result
            
            if LANGUAGE_UTILS_AVAILABLE:
                # Use language utils for DataFrame analysis
                detected_language = detect_language_from_dataframe(dataframe)
                result['language'] = detected_language
                result['language_code'] = self.LANGUAGE_CODE_MAP.get(detected_language, 'en')
                result['method'] = 'dataframe_analysis'
                
                # Get detailed statistics
                sample_texts = self._extract_text_samples(dataframe)
                if sample_texts:
                    combined_text = ' '.join(sample_texts[:100])  # Limit for performance
                    stats = get_language_statistics(combined_text)
                    result['statistics'] = stats
                    result['sample_size'] = len(sample_texts)
                    
                    if detected_language in stats:
                        result['confidence'] = stats[detected_language]
                        
            else:
                # Fallback method using basic detection
                sample_texts = self._extract_text_samples(dataframe)
                if sample_texts:
                    combined_text = ' '.join(sample_texts[:50])
                    result.update(self._basic_language_detection(combined_text))
                    result['sample_size'] = len(sample_texts)
            
            # Cache the result if index_name provided
            if index_name:
                cache_key = f"csv_lang_{index_name}"
                self.cache[cache_key] = result
                
        except Exception as e:
            print(f"❌ Error in CSV language detection: {e}")
            result['error'] = str(e)
        
        return result
    
    def _extract_text_samples(self, dataframe: pd.DataFrame, max_samples: int = 100) -> List[str]:
        """Extract text samples from DataFrame for language analysis."""
        samples = []
        
        for column in dataframe.columns:
            if dataframe[column].dtype == 'object':  # String columns
                sample_values = dataframe[column].dropna().head(max_samples // len(dataframe.columns))
                for value in sample_values:
                    if isinstance(value, str) and len(value.strip()) > 0:
                        samples.append(value.strip())
                        if len(samples) >= max_samples:
                            break
            if len(samples) >= max_samples:
                break
                
        return samples
    
    def _basic_language_detection(self, text: str) -> Dict[str, Any]:
        """Basic language detection using Unicode patterns."""
        result = {
            'language': 'English',
            'language_code': 'en',
            'confidence': 0.0,
            'method': 'basic_unicode'
        }
        
        if not text:
            return result
        
        # Tamil detection
        if re.search(r'[\u0B80-\u0BFF]', text):
            result['language'] = 'Tamil'
            result['language_code'] = 'ta'
            result['confidence'] = 0.8
        # Telugu detection  
        elif re.search(r'[\u0C00-\u0C7F]', text):
            result['language'] = 'Telugu'
            result['language_code'] = 'te'
            result['confidence'] = 0.8
        # Kannada detection
        elif re.search(r'[\u0C80-\u0CFF]', text):
            result['language'] = 'Kannada'
            result['language_code'] = 'kn'
            result['confidence'] = 0.8
        # Hindi detection
        elif re.search(r'[\u0900-\u097F]', text):
            result['language'] = 'Hindi'
            result['language_code'] = 'hi'
            result['confidence'] = 0.8
        
        return result
    
    def _code_to_language_name(self, code: str) -> str:
        """Convert language code to language name."""
        code_to_name = {v: k for k, v in self.LANGUAGE_CODE_MAP.items()}
        return code_to_name.get(code, 'English')

    def should_use_direct_processing(self, query_language: str, csv_language: str) -> bool:
        """
        Determine if direct processing should be used based on language matching logic.

        Args:
            query_language: Detected language of the query
            csv_language: Detected language of the CSV data

        Returns:
            bool: True if direct processing should be used
        """
        # Direct processing conditions:
        # 1. Query language is South Indian AND matches CSV language
        # 2. Both are English (default case)

        if query_language in self.SOUTH_INDIAN_LANGUAGES and query_language == csv_language:
            return True

        if query_language == 'English' and csv_language == 'English':
            return True

        return False

    def process_query_with_language_awareness(self, query: str, index_name: str = None,
                                            csv_data: str = None, dataframe: pd.DataFrame = None,
                                            search_function: callable = None,
                                            **search_kwargs) -> Dict[str, Any]:
        """
        Main method to process queries with language awareness.

        This implements the complete language-aware query processing logic:
        1. Detect query language
        2. Detect CSV language (if available)
        3. Apply appropriate processing strategy
        4. Handle translations as needed

        Args:
            query: User's query text
            index_name: Name of the index to search
            csv_data: Raw CSV data for language detection
            dataframe: DataFrame for language detection
            search_function: Function to perform the actual search
            **search_kwargs: Additional arguments for search function

        Returns:
            Dict containing processing results and metadata
        """
        processing_start_time = datetime.now()

        # Step 1: Detect query language
        print(f"🔍 LANGUAGE-AWARE PROCESSING: Starting analysis for query: '{query[:50]}...'")
        query_lang_info = self.detect_query_language(query)
        query_language = query_lang_info['language']
        query_lang_code = query_lang_info['language_code']

        print(f"🌐 Query Language Detected: {query_language} ({query_lang_code}) - Confidence: {query_lang_info.get('confidence', 0):.3f}")

        # Step 2: Detect CSV language if data is available
        csv_lang_info = None
        csv_language = 'English'  # Default
        csv_lang_code = 'en'

        if csv_data or dataframe or index_name:
            csv_lang_info = self.detect_csv_language(csv_data, dataframe, index_name)
            csv_language = csv_lang_info['language']
            csv_lang_code = csv_lang_info['language_code']

            print(f"📊 CSV Language Detected: {csv_language} ({csv_lang_code}) - Confidence: {csv_lang_info.get('confidence', 0):.3f}")

        # Step 3: Determine processing strategy
        use_direct_processing = self.should_use_direct_processing(query_language, csv_language)

        processing_strategy = "direct" if use_direct_processing else "translation_based"
        print(f"🎯 Processing Strategy: {processing_strategy.upper()}")

        # Initialize result structure
        result = {
            'original_query': query,
            'query_language_info': query_lang_info,
            'csv_language_info': csv_lang_info,
            'processing_strategy': processing_strategy,
            'use_direct_processing': use_direct_processing,
            'processing_start_time': processing_start_time.isoformat(),
            'translations_performed': [],
            'search_results': None,
            'final_response': None,
            'errors': []
        }

        try:
            if use_direct_processing:
                # Direct processing path
                result.update(self._process_direct(query, search_function, **search_kwargs))
            else:
                # Translation-based processing path
                result.update(self._process_with_translation(
                    query, query_lang_code, csv_lang_code, search_function, **search_kwargs
                ))

        except Exception as e:
            error_msg = f"Error in language-aware processing: {str(e)}"
            print(f"❌ {error_msg}")
            result['errors'].append(error_msg)
            result['success'] = False

        # Add processing time
        processing_end_time = datetime.now()
        result['processing_end_time'] = processing_end_time.isoformat()
        result['processing_duration_ms'] = int((processing_end_time - processing_start_time).total_seconds() * 1000)

        return result

    def _process_direct(self, query: str, search_function: callable, **search_kwargs) -> Dict[str, Any]:
        """
        Handle direct processing when query and CSV languages match.

        Args:
            query: Original query
            search_function: Function to perform search
            **search_kwargs: Additional search arguments

        Returns:
            Dict with processing results
        """
        print("✅ DIRECT PROCESSING: Query and CSV languages match - no translation needed")

        result = {
            'search_query': query,
            'query_translated': False,
            'success': True
        }

        try:
            if search_function:
                search_results = search_function(query, **search_kwargs)
                result['search_results'] = search_results
                result['results_count'] = len(search_results) if isinstance(search_results, list) else 0
                print(f"🔍 Direct search completed: {result['results_count']} results found")
            else:
                result['search_results'] = []
                result['results_count'] = 0
                print("⚠️ No search function provided for direct processing")

        except Exception as e:
            error_msg = f"Error in direct processing search: {str(e)}"
            print(f"❌ {error_msg}")
            result['errors'] = [error_msg]
            result['success'] = False

        return result

    def _process_with_translation(self, query: str, query_lang_code: str, csv_lang_code: str,
                                 search_function: callable, **search_kwargs) -> Dict[str, Any]:
        """
        Handle translation-based processing when query and CSV languages differ.

        This method implements the translation workflow:
        1. Translate query from original language to CSV language
        2. Perform search using translated query
        3. Translate results back to original language

        Args:
            query: Original query
            query_lang_code: Query language code
            csv_lang_code: CSV language code
            search_function: Function to perform search
            **search_kwargs: Additional search arguments

        Returns:
            Dict with processing results
        """
        print(f"🌐 TRANSLATION-BASED PROCESSING: {query_lang_code} -> {csv_lang_code} -> {query_lang_code}")

        result = {
            'search_query': query,
            'translated_query': None,
            'query_translated': False,
            'results_translated': False,
            'success': True,
            'translations_performed': []
        }

        if not self.translation_service:
            error_msg = "Translation service not available for translation-based processing"
            print(f"❌ {error_msg}")
            result['errors'] = [error_msg]
            result['success'] = False
            return result

        try:
            # Step 1: Translate query to CSV language
            if query_lang_code != csv_lang_code:
                print(f"🔄 Translating query: {query_lang_code} -> {csv_lang_code}")
                translation_result = self.translation_service.translate_text(
                    query, csv_lang_code, query_lang_code
                )

                if translation_result and translation_result.get('translated_text'):
                    result['translated_query'] = translation_result['translated_text']
                    result['query_translated'] = True
                    result['translations_performed'].append({
                        'type': 'query_translation',
                        'from': query_lang_code,
                        'to': csv_lang_code,
                        'original': query,
                        'translated': result['translated_query'],
                        'provider': translation_result.get('translation_provider', 'unknown')
                    })
                    print(f"✅ Query translated: '{result['translated_query'][:50]}...'")
                else:
                    print("⚠️ Query translation failed, using original query")
                    result['translated_query'] = query
            else:
                result['translated_query'] = query

            # Step 2: Perform search using translated query
            search_query = result['translated_query'] or query
            if search_function:
                search_results = search_function(search_query, **search_kwargs)
                result['search_results'] = search_results
                result['results_count'] = len(search_results) if isinstance(search_results, list) else 0
                print(f"🔍 Translation-based search completed: {result['results_count']} results found")
            else:
                result['search_results'] = []
                result['results_count'] = 0
                print("⚠️ No search function provided for translation-based processing")

            # Step 3: Translate results back to original language if needed
            if (result['search_results'] and csv_lang_code != query_lang_code and
                isinstance(result['search_results'], list)):

                print(f"🔄 Translating results back: {csv_lang_code} -> {query_lang_code}")
                translated_results = self._translate_search_results(
                    result['search_results'], csv_lang_code, query_lang_code
                )

                if translated_results:
                    result['search_results'] = translated_results
                    result['results_translated'] = True
                    result['translations_performed'].append({
                        'type': 'results_translation',
                        'from': csv_lang_code,
                        'to': query_lang_code,
                        'results_count': len(translated_results)
                    })
                    print(f"✅ Results translated back to {query_lang_code}")

        except Exception as e:
            error_msg = f"Error in translation-based processing: {str(e)}"
            print(f"❌ {error_msg}")
            result['errors'] = [error_msg]
            result['success'] = False

        return result

    def _translate_search_results(self, results: List[Dict], from_lang: str, to_lang: str) -> List[Dict]:
        """
        Translate search results from one language to another.

        Args:
            results: List of search result dictionaries
            from_lang: Source language code
            to_lang: Target language code

        Returns:
            List of translated search results
        """
        if not self.translation_service or not results:
            return results

        translated_results = []

        for result in results:
            try:
                translated_result = result.copy()

                # Translate text fields that are commonly present in search results
                text_fields = ['text', 'content', 'chunk_text', 'summary', 'title', 'description']

                for field in text_fields:
                    if field in result and result[field] and isinstance(result[field], str):
                        translation_result = self.translation_service.translate_text(
                            result[field], to_lang, from_lang
                        )

                        if translation_result and translation_result.get('translated_text'):
                            translated_result[field] = translation_result['translated_text']
                            # Keep original for reference
                            translated_result[f'original_{field}'] = result[field]

                translated_results.append(translated_result)

            except Exception as e:
                print(f"⚠️ Error translating individual result: {e}")
                # Include original result if translation fails
                translated_results.append(result)

        return translated_results

    def translate_response_data(self, response_data: Dict[str, Any], target_language: str) -> Dict[str, Any]:
        """
        Translate response data to target language while preserving structure.

        Args:
            response_data: Response data dictionary
            target_language: Target language code

        Returns:
            Translated response data
        """
        if not self.translation_service:
            return response_data

        try:
            # Use existing translation service method if available
            if hasattr(self.translation_service, 'translate_response_data'):
                return self.translation_service.translate_response_data(response_data, target_language)

            # Fallback: translate key fields manually
            translated_data = response_data.copy()

            # Fields that commonly need translation
            translatable_fields = [
                'ai_response', 'summary', 'description', 'content',
                'related_questions', 'error_message'
            ]

            for field in translatable_fields:
                if field in response_data and response_data[field]:
                    if isinstance(response_data[field], str):
                        # Translate string field
                        translation_result = self.translation_service.translate_text(
                            response_data[field], target_language
                        )
                        if translation_result and translation_result.get('translated_text'):
                            translated_data[field] = translation_result['translated_text']

                    elif isinstance(response_data[field], list) and field == 'related_questions':
                        # Translate list of questions
                        translated_questions = []
                        for question in response_data[field]:
                            if isinstance(question, str):
                                translation_result = self.translation_service.translate_text(
                                    question, target_language
                                )
                                if translation_result and translation_result.get('translated_text'):
                                    translated_questions.append(translation_result['translated_text'])
                                else:
                                    translated_questions.append(question)
                        translated_data[field] = translated_questions

            return translated_data

        except Exception as e:
            print(f"❌ Error translating response data: {e}")
            return response_data

    def get_processing_summary(self, processing_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a summary of the language-aware processing results.

        Args:
            processing_result: Result from process_query_with_language_awareness

        Returns:
            Processing summary dictionary
        """
        summary = {
            'query_language': processing_result.get('query_language_info', {}).get('language', 'Unknown'),
            'csv_language': processing_result.get('csv_language_info', {}).get('language', 'Unknown'),
            'processing_strategy': processing_result.get('processing_strategy', 'unknown'),
            'direct_processing_used': processing_result.get('use_direct_processing', False),
            'translations_count': len(processing_result.get('translations_performed', [])),
            'query_translated': processing_result.get('query_translated', False),
            'results_translated': processing_result.get('results_translated', False),
            'processing_duration_ms': processing_result.get('processing_duration_ms', 0),
            'success': processing_result.get('success', False),
            'errors_count': len(processing_result.get('errors', []))
        }

        # Add language confidence scores
        if processing_result.get('query_language_info'):
            summary['query_language_confidence'] = processing_result['query_language_info'].get('confidence', 0)

        if processing_result.get('csv_language_info'):
            summary['csv_language_confidence'] = processing_result['csv_language_info'].get('confidence', 0)

        # Add translation details
        if processing_result.get('translations_performed'):
            summary['translation_details'] = []
            for translation in processing_result['translations_performed']:
                summary['translation_details'].append({
                    'type': translation.get('type'),
                    'from_language': translation.get('from'),
                    'to_language': translation.get('to'),
                    'provider': translation.get('provider', 'unknown')
                })

        return summary

    def clear_cache(self):
        """Clear the language detection cache."""
        self.cache.clear()
        print("🧹 Language detection cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            'cache_size': len(self.cache),
            'cache_keys': list(self.cache.keys())
        }


# Global instance for easy access
language_aware_processor = LanguageAwareProcessor()


def process_language_aware_query(query: str, index_name: str = None,
                                csv_data: str = None, dataframe: pd.DataFrame = None,
                                search_function: callable = None, **search_kwargs) -> Dict[str, Any]:
    """
    Convenience function for language-aware query processing.

    Args:
        query: User's query text
        index_name: Name of the index to search
        csv_data: Raw CSV data for language detection
        dataframe: DataFrame for language detection
        search_function: Function to perform the actual search
        **search_kwargs: Additional arguments for search function

    Returns:
        Dict containing processing results and metadata
    """
    return language_aware_processor.process_query_with_language_awareness(
        query=query,
        index_name=index_name,
        csv_data=csv_data,
        dataframe=dataframe,
        search_function=search_function,
        **search_kwargs
    )
