# Language-Aware Query Processing System

## Overview

The Language-Aware Query Processing System is a comprehensive solution that intelligently handles multilingual queries and data processing. It automatically detects languages, applies appropriate processing strategies, and ensures optimal search results while maintaining the user's preferred response language.

## Key Features

### 1. **Dual Language Detection**
- **Query Language Detection**: Automatically identifies the language of incoming user queries
- **CSV Language Detection**: Analyzes CSV file content to determine the primary language used in the data
- **High Accuracy**: Uses Unicode character patterns and statistical analysis for reliable detection
- **Caching**: Implements intelligent caching to improve performance for repeated operations

### 2. **Intelligent Processing Logic**
- **Direct Processing**: When query language matches CSV language (especially for South Indian languages)
- **Translation-Based Processing**: When languages differ, with automatic translation workflows
- **South Indian Language Priority**: Special handling for Tamil, Telugu, and Kannada languages
- **Fallback Mechanisms**: Robust error handling with graceful degradation

### 3. **Translation Workflows**
- **Query Translation**: Translates user queries to match CSV data language for better search accuracy
- **Result Translation**: Translates search results back to user's preferred language
- **Bidirectional Support**: Handles translation in both directions seamlessly
- **Provider Integration**: Works with multiple translation services (MyMemory, LibreTranslate, etc.)

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Query    │───▶│  Language-Aware  │───▶│  Search Results │
│                 │    │    Processor     │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │  Processing Flow │
                    └──────────────────┘
                              │
                    ┌─────────▼─────────┐
                    │                   │
            ┌───────▼────────┐  ┌───────▼────────┐
            │ Direct         │  │ Translation    │
            │ Processing     │  │ Based          │
            │                │  │ Processing     │
            └────────────────┘  └────────────────┘
```

## Processing Strategies

### Direct Processing
**When to Use**: Query language matches CSV language AND both are South Indian languages (Tamil, Telugu, Kannada) OR both are English.

**Process**:
1. Detect query language
2. Detect CSV language  
3. Confirm language match
4. Perform direct search without translation
5. Return results in original language

**Benefits**:
- Faster processing (no translation overhead)
- Higher accuracy (no translation artifacts)
- Preserves linguistic nuances

### Translation-Based Processing
**When to Use**: Query language differs from CSV language.

**Process**:
1. Detect query language
2. Detect CSV language
3. Translate query from original → CSV language
4. Perform search using translated query
5. Translate results from CSV language → original language
6. Return results in user's preferred language

**Benefits**:
- Cross-language search capability
- Maintains user experience in preferred language
- Comprehensive multilingual support

## Implementation Details

### Core Classes

#### `LanguageAwareProcessor`
Main processing class that orchestrates the entire workflow.

**Key Methods**:
- `detect_query_language(query)`: Detects language of user queries
- `detect_csv_language(csv_data/dataframe)`: Analyzes CSV content language
- `process_query_with_language_awareness()`: Main processing method
- `should_use_direct_processing()`: Determines processing strategy

#### Language Detection
```python
# Query language detection
query_info = processor.detect_query_language("வணக்கம், எப்படி இருக்கீங்க?")
# Returns: {'language': 'Tamil', 'language_code': 'ta', 'confidence': 0.95}

# CSV language detection  
csv_info = processor.detect_csv_language(dataframe=df)
# Returns: {'language': 'Tamil', 'language_code': 'ta', 'confidence': 0.87}
```

### Integration Points

#### With Existing Query Handler
The system integrates seamlessly with the existing `handle_query()` function in `full_code.py`:

```python
# Language-aware processing
if language_aware_available:
    language_processing_result = language_aware_processor.process_query_with_language_awareness(
        query=query,
        index_name=index_name,
        search_function=search_wrapper
    )
```

#### With CSV Upload Process
Language detection is performed during CSV upload to cache language information:

```python
# Detect CSV language during upload
csv_language_info = language_aware_processor.detect_csv_language(
    dataframe=df, 
    index_name=index_name
)
```

## Configuration

### Supported Languages
- **Tamil** (ta): தமிழ்
- **Telugu** (te): తెలుగు  
- **Kannada** (kn): ಕನ್ನಡ
- **Hindi** (hi): हिन्दी
- **English** (en): English
- **Arabic** (ar): العربية
- **Chinese** (zh): 中文

### Language Code Mapping
```python
LANGUAGE_CODE_MAP = {
    'Tamil': 'ta',
    'Telugu': 'te', 
    'Kannada': 'kn',
    'Hindi': 'hi',
    'English': 'en',
    'Arabic': 'ar',
    'Chinese': 'zh'
}
```

## Usage Examples

### Basic Usage
```python
from services.language_aware_processor import language_aware_processor

# Process a Tamil query
result = language_aware_processor.process_query_with_language_awareness(
    query="பங்குச் சந்தை செய்தி என்ன?",
    index_name="financial_data",
    search_function=my_search_function
)

print(f"Strategy: {result['processing_strategy']}")
print(f"Success: {result['success']}")
print(f"Results: {len(result['search_results'])}")
```

### With CSV Language Detection
```python
import pandas as pd

# Create DataFrame with Tamil content
df = pd.DataFrame({
    'தலைப்பு': ['பங்குச் சந்தை செய்தி', 'பொருளாதார அறிக்கை'],
    'உள்ளடக்கம்': ['சந்தை நன்றாக செயல்பட்டது', 'வளர்ச்சி குறிகாட்டிகள்']
})

# Process with language awareness
result = language_aware_processor.process_query_with_language_awareness(
    query="What is the market performance?",  # English query
    dataframe=df,  # Tamil data
    search_function=search_function
)

# This will use translation-based processing
print(f"Strategy: {result['processing_strategy']}")  # "translation_based"
print(f"Translations: {len(result['translations_performed'])}")  # 2 (query + results)
```

## Response Format

### Language Processing Metadata
The system adds comprehensive metadata to API responses:

```json
{
  "query": "Original user query",
  "translated_query": "Translated query if applicable",
  "language_processing": {
    "enabled": true,
    "query_language": "Tamil",
    "csv_language": "English", 
    "processing_strategy": "translation_based",
    "direct_processing_used": false,
    "translations_performed": [
      {
        "type": "query_translation",
        "from": "ta",
        "to": "en",
        "provider": "mymemory_api"
      }
    ],
    "processing_duration_ms": 1250,
    "success": true,
    "query_language_confidence": 0.95,
    "csv_language_confidence": 0.87
  }
}
```

## Performance Considerations

### Caching Strategy
- **Language Detection Cache**: Stores detection results to avoid repeated analysis
- **Translation Cache**: Caches translation results for frequently used phrases
- **CSV Language Cache**: Persists CSV language detection results by index name

### Optimization Features
- **Lazy Loading**: Services are loaded only when needed
- **Batch Processing**: Efficient handling of multiple translations
- **Fallback Mechanisms**: Graceful degradation when services are unavailable
- **Confidence Thresholds**: Configurable thresholds for language detection accuracy

## Error Handling

### Graceful Degradation
- Falls back to legacy processing if language-aware processor fails
- Continues with original query if translation fails
- Provides detailed error information in responses
- Maintains system stability even with service failures

### Edge Cases
- Empty or null queries
- Mixed language content
- Uncertain language detection
- Translation service unavailability
- Malformed CSV data

## Testing

### Test Coverage
The system includes comprehensive tests for:
- Language detection accuracy
- Processing strategy selection
- Translation workflows
- Edge cases and error conditions
- Performance benchmarks

### Running Tests
```bash
cd python-fiass-backend
python test_language_aware_processing.py
```

## Future Enhancements

### Planned Features
- **Machine Learning Integration**: Advanced language detection using ML models
- **Custom Language Models**: Support for domain-specific language detection
- **Real-time Language Learning**: Adaptive detection based on user patterns
- **Advanced Translation**: Context-aware translation with domain knowledge
- **Performance Analytics**: Detailed metrics and optimization insights

### Extensibility
The system is designed for easy extension:
- Plugin architecture for new translation providers
- Configurable language detection algorithms
- Customizable processing strategies
- Flexible caching mechanisms
