#!/usr/bin/env python3
"""
Test script to check AI response generation and identify the source of corrupted output.
"""

import sys
import os

# Add the python-fiass-backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

def test_ai_response_generation():
    """Test AI response generation with Tamil content"""
    
    print("🧪 TESTING AI RESPONSE GENERATION")
    print("=" * 50)
    
    try:
        # Import the generate_response function
        from full_code import generate_response
        
        # Create mock context documents with Tamil content
        mock_docs = [
            {
                'metadata': {
                    'chunk_text': 'புதுக்கோட்டை மாவட்டத்தில் மின்தடை பிரச்சனை காரணமாக விவசாயிகள் போராட்டம் நடத்தினர்.',
                    'source_type': 'article',
                    'url': 'https://example.com/news1',
                    'title': 'புதுக்கோட்டை மின்தடை செய்தி'
                }
            },
            {
                'metadata': {
                    'chunk_text': 'போலீசார் மற்றும் விவசாயிகள் இடையே மோதல் ஏற்பட்டது. பல பேர் காயமடைந்தனர்.',
                    'source_type': 'article', 
                    'url': 'https://example.com/news2',
                    'title': 'போலீசார் விவசாயிகள் மோதல்'
                }
            }
        ]
        
        # Test Tamil query
        tamil_query = "புதுக்கோட்டை மின்தடை பற்றி என்ன நடந்தது?"
        
        print(f"📝 Testing with Tamil query: {tamil_query}")
        print(f"📊 Mock documents: {len(mock_docs)} documents")
        
        # Generate response in Tamil
        print("\n🤖 Generating AI response in Tamil...")
        ai_response = generate_response(tamil_query, mock_docs, "Tamil")
        
        print(f"✅ AI Response generated:")
        print(f"Response length: {len(ai_response)} characters")
        print(f"Response preview: {ai_response[:200]}...")
        
        # Check for corruption patterns
        corruption_indicators = [
            "கொழுப்பு",
            "தகவல்தொடர்புகள்",
            "குறும்படம் கேலக்",
            "எக்ஸெல்:",
            "வெளியீடு:"
        ]
        
        is_corrupted = any(indicator in ai_response for indicator in corruption_indicators)
        
        if is_corrupted:
            print("❌ CORRUPTION DETECTED in AI response!")
            for indicator in corruption_indicators:
                if indicator in ai_response:
                    print(f"   - Found corruption pattern: '{indicator}'")
        else:
            print("✅ No corruption patterns detected in AI response")
            
        # Test English response for comparison
        print("\n🤖 Generating AI response in English for comparison...")
        ai_response_en = generate_response("What happened regarding power outage in Pudukkottai?", mock_docs, "English")
        
        print(f"✅ English AI Response generated:")
        print(f"Response length: {len(ai_response_en)} characters")
        print(f"Response preview: {ai_response_en[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_deepseek_api_directly():
    """Test DeepSeek API directly to isolate the issue"""
    
    print("\n🧪 TESTING DEEPSEEK API DIRECTLY")
    print("=" * 50)
    
    try:
        from full_code import deepseek_client, DEEPSEEK_API_KEY
        
        if not DEEPSEEK_API_KEY:
            print("❌ DEEPSEEK_API_KEY not found")
            return False
            
        print("🔑 DeepSeek API key found")
        
        # Test simple Tamil prompt
        messages = [
            {"role": "system", "content": "நீங்கள் ஒரு உதவிகரமான உதவியாளர். தமிழில் மட்டுமே பதிலளியுங்கள்."},
            {"role": "user", "content": "புதுக்கோட்டை மாவட்டத்தில் மின்தடை பிரச்சனை பற்றி சுருக்கமாக கூறுங்கள்."}
        ]
        
        print("📤 Sending request to DeepSeek API...")
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            stream=False
        )
        
        api_response = response.choices[0].message.content
        print(f"✅ DeepSeek API response received:")
        print(f"Response length: {len(api_response)} characters")
        print(f"Response: {api_response}")
        
        # Check for corruption in direct API response
        corruption_indicators = [
            "கொழுப்பு",
            "தகவல்தொடர்புகள்", 
            "குறும்படம் கேலக்",
            "எக்ஸெல்:",
            "வெளியீடு:"
        ]
        
        is_corrupted = any(indicator in api_response for indicator in corruption_indicators)
        
        if is_corrupted:
            print("❌ CORRUPTION DETECTED in DeepSeek API response!")
        else:
            print("✅ No corruption in DeepSeek API response")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing DeepSeek API: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 DIAGNOSING AI RESPONSE CORRUPTION")
    print("=" * 60)
    
    # Test AI response generation
    success1 = test_ai_response_generation()
    
    # Test DeepSeek API directly
    success2 = test_deepseek_api_directly()
    
    print("\n" + "=" * 60)
    print("🏁 DIAGNOSIS COMPLETE")
    
    if success1 and success2:
        print("✅ All tests completed successfully")
    else:
        print("❌ Some tests failed - check the output above for details")
