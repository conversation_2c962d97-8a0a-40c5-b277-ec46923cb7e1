#!/usr/bin/env python3
"""
Test script to verify that direct processing works correctly when query and dataset languages match.
This tests the core requirement: "if both the query and the dataset are same languages dont use translators, instead just search and retrieve them"
"""

import requests
import json

def test_direct_processing():
    """Test direct processing with Tamil query and Tamil dataset"""
    
    # Test endpoint
    url = "http://localhost:5010/financial_query"
    
    # Test case 1: Tamil query (should use direct processing if Tamil dataset exists)
    tamil_test = {
        "query": "பங்குச் சந்தை செய்தி என்ன?",  # "What is the stock market news?"
        "language": "Tamil",
        "index_name": "default"
    }
    
    print("🧪 TESTING DIRECT PROCESSING")
    print("=" * 50)
    
    try:
        print(f"📤 Sending Tamil query: {tamil_test['query']}")
        response = requests.post(url, json=tamil_test)
        
        if response.status_code == 200:
            data = response.json()
            
            # Check if language-aware processing was used
            if "language_processing" in data:
                lang_proc = data["language_processing"]
                print(f"✅ Language-aware processing: {lang_proc.get('enabled', False)}")
                print(f"🔍 Query Language: {lang_proc.get('query_language', 'Unknown')}")
                print(f"📊 CSV Language: {lang_proc.get('csv_language', 'Unknown')}")
                print(f"🎯 Processing Strategy: {lang_proc.get('processing_strategy', 'unknown')}")
                print(f"⚡ Direct Processing Used: {lang_proc.get('direct_processing_used', False)}")
                print(f"🌐 Translations Performed: {len(lang_proc.get('translations_performed', []))}")
                print(f"⏱️ Processing Duration: {lang_proc.get('processing_duration_ms', 0)}ms")
                
                # Check if direct processing was used correctly
                if lang_proc.get('direct_processing_used'):
                    print("🎉 SUCCESS: Direct processing was used (no translation needed)!")
                else:
                    print("⚠️ WARNING: Translation-based processing was used instead of direct processing")
                    
                # Check if any translations were performed (should be 0 for direct processing)
                translations = lang_proc.get('translations_performed', [])
                if len(translations) == 0:
                    print("✅ SUCCESS: No translations were performed (as expected for direct processing)")
                else:
                    print(f"⚠️ WARNING: {len(translations)} translations were performed:")
                    for trans in translations:
                        print(f"   - {trans.get('type', 'unknown')}: {trans.get('from', '?')} -> {trans.get('to', '?')}")
                        
            else:
                print("⚠️ Language-aware processing metadata not found in response")
                
            # Check if query was translated
            if data.get('query_translated'):
                print(f"⚠️ WARNING: Query was translated from '{data.get('query')}' to '{data.get('translated_query')}'")
            else:
                print("✅ SUCCESS: Query was not translated (direct processing)")
                
            print(f"🔍 Search Results: {len(data.get('retrieved_documents', []))} documents found")
            
        else:
            print(f"❌ ERROR: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 TEST COMPLETED")

if __name__ == "__main__":
    test_direct_processing()
