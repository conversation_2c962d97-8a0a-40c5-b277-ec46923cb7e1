#!/usr/bin/env python3
"""
Test script for Language-Aware Query Processing System

This script demonstrates the functionality of the language-aware query processing system
with various test scenarios including:
1. Direct processing for matching languages
2. Translation-based processing for different languages
3. Edge cases and error handling
"""

import sys
import os
import pandas as pd
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_language_detection():
    """Test language detection functionality."""
    print("=" * 60)
    print("TESTING LANGUAGE DETECTION")
    print("=" * 60)
    
    try:
        from services.language_aware_processor import language_aware_processor
        
        # Test queries in different languages
        test_queries = [
            ("Hello, how are you?", "English"),
            ("வணக்கம், எப்படி இருக்கீங்க?", "Tamil"),
            ("నమస్కారం, ఎలా ఉన్నారు?", "Telugu"),
            ("ನಮಸ್ಕಾರ, ಹೇಗಿದ್ದೀರಿ?", "Kannada"),
            ("नमस्ते, आप कैसे हैं?", "Hindi"),
            ("What is the stock market performance?", "English"),
            ("பங்குச் சந்தையின் செயல்திறன் என்ன?", "Tamil")
        ]
        
        for query, expected_lang in test_queries:
            print(f"\nQuery: {query}")
            print(f"Expected: {expected_lang}")
            
            result = language_aware_processor.detect_query_language(query)
            detected_lang = result.get('language', 'Unknown')
            confidence = result.get('confidence', 0)
            
            print(f"Detected: {detected_lang} (confidence: {confidence:.3f})")
            print(f"Match: {'✅' if detected_lang == expected_lang else '❌'}")
            
    except ImportError as e:
        print(f"❌ Language-aware processor not available: {e}")
    except Exception as e:
        print(f"❌ Error in language detection test: {e}")

def test_csv_language_detection():
    """Test CSV language detection functionality."""
    print("\n" + "=" * 60)
    print("TESTING CSV LANGUAGE DETECTION")
    print("=" * 60)
    
    try:
        from services.language_aware_processor import language_aware_processor
        
        # Create test DataFrames with different languages
        test_data = [
            {
                'name': 'English CSV',
                'data': pd.DataFrame({
                    'Title': ['Stock Market Update', 'Economic News', 'Financial Report'],
                    'Content': ['The stock market performed well today', 'Economic indicators show growth', 'Quarterly financial results released'],
                    'Category': ['Finance', 'Economics', 'Business']
                }),
                'expected': 'English'
            },
            {
                'name': 'Tamil CSV',
                'data': pd.DataFrame({
                    'தலைப்பு': ['பங்குச் சந்தை செய்தி', 'பொருளாதார அறிக்கை', 'நிதி தகவல்'],
                    'உள்ளடக்கம்': ['பங்குச் சந்தை இன்று நன்றாக செயல்பட்டது', 'பொருளாதார குறிகாட்டிகள் வளர்ச்சியைக் காட்டுகின்றன', 'காலாண்டு நிதி முடிவுகள் வெளியிடப்பட்டன'],
                    'வகை': ['நிதி', 'பொருளாதாரம்', 'வணிகம்']
                }),
                'expected': 'Tamil'
            }
        ]
        
        for test_case in test_data:
            print(f"\nTesting: {test_case['name']}")
            print(f"Expected: {test_case['expected']}")
            
            result = language_aware_processor.detect_csv_language(dataframe=test_case['data'])
            detected_lang = result.get('language', 'Unknown')
            confidence = result.get('confidence', 0)
            sample_size = result.get('sample_size', 0)
            
            print(f"Detected: {detected_lang} (confidence: {confidence:.3f}, samples: {sample_size})")
            print(f"Match: {'✅' if detected_lang == test_case['expected'] else '❌'}")
            
    except ImportError as e:
        print(f"❌ Language-aware processor not available: {e}")
    except Exception as e:
        print(f"❌ Error in CSV language detection test: {e}")

def test_processing_strategies():
    """Test different processing strategies."""
    print("\n" + "=" * 60)
    print("TESTING PROCESSING STRATEGIES")
    print("=" * 60)
    
    try:
        from services.language_aware_processor import language_aware_processor
        
        # Mock search function for testing
        def mock_search_function(query, **kwargs):
            return [
                {
                    'text': f'Mock result for query: {query}',
                    'score': 0.95,
                    'metadata': {'source': 'test_data'}
                }
            ]
        
        # Test scenarios
        test_scenarios = [
            {
                'name': 'Direct Processing - Tamil to Tamil',
                'query': 'பங்குச் சந்தை செய்தி',
                'csv_language': 'Tamil',
                'expected_strategy': 'direct'
            },
            {
                'name': 'Translation Processing - English to Tamil',
                'query': 'Stock market news',
                'csv_language': 'Tamil',
                'expected_strategy': 'translation_based'
            },
            {
                'name': 'Direct Processing - English to English',
                'query': 'Stock market news',
                'csv_language': 'English',
                'expected_strategy': 'direct'
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\nScenario: {scenario['name']}")
            print(f"Query: {scenario['query']}")
            print(f"CSV Language: {scenario['csv_language']}")
            print(f"Expected Strategy: {scenario['expected_strategy']}")
            
            # Create mock CSV data
            if scenario['csv_language'] == 'Tamil':
                mock_df = pd.DataFrame({
                    'தலைப்பு': ['பங்குச் சந்தை செய்தி'],
                    'உள்ளடக்கம்': ['பங்குச் சந்தை இன்று நன்றாக செயல்பட்டது']
                })
            else:
                mock_df = pd.DataFrame({
                    'Title': ['Stock Market News'],
                    'Content': ['The stock market performed well today']
                })
            
            # Process with language awareness
            result = language_aware_processor.process_query_with_language_awareness(
                query=scenario['query'],
                dataframe=mock_df,
                search_function=mock_search_function
            )
            
            strategy = result.get('processing_strategy', 'unknown')
            success = result.get('success', False)
            
            print(f"Actual Strategy: {strategy}")
            print(f"Success: {'✅' if success else '❌'}")
            print(f"Strategy Match: {'✅' if strategy == scenario['expected_strategy'] else '❌'}")
            
            if result.get('translations_performed'):
                print(f"Translations: {len(result['translations_performed'])}")
                for translation in result['translations_performed']:
                    print(f"  - {translation.get('type', 'unknown')}: {translation.get('from', '?')} -> {translation.get('to', '?')}")
            
    except ImportError as e:
        print(f"❌ Language-aware processor not available: {e}")
    except Exception as e:
        print(f"❌ Error in processing strategy test: {e}")

def test_edge_cases():
    """Test edge cases and error handling."""
    print("\n" + "=" * 60)
    print("TESTING EDGE CASES")
    print("=" * 60)
    
    try:
        from services.language_aware_processor import language_aware_processor
        
        # Test empty query
        print("\nTesting empty query:")
        result = language_aware_processor.detect_query_language("")
        print(f"Empty query result: {result}")
        
        # Test None query
        print("\nTesting None query:")
        result = language_aware_processor.detect_query_language(None)
        print(f"None query result: {result}")
        
        # Test empty DataFrame
        print("\nTesting empty DataFrame:")
        empty_df = pd.DataFrame()
        result = language_aware_processor.detect_csv_language(dataframe=empty_df)
        print(f"Empty DataFrame result: {result}")
        
        # Test mixed language query
        print("\nTesting mixed language query:")
        mixed_query = "Hello வணக்கம் how are you எப்படி இருக்கீங்க?"
        result = language_aware_processor.detect_query_language(mixed_query)
        print(f"Mixed language result: {result}")
        
    except ImportError as e:
        print(f"❌ Language-aware processor not available: {e}")
    except Exception as e:
        print(f"❌ Error in edge cases test: {e}")

def main():
    """Run all tests."""
    print("🚀 LANGUAGE-AWARE QUERY PROCESSING SYSTEM TESTS")
    print(f"Test started at: {datetime.now().isoformat()}")
    
    # Run all test functions
    test_language_detection()
    test_csv_language_detection()
    test_processing_strategies()
    test_edge_cases()
    
    print("\n" + "=" * 60)
    print("ALL TESTS COMPLETED")
    print("=" * 60)
    print(f"Test completed at: {datetime.now().isoformat()}")

if __name__ == "__main__":
    main()
