{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/services/fileUploadService.ts"], "sourcesContent": ["/**\r\n * Format file size to human-readable format\r\n * @param {number} bytes - File size in bytes\r\n * @returns {string} - Formatted file size (e.g., \"2.5 MB\")\r\n */\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Upload a CSV file to FAISS via the backend\r\n * @param {File} file - The CSV file to upload\r\n * @param {string} clientEmail - Client email identifier\r\n * @param {string} indexName - Name for the FAISS index\r\n * @param {string} updateMode - Update mode ('update' or 'new') (optional)\r\n * @param {AbortSignal} signal - AbortSignal for cancelling the upload (optional)\r\n * @param {Function} onProgress - Callback function for upload progress (optional)\r\n * @param {string} embedModel - Name of the embedding model to use (optional)\r\n * @returns {Promise} - Promise that resolves with the server response including upload_id\r\n */\r\nexport const uploadCSVToFaiss = async (\r\n  file: File,\r\n  clientEmail: string,\r\n  indexName: string,\r\n  updateMode?: string | null,\r\n  signal?: AbortSignal,\r\n  onProgress?: (progress: number) => void,\r\n  embedModel?: string\r\n): Promise<any> => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // Validate file type\r\n      if (file.type !== 'text/csv') {\r\n        reject(new Error('Only CSV files are supported for FAISS upload'));\r\n        return;\r\n      }\r\n\r\n      // Create a new FormData instance\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n\r\n      // Add client information to form data if provided\r\n      if (clientEmail) {\r\n        formData.append('client', clientEmail);\r\n      }\r\n\r\n      // Add index name to form data\r\n      if (indexName) {\r\n        formData.append('index_name', indexName);\r\n      }\r\n\r\n      // Add index name to form data\r\n      if (indexName) {\r\n        formData.append('index_name', indexName);\r\n      }\r\n\r\n      // Add update mode to form data if provided\r\n      if (updateMode) {\r\n        formData.append('update_mode', updateMode);\r\n      }\r\n\r\n      // Add embedding model to form data if provided\r\n      if (embedModel) {\r\n        formData.append('embed_model', embedModel);\r\n      }\r\n\r\n      // Create a new XMLHttpRequest and connect abort signal\r\n      const xhr = new XMLHttpRequest();\r\n\r\n      // Handle abort signal for client-side cancellation\r\n      if (signal) {\r\n        signal.onabort = () => {\r\n          xhr.abort();\r\n          // Instead of rejecting with an error, resolve with a cancellation object\r\n          // This prevents the error from appearing in the console\r\n          resolve({\r\n            success: false,\r\n            cancelled: true,\r\n            message: 'Upload cancelled by user'\r\n          });\r\n        };\r\n      }\r\n\r\n      // Configure the request to our backend endpoint\r\n      xhr.open('POST', 'http://localhost:5010/api/upload-csv', true);\r\n      // Add authentication header only (no Content-Type for FormData)\r\n      xhr.setRequestHeader('xxxid', 'FAISS');\r\n\r\n      // Track upload progress if callback provided\r\n      if (onProgress) {\r\n        xhr.upload.onprogress = (event) => {\r\n          if (event.lengthComputable) {\r\n            const progress = Math.round((event.loaded / event.total) * 100);\r\n            onProgress(progress);\r\n          }\r\n        };\r\n      }\r\n\r\n      // Handle response\r\n      xhr.onload = () => {\r\n        if (xhr.status >= 200 && xhr.status < 300) {\r\n          try {\r\n            const response = JSON.parse(xhr.responseText);\r\n            resolve(response);\r\n          } catch (error) {\r\n            resolve({\r\n              success: true,\r\n              message: 'CSV file uploaded successfully to FAISS',\r\n              indexName: indexName // Use the user-provided index name\r\n            });\r\n          }\r\n        } else {\r\n          reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));\r\n        }\r\n      };\r\n\r\n      // Handle network errors\r\n      xhr.onerror = () => {\r\n        reject(new Error('Network error occurred while uploading CSV file'));\r\n      };\r\n\r\n      // Send the request\r\n      xhr.send(formData);\r\n    } catch (error) {\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n// Compatibility alias for existing code\r\nexport const uploadCSVToPinecone = uploadCSVToFaiss;\r\n\r\n/**\r\n * Upload a single file to the server\r\n * @param {File} file - The file to upload\r\n * @param {Function} onProgress - Callback function for upload progress\r\n * @returns {Promise} - Promise that resolves with the server response\r\n */\r\nexport const uploadFile = async (\r\n  file: File,\r\n  onProgress?: (progress: number) => void\r\n): Promise<any> => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // Create a new FormData instance\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n\r\n      // Create a new XMLHttpRequest\r\n      const xhr = new XMLHttpRequest();\r\n\r\n      // Configure the request\r\n      xhr.open('POST', 'http://localhost:5010/api/upload', true);\r\n\r\n      // Track upload progress\r\n      xhr.upload.onprogress = (event) => {\r\n        if (event.lengthComputable && onProgress) {\r\n          const progress = Math.round((event.loaded / event.total) * 100);\r\n          onProgress(progress);\r\n        }\r\n      };\r\n\r\n      // Handle response\r\n      xhr.onload = () => {\r\n        if (xhr.status >= 200 && xhr.status < 300) {\r\n          try {\r\n            const response = JSON.parse(xhr.responseText);\r\n            resolve(response);\r\n          } catch (error) {\r\n            resolve({\r\n              success: true,\r\n              message: 'File uploaded successfully',\r\n              fileName: file.name\r\n            });\r\n          }\r\n        } else {\r\n          reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));\r\n        }\r\n      };\r\n\r\n      // Handle network errors\r\n      xhr.onerror = () => {\r\n        reject(new Error('Network error occurred while uploading file'));\r\n      };\r\n\r\n      // Send the request\r\n      xhr.send(formData);\r\n    } catch (error) {\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Upload multiple files to the server\r\n * @param {File[]} files - Array of files to upload\r\n * @param {Function} onProgress - Callback function for upload progress\r\n * @returns {Promise} - Promise that resolves with an array of server responses\r\n */\r\nexport const uploadMultipleFiles = async (\r\n  files: File[],\r\n  onProgress?: (fileName: string, progress: number) => void\r\n): Promise<any[]> => {\r\n  const uploadPromises = files.map((file) => {\r\n    return uploadFile(file, (progress) => {\r\n      if (onProgress) {\r\n        onProgress(file.name, progress);\r\n      }\r\n    });\r\n  });\r\n\r\n  return Promise.all(uploadPromises);\r\n};\r\n\r\n/**\r\n * Check if a file type is allowed\r\n * @param {string} fileType - MIME type of the file\r\n * @param {string[]} allowedTypes - Array of allowed MIME types\r\n * @returns {boolean} - True if the file type is allowed\r\n */\r\nexport const isFileTypeAllowed = (fileType: string, allowedTypes: string[]): boolean => {\r\n  return allowedTypes.includes(fileType);\r\n};\r\n\r\n/**\r\n * Check if a file size is within the limit\r\n * @param {number} fileSize - Size of the file in bytes\r\n * @param {number} maxSizeMB - Maximum allowed size in MB\r\n * @returns {boolean} - True if the file size is within the limit\r\n */\r\nexport const isFileSizeValid = (fileSize: number, maxSizeMB: number): boolean => {\r\n  const maxSizeBytes = maxSizeMB * 1024 * 1024;\r\n  return fileSize <= maxSizeBytes;\r\n};\r\n\r\n/**\r\n * List all CSV files stored in the database\r\n * @param {string} clientEmail - Optional client email to filter by\r\n * @returns {Promise} - Promise that resolves with the list of CSV files\r\n */\r\nexport const listCSVFiles = async (clientEmail?: string): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/list-csv-files', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        client_email: clientEmail\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error listing CSV files:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get CSV data from the database\r\n * @param {string} indexName - Name of the index to get data for\r\n * @param {number} limit - Maximum number of rows to return (default: 100)\r\n * @param {number} offset - Number of rows to skip (default: 0)\r\n * @returns {Promise} - Promise that resolves with the CSV data\r\n */\r\nexport const getCSVData = async (indexName: string, limit: number = 100, offset: number = 0): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/get-csv-data', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        index_name: indexName,\r\n        limit,\r\n        offset\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error getting CSV data:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get available embedding models\r\n * @returns {Promise} - Promise that resolves with the list of available embedding models\r\n */\r\nexport const getEmbeddingModels = async (): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/list-embedding-models', {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      }\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error getting embedding models:', error);\r\n\r\n    // Return fallback data when backend is not available\r\n    return {\r\n      success: true,\r\n      models: {\r\n        \"all-MiniLM-L6-v2\": {\r\n          \"name\": \"all-MiniLM-L6-v2\",\r\n          \"description\": \"Sentence Transformers model for semantic similarity\",\r\n          \"dimensions\": 384\r\n        },\r\n        \"all-mpnet-base-v2\": {\r\n          \"name\": \"all-mpnet-base-v2\",\r\n          \"description\": \"High-quality sentence embeddings\",\r\n          \"dimensions\": 768\r\n        },\r\n        \"paraphrase-MiniLM-L6-v2\": {\r\n          \"name\": \"paraphrase-MiniLM-L6-v2\",\r\n          \"description\": \"Paraphrase detection model\",\r\n          \"dimensions\": 384\r\n        }\r\n      },\r\n      default_model: \"all-MiniLM-L6-v2\"\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * Check if an error or response is a cancellation\r\n * @param {any} errorOrResponse - Error object or response from upload/cancel functions\r\n * @returns {boolean} - True if the error/response indicates a cancellation\r\n */\r\nexport const isCancellation = (errorOrResponse: any): boolean => {\r\n  // Check for our custom cancellation response\r\n  if (errorOrResponse && errorOrResponse.cancelled === true) {\r\n    return true;\r\n  }\r\n\r\n  // Check for error message containing cancellation text\r\n  if (errorOrResponse instanceof Error) {\r\n    const errorMessage = errorOrResponse.message.toLowerCase();\r\n    return errorMessage.includes('cancel') ||\r\n           errorMessage.includes('abort') ||\r\n           errorMessage.includes('user interrupt');\r\n  }\r\n\r\n  // Check for response with cancellation status\r\n  if (errorOrResponse && errorOrResponse.status === 'cancelled') {\r\n    return true;\r\n  }\r\n\r\n  // Check for response with error_type indicating cancellation\r\n  if (errorOrResponse && errorOrResponse.error_type === 'upload_cancelled') {\r\n    return true;\r\n  }\r\n\r\n  return false;\r\n};\r\n\r\n/**\r\n * Fetch emails from the API\r\n * @returns {Promise<string[]>} - Promise that resolves with an array of email addresses\r\n */\r\nexport const fetchEmails = async (): Promise<string[]> => {\r\n  try {\r\n    const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eSearch', {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'xxxid': 'QUKTYWK'\r\n      }\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    if (data.statusCode === 200 && Array.isArray(data.source)) {\r\n      // Parse each JSON string in the source array and extract emails\r\n      const emails = data.source.map((jsonStr: string) => {\r\n        try {\r\n          const userObj = JSON.parse(jsonStr);\r\n          return userObj.email || '';\r\n        } catch (error) {\r\n          console.error('Error parsing JSON:', error);\r\n          return '';\r\n        }\r\n      }).filter(Boolean); // Remove empty strings\r\n\r\n      return emails;\r\n    }\r\n\r\n    return [];\r\n  } catch (error) {\r\n    console.error('Error fetching emails:', error);\r\n    return [];\r\n  }\r\n};\r\n\r\n/**\r\n * Create PINE Collection Entry for index storage\r\n * @param {string} embedModel - Embedding model name (stored as api_key)\r\n * @param {string} indexName - Index name\r\n * @param {string} clientEmail - Client email\r\n * @returns {Promise<any>} - Promise that resolves with the server response\r\n */\r\nexport const createPineCollectionEntry = async (\r\n  embedModel: string,\r\n  indexName: string,\r\n  clientEmail: string\r\n): Promise<any> => {\r\n  try {\r\n    console.log(`Creating PINE collection entry: embedModel=${embedModel}, indexName=${indexName}, clientEmail=${clientEmail}`);\r\n\r\n    const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eCreateCol?colname=PINE', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'xxxid': 'PINE'\r\n      },\r\n      body: JSON.stringify({\r\n        api_key: embedModel,    // Store embedding model name as api_key\r\n        index_name: indexName,  // Store provided index name\r\n        client: clientEmail     // Store email as client\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(`Server error: ${response.status} - ${errorText}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    console.log('PINE collection entry created successfully:', result);\r\n    return result;\r\n  } catch (error) {\r\n    console.error('Error creating PINE collection entry:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get all indexes for a specific email from PINE collection\r\n * @param {string} clientEmail - Client email to filter by\r\n * @returns {Promise<any[]>} - Promise that resolves with array of index data\r\n */\r\nexport const getIndexesByEmail = async (clientEmail: string): Promise<any[]> => {\r\n  try {\r\n    console.log(`Fetching indexes for email: ${clientEmail}`);\r\n\r\n    const response = await fetch(\r\n      `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&filtercount=1&f1_field=client_S&f1_op=eq&f1_value=${encodeURIComponent(clientEmail)}`,\r\n      {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'xxxid': 'PINE'\r\n        }\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log('PINE collection response for email:', data);\r\n\r\n    if (data.statusCode === 200 && Array.isArray(data.source)) {\r\n      // Parse each JSON string in the source array\r\n      const indexes = data.source.map((jsonStr: string, index: number) => {\r\n        try {\r\n          const indexObj = JSON.parse(jsonStr);\r\n          return {\r\n            _id: indexObj._id?.$oid || indexObj._id || `pine-item-${index}`,\r\n            email: indexObj.client || clientEmail,\r\n            index_name: indexObj.index_name || 'N/A',\r\n            embed_model: indexObj.api_key || 'N/A', // api_key contains the embedding model\r\n            source: 'PINE' as const,\r\n            originalData: indexObj\r\n          };\r\n        } catch (error) {\r\n          console.error('Error parsing PINE index JSON:', error);\r\n          return null;\r\n        }\r\n      }).filter((item: any) => item !== null);\r\n\r\n      return indexes;\r\n    }\r\n\r\n    return [];\r\n  } catch (error) {\r\n    console.error('Error fetching indexes by email:', error);\r\n    return [];\r\n  }\r\n};\r\n\r\n/**\r\n * Check if a FAISS index exists\r\n * @param {string} indexName - Name of the index to check\r\n * @param {string} client - Client email or identifier (optional)\r\n * @param {string} embedModel - Name of the embedding model to use (optional)\r\n * @returns {Promise<{exists: boolean, embedding_model?: string}>} - Promise that resolves with info about the index\r\n */\r\nexport const checkIndexExists = async (\r\n  indexName: string,\r\n  client?: string,\r\n  embedModel?: string\r\n): Promise<{exists: boolean, embedding_model?: string}> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/check-index', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        index_name: indexName,\r\n        client: client,\r\n        embed_model: embedModel\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    if (data.success) {\r\n      return {\r\n        exists: data.exists,\r\n        embedding_model: data.embedding_model\r\n      };\r\n    }\r\n\r\n    return { exists: false };\r\n  } catch (error) {\r\n    console.error('Error checking if index exists:', error);\r\n    return { exists: false };\r\n  }\r\n};\r\n\r\n/**\r\n * Cancel an ongoing upload\r\n * @param {string} uploadId - The ID of the upload to cancel\r\n * @param {AbortController} abortController - Optional AbortController to abort the HTTP request\r\n * @returns {Promise<any>} - Promise that resolves with the server response\r\n */\r\nexport const cancelUpload = async (uploadId: string, abortController?: AbortController): Promise<any> => {\r\n  try {\r\n    // First, abort the HTTP request if an AbortController is provided\r\n    if (abortController) {\r\n      try {\r\n        abortController.abort();\r\n        console.log('HTTP request aborted');\r\n      } catch (abortError) {\r\n        // Don't log this as an error since it's expected behavior\r\n        console.log('Note: AbortController already used or not applicable');\r\n        // Continue with server-side cancellation even if client-side abort fails\r\n      }\r\n    }\r\n\r\n    // Then, send a cancellation request to the server\r\n    console.log(`Sending cancellation request for upload ${uploadId}`);\r\n    const response = await fetch('http://localhost:5010/api/cancel-upload', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        upload_id: uploadId\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(`Server error: ${response.status} - ${errorText}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log('Cancellation response:', data);\r\n\r\n    // Verify cancellation by checking status\r\n    try {\r\n      const statusResponse = await checkUploadStatus(uploadId);\r\n      console.log('Status after cancellation:', statusResponse);\r\n    } catch (statusError) {\r\n      console.error('Error checking status after cancellation:', statusError);\r\n      // Continue even if status check fails\r\n    }\r\n\r\n    return data;\r\n  } catch (error) {\r\n    console.error('Error cancelling upload:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Check the status of an ongoing upload\r\n * @param {string} uploadId - The ID of the upload to check\r\n * @param {boolean} silent - Whether to suppress console errors (default: false)\r\n * @returns {Promise<any>} - Promise that resolves with the upload status\r\n */\r\nexport const checkUploadStatus = async (uploadId: string, silent: boolean = false): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/upload-status', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        upload_id: uploadId\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(`Server error: ${response.status} - ${errorText}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    // Log cancellation status if detected\r\n    if (data.success && data.cancelled) {\r\n      console.log(`Upload ${uploadId} is marked as cancelled. Status: ${data.status}`);\r\n    }\r\n\r\n    return data;\r\n  } catch (error) {\r\n    if (!silent) {\r\n      console.error('Error checking upload status:', error);\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;;;;AACM,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAaO,MAAM,mBAAmB,OAC9B,MACA,aACA,WACA,YACA,QACA,YACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,qBAAqB;YACrB,IAAI,KAAK,IAAI,KAAK,YAAY;gBAC5B,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,iCAAiC;YACjC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,kDAAkD;YAClD,IAAI,aAAa;gBACf,SAAS,MAAM,CAAC,UAAU;YAC5B;YAEA,8BAA8B;YAC9B,IAAI,WAAW;gBACb,SAAS,MAAM,CAAC,cAAc;YAChC;YAEA,8BAA8B;YAC9B,IAAI,WAAW;gBACb,SAAS,MAAM,CAAC,cAAc;YAChC;YAEA,2CAA2C;YAC3C,IAAI,YAAY;gBACd,SAAS,MAAM,CAAC,eAAe;YACjC;YAEA,+CAA+C;YAC/C,IAAI,YAAY;gBACd,SAAS,MAAM,CAAC,eAAe;YACjC;YAEA,uDAAuD;YACvD,MAAM,MAAM,IAAI;YAEhB,mDAAmD;YACnD,IAAI,QAAQ;gBACV,OAAO,OAAO,GAAG;oBACf,IAAI,KAAK;oBACT,yEAAyE;oBACzE,wDAAwD;oBACxD,QAAQ;wBACN,SAAS;wBACT,WAAW;wBACX,SAAS;oBACX;gBACF;YACF;YAEA,gDAAgD;YAChD,IAAI,IAAI,CAAC,QAAQ,wCAAwC;YACzD,gEAAgE;YAChE,IAAI,gBAAgB,CAAC,SAAS;YAE9B,6CAA6C;YAC7C,IAAI,YAAY;gBACd,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;oBACvB,IAAI,MAAM,gBAAgB,EAAE;wBAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;wBAC3D,WAAW;oBACb;gBACF;YACF;YAEA,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,QAAQ;4BACN,SAAS;4BACT,SAAS;4BACT,WAAW,UAAU,mCAAmC;wBAC1D;oBACF;gBACF,OAAO;oBACL,OAAO,IAAI,MAAM,CAAC,cAAc,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,UAAU,EAAE;gBAClE;YACF;YAEA,wBAAwB;YACxB,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,mBAAmB;YACnB,IAAI,IAAI,CAAC;QACX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAGO,MAAM,sBAAsB;AAQ5B,MAAM,aAAa,OACxB,MACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,iCAAiC;YACjC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,8BAA8B;YAC9B,MAAM,MAAM,IAAI;YAEhB,wBAAwB;YACxB,IAAI,IAAI,CAAC,QAAQ,oCAAoC;YAErD,wBAAwB;YACxB,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;gBACvB,IAAI,MAAM,gBAAgB,IAAI,YAAY;oBACxC,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;oBAC3D,WAAW;gBACb;YACF;YAEA,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,QAAQ;4BACN,SAAS;4BACT,SAAS;4BACT,UAAU,KAAK,IAAI;wBACrB;oBACF;gBACF,OAAO;oBACL,OAAO,IAAI,MAAM,CAAC,cAAc,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,UAAU,EAAE;gBAClE;YACF;YAEA,wBAAwB;YACxB,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,mBAAmB;YACnB,IAAI,IAAI,CAAC;QACX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAQO,MAAM,sBAAsB,OACjC,OACA;IAEA,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC;QAChC,OAAO,WAAW,MAAM,CAAC;YACvB,IAAI,YAAY;gBACd,WAAW,KAAK,IAAI,EAAE;YACxB;QACF;IACF;IAEA,OAAO,QAAQ,GAAG,CAAC;AACrB;AAQO,MAAM,oBAAoB,CAAC,UAAkB;IAClD,OAAO,aAAa,QAAQ,CAAC;AAC/B;AAQO,MAAM,kBAAkB,CAAC,UAAkB;IAChD,MAAM,eAAe,YAAY,OAAO;IACxC,OAAO,YAAY;AACrB;AAOO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,4CAA4C;YACvE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AASO,MAAM,aAAa,OAAO,WAAmB,QAAgB,GAAG,EAAE,SAAiB,CAAC;IACzF,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,0CAA0C;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,YAAY;gBACZ;gBACA;YACF;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,mDAAmD;YAC9E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,qDAAqD;QACrD,OAAO;YACL,SAAS;YACT,QAAQ;gBACN,oBAAoB;oBAClB,QAAQ;oBACR,eAAe;oBACf,cAAc;gBAChB;gBACA,qBAAqB;oBACnB,QAAQ;oBACR,eAAe;oBACf,cAAc;gBAChB;gBACA,2BAA2B;oBACzB,QAAQ;oBACR,eAAe;oBACf,cAAc;gBAChB;YACF;YACA,eAAe;QACjB;IACF;AACF;AAOO,MAAM,iBAAiB,CAAC;IAC7B,6CAA6C;IAC7C,IAAI,mBAAmB,gBAAgB,SAAS,KAAK,MAAM;QACzD,OAAO;IACT;IAEA,uDAAuD;IACvD,IAAI,2BAA2B,OAAO;QACpC,MAAM,eAAe,gBAAgB,OAAO,CAAC,WAAW;QACxD,OAAO,aAAa,QAAQ,CAAC,aACtB,aAAa,QAAQ,CAAC,YACtB,aAAa,QAAQ,CAAC;IAC/B;IAEA,8CAA8C;IAC9C,IAAI,mBAAmB,gBAAgB,MAAM,KAAK,aAAa;QAC7D,OAAO;IACT;IAEA,6DAA6D;IAC7D,IAAI,mBAAmB,gBAAgB,UAAU,KAAK,oBAAoB;QACxE,OAAO;IACT;IAEA,OAAO;AACT;AAMO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,qDAAqD;YAChF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS;YACX;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE;QACpD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,UAAU,KAAK,OAAO,MAAM,OAAO,CAAC,KAAK,MAAM,GAAG;YACzD,gEAAgE;YAChE,MAAM,SAAS,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC9B,IAAI;oBACF,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,OAAO,QAAQ,KAAK,IAAI;gBAC1B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,OAAO;gBACT;YACF,GAAG,MAAM,CAAC,UAAU,uBAAuB;YAE3C,OAAO;QACT;QAEA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,EAAE;IACX;AACF;AASO,MAAM,4BAA4B,OACvC,YACA,WACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,WAAW,YAAY,EAAE,UAAU,cAAc,EAAE,aAAa;QAE1H,MAAM,WAAW,MAAM,MAAM,qEAAqE;YAChG,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS;YACX;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,SAAS;gBACT,YAAY;gBACZ,QAAQ,YAAgB,wBAAwB;YAClD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACnE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,+CAA+C;QAC3D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF;AAOO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,aAAa;QAExD,MAAM,WAAW,MAAM,MACrB,CAAC,4HAA4H,EAAE,mBAAmB,cAAc,EAChK;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS;YACX;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE;QACpD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,IAAI,KAAK,UAAU,KAAK,OAAO,MAAM,OAAO,CAAC,KAAK,MAAM,GAAG;YACzD,6CAA6C;YAC7C,MAAM,UAAU,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,SAAiB;gBAChD,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,OAAO;wBACL,KAAK,SAAS,GAAG,EAAE,QAAQ,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,OAAO;wBAC/D,OAAO,SAAS,MAAM,IAAI;wBAC1B,YAAY,SAAS,UAAU,IAAI;wBACnC,aAAa,SAAS,OAAO,IAAI;wBACjC,QAAQ;wBACR,cAAc;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,OAAO;gBACT;YACF,GAAG,MAAM,CAAC,CAAC,OAAc,SAAS;YAElC,OAAO;QACT;QAEA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,EAAE;IACX;AACF;AASO,MAAM,mBAAmB,OAC9B,WACA,QACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,yCAAyC;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,YAAY;gBACZ,QAAQ;gBACR,aAAa;YACf;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE;QACpD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,OAAO,EAAE;YAChB,OAAO;gBACL,QAAQ,KAAK,MAAM;gBACnB,iBAAiB,KAAK,eAAe;YACvC;QACF;QAEA,OAAO;YAAE,QAAQ;QAAM;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,QAAQ;QAAM;IACzB;AACF;AAQO,MAAM,eAAe,OAAO,UAAkB;IACnD,IAAI;QACF,kEAAkE;QAClE,IAAI,iBAAiB;YACnB,IAAI;gBACF,gBAAgB,KAAK;gBACrB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,YAAY;gBACnB,0DAA0D;gBAC1D,QAAQ,GAAG,CAAC;YACZ,yEAAyE;YAC3E;QACF;QAEA,kDAAkD;QAClD,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU;QACjE,MAAM,WAAW,MAAM,MAAM,2CAA2C;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,WAAW;YACb;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,yCAAyC;QACzC,IAAI;YACF,MAAM,iBAAiB,MAAM,kBAAkB;YAC/C,QAAQ,GAAG,CAAC,8BAA8B;QAC5C,EAAE,OAAO,aAAa;YACpB,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,sCAAsC;QACxC;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAQO,MAAM,oBAAoB,OAAO,UAAkB,SAAkB,KAAK;IAC/E,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,2CAA2C;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,WAAW;YACb;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,sCAAsC;QACtC,IAAI,KAAK,OAAO,IAAI,KAAK,SAAS,EAAE;YAClC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,SAAS,iCAAiC,EAAE,KAAK,MAAM,EAAE;QACjF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,IAAI,CAAC,QAAQ;YACX,QAAQ,KAAK,CAAC,iCAAiC;QACjD;QACA,MAAM;IACR;AACF"}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/services/faissService.ts"], "sourcesContent": ["/**\r\n * FAISS Service - Handles all FAISS-related API calls\r\n * Replaces Pinecone-specific functionality with FAISS backend integration\r\n */\r\n\r\nexport interface EmbeddingModel {\r\n  name: string;\r\n  dimension: number;\r\n  description: string;\r\n}\r\n\r\nexport interface FaissCategory {\r\n  index_name: string;\r\n  email?: string;\r\n  embedding_model?: string;\r\n  embedding_dimension?: number;\r\n  created_at?: string;\r\n}\r\n\r\nexport interface FaissQueryResult {\r\n  rank: number;\r\n  score: string;\r\n  metadata: any;\r\n  text: string;\r\n}\r\n\r\nexport interface UploadProgress {\r\n  upload_id: string;\r\n  status: 'processing' | 'complete' | 'cancelled' | 'error';\r\n  total_rows: number;\r\n  processed_rows: number;\r\n  total_vectors: number;\r\n  index_name: string;\r\n  cancelled: boolean;\r\n  processing_time?: number;\r\n}\r\n\r\n// Base URL for FAISS backend\r\nconst FAISS_BASE_URL = process.env.NODE_ENV === 'development' \r\n  ? 'http://localhost:5010' \r\n  : 'http://localhost:5010';\r\n\r\n/**\r\n * Upload CSV file to FAISS backend\r\n */\r\nexport const uploadCSVToFaiss = async (\r\n  file: File,\r\n  indexName: string,\r\n  clientEmail?: string,\r\n  updateMode: 'update' | 'new' = 'update',\r\n  embedModel: string = 'all-MiniLM-L6-v2',\r\n  signal?: AbortSignal,\r\n  onProgress?: (progress: number) => void\r\n): Promise<any> => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // Validate file type\r\n      if (file.type !== 'text/csv' && !file.name.toLowerCase().endsWith('.csv')) {\r\n        reject(new Error('Only CSV files are supported for CSV upload'));\r\n        return;\r\n      }\r\n\r\n      // Create FormData\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('index_name', indexName);\r\n      formData.append('update_mode', updateMode);\r\n      formData.append('embed_model', embedModel);\r\n      \r\n      if (clientEmail) {\r\n        formData.append('client', clientEmail);\r\n      }\r\n\r\n      // Create XMLHttpRequest for progress tracking\r\n      const xhr = new XMLHttpRequest();\r\n\r\n      // Handle response\r\n      xhr.onload = () => {\r\n        if (xhr.status >= 200 && xhr.status < 300) {\r\n          try {\r\n            const response = JSON.parse(xhr.responseText);\r\n            resolve(response);\r\n          } catch (e) {\r\n            reject(new Error('Invalid JSON response from server'));\r\n          }\r\n        } else {\r\n          try {\r\n            const errorResponse = JSON.parse(xhr.responseText);\r\n            reject(new Error(errorResponse.error || `HTTP ${xhr.status}: ${xhr.statusText}`));\r\n          } catch (e) {\r\n            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));\r\n          }\r\n        }\r\n      };\r\n\r\n      xhr.onerror = () => {\r\n        reject(new Error('Network error occurred during upload'));\r\n      };\r\n\r\n      xhr.onabort = () => {\r\n        reject(new Error('Upload was cancelled'));\r\n      };\r\n\r\n      // Track upload progress\r\n      if (onProgress) {\r\n        xhr.upload.onprogress = (event) => {\r\n          if (event.lengthComputable) {\r\n            const progress = Math.round((event.loaded / event.total) * 100);\r\n            onProgress(progress);\r\n          }\r\n        };\r\n      }\r\n\r\n      // Handle cancellation\r\n      if (signal) {\r\n        signal.addEventListener('abort', () => {\r\n          xhr.abort();\r\n        });\r\n      }\r\n\r\n      // Send request\r\n      xhr.open('POST', `${FAISS_BASE_URL}/api/upload-csv`, true);\r\n      xhr.send(formData);\r\n\r\n    } catch (error) {\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Upload Excel file to FAISS backend\r\n */\r\nexport const uploadExcelToFaiss = async (\r\n  file: File,\r\n  indexName: string,\r\n  clientId: string,\r\n  updateMode: 'update' | 'new' = 'update',\r\n  embedModel: string = 'all-MiniLM-L6-v2',\r\n  signal?: AbortSignal,\r\n  onProgress?: (progress: number) => void\r\n): Promise<any> => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // Validate file type\r\n      const fileName = file.name.toLowerCase();\r\n      if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {\r\n        reject(new Error('Only Excel files (.xlsx, .xls) are supported for Excel upload'));\r\n        return;\r\n      }\r\n\r\n      // Create FormData\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('index_name', indexName);\r\n      formData.append('client_id', clientId);\r\n      formData.append('update_mode', updateMode);\r\n      formData.append('embed_model', embedModel);\r\n\r\n      // Create XMLHttpRequest for progress tracking\r\n      const xhr = new XMLHttpRequest();\r\n\r\n      // Handle response\r\n      xhr.onload = () => {\r\n        if (xhr.status >= 200 && xhr.status < 300) {\r\n          try {\r\n            const response = JSON.parse(xhr.responseText);\r\n            resolve(response);\r\n          } catch (e) {\r\n            reject(new Error('Invalid JSON response from server'));\r\n          }\r\n        } else {\r\n          try {\r\n            const errorResponse = JSON.parse(xhr.responseText);\r\n            reject(new Error(errorResponse.error?.message || errorResponse.message || `HTTP ${xhr.status}: ${xhr.statusText}`));\r\n          } catch (e) {\r\n            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));\r\n          }\r\n        }\r\n      };\r\n\r\n      xhr.onerror = () => {\r\n        reject(new Error('Network error occurred during upload'));\r\n      };\r\n\r\n      xhr.onabort = () => {\r\n        reject(new Error('Upload was cancelled'));\r\n      };\r\n\r\n      // Track upload progress\r\n      if (onProgress) {\r\n        xhr.upload.onprogress = (event) => {\r\n          if (event.lengthComputable) {\r\n            const progress = Math.round((event.loaded / event.total) * 100);\r\n            onProgress(progress);\r\n          }\r\n        };\r\n      }\r\n\r\n      // Handle cancellation\r\n      if (signal) {\r\n        signal.addEventListener('abort', () => {\r\n          xhr.abort();\r\n        });\r\n      }\r\n\r\n      // Send request\r\n      xhr.open('POST', `${FAISS_BASE_URL}/api/upload-excel`, true);\r\n      xhr.send(formData);\r\n\r\n    } catch (error) {\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Upload file to FAISS backend (supports both CSV and Excel)\r\n */\r\nexport const uploadFileToFaiss = async (\r\n  file: File,\r\n  indexName: string,\r\n  clientEmail?: string,\r\n  updateMode: 'update' | 'new' = 'update',\r\n  embedModel: string = 'all-MiniLM-L6-v2',\r\n  signal?: AbortSignal,\r\n  onProgress?: (progress: number) => void\r\n): Promise<any> => {\r\n  const fileName = file.name.toLowerCase();\r\n  const isExcel = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');\r\n\r\n  if (isExcel) {\r\n    // For Excel files, client_id is required\r\n    if (!clientEmail) {\r\n      throw new Error('Client email is required for Excel file uploads');\r\n    }\r\n    return uploadExcelToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);\r\n  } else {\r\n    // For CSV files, use the existing CSV upload function\r\n    return uploadCSVToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);\r\n  }\r\n};\r\n\r\n/**\r\n * Get list of available embedding models\r\n */\r\nexport const getEmbeddingModels = async (): Promise<{ models: Record<string, EmbeddingModel>, default_model: string }> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-embedding-models`);\r\n  if (!response.ok) {\r\n    throw new Error(`Failed to fetch embedding models: ${response.statusText}`);\r\n  }\r\n  return response.json();\r\n};\r\n\r\n/**\r\n * Get list of FAISS categories/indexes\r\n */\r\nexport const getFaissCategories = async (clientEmail?: string): Promise<FaissCategory[]> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-categories`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify(clientEmail ? { client_email: clientEmail } : {})\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to fetch FAISS categories: ${response.statusText}`);\r\n  }\r\n  \r\n  const data = await response.json();\r\n  return data.categories || [];\r\n};\r\n\r\n/**\r\n * Check if FAISS index exists\r\n */\r\nexport const checkFaissIndexExists = async (indexName: string, embedModel?: string): Promise<boolean> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/check-index`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({\r\n      index_name: indexName,\r\n      embed_model: embedModel\r\n    })\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to check FAISS index: ${response.statusText}`);\r\n  }\r\n  \r\n  const data = await response.json();\r\n  return data.exists || false;\r\n};\r\n\r\n/**\r\n * Get current user's email from session storage\r\n */\r\nconst getCurrentUserEmail = (): string | null => {\r\n  try {\r\n    if (typeof window === 'undefined') return null;\r\n\r\n    // Try multiple sources for user email\r\n    const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');\r\n    if (directEmail) return directEmail;\r\n\r\n    // Try from user session data\r\n    const userSession = sessionStorage.getItem('resultUser');\r\n    if (userSession) {\r\n      const userData = JSON.parse(userSession);\r\n      return userData.email || userData.username || null;\r\n    }\r\n\r\n    return null;\r\n  } catch (error) {\r\n    console.error('Error getting current user email:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Query FAISS index with user access validation\r\n */\r\nexport const queryFaiss = async (\r\n  query: string,\r\n  indexName: string,\r\n  k: number = 5,\r\n  userEmail?: string\r\n): Promise<FaissQueryResult[]> => {\r\n  // Get user email if not provided\r\n  const emailToUse = userEmail || getCurrentUserEmail();\r\n\r\n  const requestBody: any = {\r\n    query,\r\n    index_name: indexName,\r\n    k\r\n  };\r\n\r\n  // Add user email for access validation if available\r\n  if (emailToUse) {\r\n    requestBody.user_email = emailToUse;\r\n  }\r\n\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/query-faiss`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify(requestBody)\r\n  });\r\n\r\n  if (!response.ok) {\r\n    throw new Error(`Failed to query FAISS index: ${response.statusText}`);\r\n  }\r\n\r\n  const data = await response.json();\r\n  return data.results || [];\r\n};\r\n\r\n/**\r\n * Get upload status\r\n */\r\nexport const getUploadStatus = async (uploadId: string): Promise<UploadProgress> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/upload-status`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({ upload_id: uploadId })\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to get upload status: ${response.statusText}`);\r\n  }\r\n  \r\n  return response.json();\r\n};\r\n\r\n/**\r\n * Cancel upload\r\n */\r\nexport const cancelUpload = async (uploadId: string): Promise<void> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/cancel-upload`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({ upload_id: uploadId })\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to cancel upload: ${response.statusText}`);\r\n  }\r\n};\r\n\r\n/**\r\n * Get CSV data from database\r\n */\r\nexport const getCSVData = async (\r\n  indexName: string, \r\n  limit: number = 100, \r\n  offset: number = 0\r\n): Promise<any> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/get-csv-data`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({\r\n      index_name: indexName,\r\n      limit,\r\n      offset\r\n    })\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to get CSV data: ${response.statusText}`);\r\n  }\r\n  \r\n  return response.json();\r\n};\r\n\r\n/**\r\n * List CSV files in database\r\n */\r\nexport const listCSVFiles = async (clientEmail?: string): Promise<any[]> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-csv-files`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify(clientEmail ? { client_email: clientEmail } : {})\r\n  });\r\n\r\n  if (!response.ok) {\r\n    throw new Error(`Failed to list CSV files: ${response.statusText}`);\r\n  }\r\n\r\n  const data = await response.json();\r\n  return data.files || [];\r\n};\r\n\r\n/**\r\n * List Excel files in database\r\n */\r\nexport const listExcelFiles = async (clientId?: string): Promise<any[]> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-excel-files`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify(clientId ? { client_id: clientId } : {})\r\n  });\r\n\r\n  if (!response.ok) {\r\n    throw new Error(`Failed to list Excel files: ${response.statusText}`);\r\n  }\r\n\r\n  const data = await response.json();\r\n  return data.excel_files || [];\r\n};\r\n\r\n// Utility functions for localStorage management\r\nexport const getFaissConfig = () => {\r\n  if (typeof window === 'undefined') return null;\r\n  \r\n  return {\r\n    indexName: localStorage.getItem('faiss_index_name'),\r\n    embedModel: localStorage.getItem('faiss_embed_model') || 'all-MiniLM-L6-v2',\r\n    clientEmail: localStorage.getItem('faiss_client_email')\r\n  };\r\n};\r\n\r\nexport const setFaissConfig = (config: {\r\n  indexName?: string;\r\n  embedModel?: string;\r\n  clientEmail?: string;\r\n}) => {\r\n  if (typeof window === 'undefined') return;\r\n  \r\n  if (config.indexName) {\r\n    localStorage.setItem('faiss_index_name', config.indexName);\r\n  }\r\n  if (config.embedModel) {\r\n    localStorage.setItem('faiss_embed_model', config.embedModel);\r\n  }\r\n  if (config.clientEmail) {\r\n    localStorage.setItem('faiss_client_email', config.clientEmail);\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;AAmCsB;AADvB,6BAA6B;AAC7B,MAAM,iBAAiB,uCACnB;AAMG,MAAM,mBAAmB,OAC9B,MACA,WACA,aACA,aAA+B,QAAQ,EACvC,aAAqB,kBAAkB,EACvC,QACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,qBAAqB;YACrB,IAAI,KAAK,IAAI,KAAK,cAAc,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;gBACzE,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,kBAAkB;YAClB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,cAAc;YAC9B,SAAS,MAAM,CAAC,eAAe;YAC/B,SAAS,MAAM,CAAC,eAAe;YAE/B,IAAI,aAAa;gBACf,SAAS,MAAM,CAAC,UAAU;YAC5B;YAEA,8CAA8C;YAC9C,MAAM,MAAM,IAAI;YAEhB,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM;oBACnB;gBACF,OAAO;oBACL,IAAI;wBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,IAAI,YAAY;wBACjD,OAAO,IAAI,MAAM,cAAc,KAAK,IAAI,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBACjF,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBAC1D;gBACF;YACF;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,wBAAwB;YACxB,IAAI,YAAY;gBACd,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;oBACvB,IAAI,MAAM,gBAAgB,EAAE;wBAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;wBAC3D,WAAW;oBACb;gBACF;YACF;YAEA,sBAAsB;YACtB,IAAI,QAAQ;gBACV,OAAO,gBAAgB,CAAC,SAAS;oBAC/B,IAAI,KAAK;gBACX;YACF;YAEA,eAAe;YACf,IAAI,IAAI,CAAC,QAAQ,GAAG,eAAe,eAAe,CAAC,EAAE;YACrD,IAAI,IAAI,CAAC;QAEX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAKO,MAAM,qBAAqB,OAChC,MACA,WACA,UACA,aAA+B,QAAQ,EACvC,aAAqB,kBAAkB,EACvC,QACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,qBAAqB;YACrB,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;YACtC,IAAI,CAAC,SAAS,QAAQ,CAAC,YAAY,CAAC,SAAS,QAAQ,CAAC,SAAS;gBAC7D,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,kBAAkB;YAClB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,cAAc;YAC9B,SAAS,MAAM,CAAC,aAAa;YAC7B,SAAS,MAAM,CAAC,eAAe;YAC/B,SAAS,MAAM,CAAC,eAAe;YAE/B,8CAA8C;YAC9C,MAAM,MAAM,IAAI;YAEhB,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM;oBACnB;gBACF,OAAO;oBACL,IAAI;wBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,IAAI,YAAY;wBACjD,OAAO,IAAI,MAAM,cAAc,KAAK,EAAE,WAAW,cAAc,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBACnH,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBAC1D;gBACF;YACF;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,wBAAwB;YACxB,IAAI,YAAY;gBACd,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;oBACvB,IAAI,MAAM,gBAAgB,EAAE;wBAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;wBAC3D,WAAW;oBACb;gBACF;YACF;YAEA,sBAAsB;YACtB,IAAI,QAAQ;gBACV,OAAO,gBAAgB,CAAC,SAAS;oBAC/B,IAAI,KAAK;gBACX;YACF;YAEA,eAAe;YACf,IAAI,IAAI,CAAC,QAAQ,GAAG,eAAe,iBAAiB,CAAC,EAAE;YACvD,IAAI,IAAI,CAAC;QAEX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAKO,MAAM,oBAAoB,OAC/B,MACA,WACA,aACA,aAA+B,QAAQ,EACvC,aAAqB,kBAAkB,EACvC,QACA;IAEA,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;IACtC,MAAM,UAAU,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC;IAEhE,IAAI,SAAS;QACX,yCAAyC;QACzC,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,mBAAmB,MAAM,WAAW,aAAa,YAAY,YAAY,QAAQ;IAC1F,OAAO;QACL,sDAAsD;QACtD,OAAO,iBAAiB,MAAM,WAAW,aAAa,YAAY,YAAY,QAAQ;IACxF;AACF;AAKO,MAAM,qBAAqB;IAChC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,0BAA0B,CAAC;IAC1E,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;IAC5E;IACA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,qBAAqB,OAAO;IACvC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,oBAAoB,CAAC,EAAE;QACpE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC,cAAc;YAAE,cAAc;QAAY,IAAI,CAAC;IACtE;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;IAC5E;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,UAAU,IAAI,EAAE;AAC9B;AAKO,MAAM,wBAAwB,OAAO,WAAmB;IAC7D,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,gBAAgB,CAAC,EAAE;QAChE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB,YAAY;YACZ,aAAa;QACf;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;IACvE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,MAAM,IAAI;AACxB;AAEA;;CAEC,GACD,MAAM,sBAAsB;IAC1B,IAAI;QACF,uCAAmC;;QAAW;QAE9C,sCAAsC;QACtC,MAAM,cAAc,aAAa,OAAO,CAAC,iBAAiB,eAAe,OAAO,CAAC;QACjF,IAAI,aAAa,OAAO;QAExB,6BAA6B;QAC7B,MAAM,cAAc,eAAe,OAAO,CAAC;QAC3C,IAAI,aAAa;YACf,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,OAAO,SAAS,KAAK,IAAI,SAAS,QAAQ,IAAI;QAChD;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAKO,MAAM,aAAa,OACxB,OACA,WACA,IAAY,CAAC,EACb;IAEA,iCAAiC;IACjC,MAAM,aAAa,aAAa;IAEhC,MAAM,cAAmB;QACvB;QACA,YAAY;QACZ;IACF;IAEA,oDAAoD;IACpD,IAAI,YAAY;QACd,YAAY,UAAU,GAAG;IAC3B;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,gBAAgB,CAAC,EAAE;QAChE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;IACvE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,OAAO,IAAI,EAAE;AAC3B;AAKO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,kBAAkB,CAAC,EAAE;QAClE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE,WAAW;QAAS;IAC7C;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;IACvE;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,kBAAkB,CAAC,EAAE;QAClE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE,WAAW;QAAS;IAC7C;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;IACnE;AACF;AAKO,MAAM,aAAa,OACxB,WACA,QAAgB,GAAG,EACnB,SAAiB,CAAC;IAElB,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,iBAAiB,CAAC,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB,YAAY;YACZ;YACA;QACF;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,mBAAmB,CAAC,EAAE;QACnE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC,cAAc;YAAE,cAAc;QAAY,IAAI,CAAC;IACtE;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;IACpE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,KAAK,IAAI,EAAE;AACzB;AAKO,MAAM,iBAAiB,OAAO;IACnC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,qBAAqB,CAAC,EAAE;QACrE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC,WAAW;YAAE,WAAW;QAAS,IAAI,CAAC;IAC7D;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;IACtE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,WAAW,IAAI,EAAE;AAC/B;AAGO,MAAM,iBAAiB;IAC5B,uCAAmC;;IAAW;IAE9C,OAAO;QACL,WAAW,aAAa,OAAO,CAAC;QAChC,YAAY,aAAa,OAAO,CAAC,wBAAwB;QACzD,aAAa,aAAa,OAAO,CAAC;IACpC;AACF;AAEO,MAAM,iBAAiB,CAAC;IAK7B,uCAAmC;;IAAM;IAEzC,IAAI,OAAO,SAAS,EAAE;QACpB,aAAa,OAAO,CAAC,oBAAoB,OAAO,SAAS;IAC3D;IACA,IAAI,OAAO,UAAU,EAAE;QACrB,aAAa,OAAO,CAAC,qBAAqB,OAAO,UAAU;IAC7D;IACA,IAAI,OAAO,WAAW,EAAE;QACtB,aAAa,OAAO,CAAC,sBAAsB,OAAO,WAAW;IAC/D;AACF"}}, {"offset": {"line": 866, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/services/fileDownloadService.ts"], "sourcesContent": ["/**\r\n * Service for handling file downloads\r\n */\r\n\r\n/**\r\n * Download a sample CSV file\r\n * @param {string} fileName - Name of the sample file to download\r\n * @returns {Promise<boolean>} - Promise that resolves to true if download was successful\r\n */\r\nexport const downloadSampleFile = async (fileName: string = 'querry.csv'): Promise<boolean> => {\r\n  try {\r\n    // First check if the file exists by making a HEAD request\r\n    try {\r\n      // Use the public directory path\r\n      const checkResponse = await fetch(`/${fileName}`, { method: 'HEAD' });\r\n      if (!checkResponse.ok) {\r\n        console.error(`Sample file ${fileName} not found. Status: ${checkResponse.status}`);\r\n        return false;\r\n      }\r\n    } catch (checkError) {\r\n      console.error('Error checking if sample file exists:', checkError);\r\n      return false;\r\n    }\r\n\r\n    // Create a URL to the file in the public directory\r\n    const fileUrl = `/${fileName}`;\r\n\r\n    // Create a link element\r\n    const link = document.createElement('a');\r\n    link.href = fileUrl;\r\n    link.download = fileName;\r\n\r\n    // Append to the document\r\n    document.body.appendChild(link);\r\n\r\n    // Trigger the download\r\n    link.click();\r\n\r\n    // Clean up\r\n    document.body.removeChild(link);\r\n\r\n    return true;\r\n  } catch (error) {\r\n    console.error('Error downloading sample file:', error);\r\n    return false;\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;CAIC;;;AACM,MAAM,qBAAqB,OAAO,WAAmB,YAAY;IACtE,IAAI;QACF,0DAA0D;QAC1D,IAAI;YACF,gCAAgC;YAChC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,EAAE,UAAU,EAAE;gBAAE,QAAQ;YAAO;YACnE,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,SAAS,oBAAoB,EAAE,cAAc,MAAM,EAAE;gBAClF,OAAO;YACT;QACF,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;QAEA,mDAAmD;QACnD,MAAM,UAAU,CAAC,CAAC,EAAE,UAAU;QAE9B,wBAAwB;QACxB,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAEhB,yBAAyB;QACzB,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,uBAAuB;QACvB,KAAK,KAAK;QAEV,WAAW;QACX,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF"}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/FileUploadWithFaiss.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef } from 'react';\r\nimport { PiCloudArrowUp, PiFile, PiX, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, PiDatabase, PiDownload } from 'react-icons/pi';\r\nimport { formatFileSize, isFileTypeAllowed, isFileSizeValid } from '@/services/fileUploadService';\r\nimport { uploadFileToFaiss } from '@/services/faissService';\r\nimport { downloadSampleFile } from '@/services/fileDownloadService';\r\n\r\ninterface FileUploadWithFaissProps {\r\n  onFileUpload?: (files: File[]) => void;\r\n  maxFileSize?: number;\r\n  allowedTypes?: string[];\r\n  selectedLanguage?: string;\r\n  showFaissUpload?: boolean;\r\n  indexName?: string;\r\n  clientEmail?: string;\r\n  updateMode?: string | null;\r\n  embedModel?: string;\r\n  onFaissUploadSuccess?: (response: any) => void;\r\n  onFaissUploadError?: (error: string) => void;\r\n}\r\n\r\ninterface FileWithStatus {\r\n  file: File;\r\n  status: 'selected' | 'uploading' | 'success' | 'error' | 'faiss-uploading' | 'faiss-success' | 'faiss-error';\r\n  error?: string;\r\n  faissResponse?: any;\r\n  uploadProgress?: number;\r\n  faissProgress?: number;\r\n}\r\n\r\nconst FileUploadWithFaiss: React.FC<FileUploadWithFaissProps> = ({\r\n  onFileUpload,\r\n  maxFileSize = 50,\r\n  allowedTypes = ['text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'],\r\n  selectedLanguage = 'English',\r\n  showFaissUpload = true,\r\n  indexName = 'default',\r\n  clientEmail = '<EMAIL>',\r\n  updateMode = 'update',\r\n  embedModel = 'all-MiniLM-L6-v2',\r\n  onFaissUploadSuccess,\r\n  onFaissUploadError\r\n}) => {\r\n  const [files, setFiles] = useState<FileWithStatus[]>([]);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [downloadError, setDownloadError] = useState<string | null>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const dropAreaRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Get language-specific text\r\n  const getLanguageText = () => {\r\n    const texts = {\r\n      English: {\r\n        dragDrop: 'Drag & drop a CSV or Excel file or click to browse',\r\n        csvOnly: 'CSV & Excel format only, up to',\r\n        downloadSample: 'Download Sample File',\r\n        selectedFile: 'Selected File',\r\n        upload: 'Upload',\r\n        uploadToFaiss: 'Upload to FAISS',\r\n        uploading: 'Uploading...',\r\n        uploadingToFaiss: 'Uploading to FAISS...',\r\n        success: 'Success',\r\n        error: 'Error',\r\n        remove: 'Remove',\r\n        indexInfo: 'Your CSV or Excel file name is taken as index in DB and your data will be stored in that index'\r\n      },\r\n      Tamil: {\r\n        dragDrop: 'CSV அல்லது Excel கோப்பை இழுத்து விடவும் அல்லது உலாவ கிளிக் செய்யவும்',\r\n        csvOnly: 'CSV & Excel வடிவம் மட்டும், வரை',\r\n        downloadSample: 'மாதிரி கோப்பை பதிவிறக்கவும்',\r\n        selectedFile: 'தேர்ந்தெடுக்கப்பட்ட கோப்பு',\r\n        upload: 'பதிவேற்று',\r\n        uploadToFaiss: 'FAISS இல் பதிவேற்று',\r\n        uploading: 'பதிவேற்றுகிறது...',\r\n        uploadingToFaiss: 'FAISS இல் பதிவேற்றுகிறது...',\r\n        success: 'வெற்றி',\r\n        error: 'பிழை',\r\n        remove: 'அகற்று',\r\n        indexInfo: 'உங்கள் CSV அல்லது Excel கோப்பு பெயர் DB இல் குறியீடாக எடுக்கப்பட்டு உங்கள் தரவு அந்த குறியீட்டில் சேமிக்கப்படும்'\r\n      },\r\n      Telugu: {\r\n        dragDrop: 'CSV లేదా Excel ఫైల్‌ను లాగి వదలండి లేదా బ్రౌజ్ చేయడానికి క్లిక్ చేయండి',\r\n        csvOnly: 'CSV & Excel ఫార్మాట్ మాత్రమే, వరకు',\r\n        downloadSample: 'నమూనా ఫైల్ డౌన్‌లోడ్ చేయండి',\r\n        selectedFile: 'ఎంచుకున్న ఫైల్',\r\n        upload: 'అప్‌లోడ్',\r\n        uploadToFaiss: 'FAISS కు అప్‌లోడ్',\r\n        uploading: 'అప్‌లోడ్ చేస్తోంది...',\r\n        uploadingToFaiss: 'FAISS కు అప్‌లోడ్ చేస్తోంది...',\r\n        success: 'విజయం',\r\n        error: 'లోపం',\r\n        remove: 'తొలగించు',\r\n        indexInfo: 'మీ CSV లేదా Excel ఫైల్ పేరు DB లో ఇండెక్స్‌గా తీసుకోబడుతుంది మరియు మీ డేటా ఆ ఇండెక్స్‌లో నిల్వ చేయబడుతుంది'\r\n      },\r\n      Kannada: {\r\n        dragDrop: 'CSV ಅಥವಾ Excel ಫೈಲ್ ಅನ್ನು ಎಳೆದು ಬಿಡಿ ಅಥವಾ ಬ್ರೌಸ್ ಮಾಡಲು ಕ್ಲಿಕ್ ಮಾಡಿ',\r\n        csvOnly: 'CSV & Excel ಸ್ವರೂಪ ಮಾತ್ರ, ವರೆಗೆ',\r\n        downloadSample: 'ಮಾದರಿ ಫೈಲ್ ಡೌನ್‌ಲೋಡ್ ಮಾಡಿ',\r\n        selectedFile: 'ಆಯ್ಕೆಮಾಡಿದ ಫೈಲ್',\r\n        upload: 'ಅಪ್‌ಲೋಡ್',\r\n        uploadToFaiss: 'FAISS ಗೆ ಅಪ್‌ಲೋಡ್',\r\n        uploading: 'ಅಪ್‌ಲೋಡ್ ಮಾಡುತ್ತಿದೆ...',\r\n        uploadingToFaiss: 'FAISS ಗೆ ಅಪ್‌ಲೋಡ್ ಮಾಡುತ್ತಿದೆ...',\r\n        success: 'ಯಶಸ್ಸು',\r\n        error: 'ದೋಷ',\r\n        remove: 'ತೆಗೆದುಹಾಕಿ',\r\n        indexInfo: 'ನಿಮ್ಮ CSV ಅಥವಾ Excel ಫೈಲ್ ಹೆಸರನ್ನು DB ಯಲ್ಲಿ ಸೂಚ್ಯಂಕವಾಗಿ ತೆಗೆದುಕೊಳ್ಳಲಾಗುತ್ತದೆ ಮತ್ತು ನಿಮ್ಮ ಡೇಟಾವನ್ನು ಆ ಸೂಚ್ಯಂಕದಲ್ಲಿ ಸಂಗ್ರಹಿಸಲಾಗುತ್ತದೆ'\r\n      }\r\n    };\r\n    return texts[selectedLanguage as keyof typeof texts] || texts.English;\r\n  };\r\n\r\n  // Handle file selection\r\n  const handleFileSelect = (fileList: FileList | null) => {\r\n    if (!fileList || fileList.length === 0) return;\r\n\r\n    const newFile = fileList[0];\r\n\r\n    // Validate file type\r\n    const fileName = newFile.name.toLowerCase();\r\n    const isCSV = newFile.type === 'text/csv' || fileName.endsWith('.csv');\r\n    const isExcel = newFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                   newFile.type === 'application/vnd.ms-excel' ||\r\n                   fileName.endsWith('.xlsx') ||\r\n                   fileName.endsWith('.xls');\r\n\r\n    if (!isCSV && !isExcel) {\r\n      alert(`File type not allowed. Please upload a CSV or Excel file.`);\r\n      return;\r\n    }\r\n\r\n    // Validate file size\r\n    if (!isFileSizeValid(newFile.size, maxFileSize)) {\r\n      alert(`File size exceeds the ${maxFileSize}MB limit.`);\r\n      return;\r\n    }\r\n\r\n    // Create file with status\r\n    const fileWithStatus: FileWithStatus = {\r\n      file: newFile,\r\n      status: 'selected',\r\n      uploadProgress: 0,\r\n      faissProgress: 0\r\n    };\r\n\r\n    setFiles([fileWithStatus]);\r\n\r\n    // Call onFileUpload callback if provided\r\n    if (onFileUpload) {\r\n      onFileUpload([newFile]);\r\n    }\r\n  };\r\n\r\n  // Handle file input change\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    handleFileSelect(e.target.files);\r\n    if (e.target) e.target.value = '';\r\n  };\r\n\r\n  // Handle click on the drop area\r\n  const handleClick = () => {\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  // Handle drag events\r\n  const handleDragEnter = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if (e.currentTarget === e.target) {\r\n      setIsDragging(false);\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(true);\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(false);\r\n    const droppedFiles = e.dataTransfer.files;\r\n    handleFileSelect(droppedFiles);\r\n  };\r\n\r\n  // Upload file to FAISS\r\n  const uploadToFaiss = async (fileWithStatus: FileWithStatus) => {\r\n    const { file } = fileWithStatus;\r\n    \r\n    // Update status to uploading\r\n    setFiles(prevFiles => \r\n      prevFiles.map(f => \r\n        f.file.name === file.name \r\n          ? { ...f, status: 'faiss-uploading', faissProgress: 0 }\r\n          : f\r\n      )\r\n    );\r\n\r\n    try {\r\n      const response = await uploadFileToFaiss(\r\n        file,\r\n        indexName,\r\n        clientEmail,\r\n        updateMode || 'update',\r\n        embedModel,\r\n        undefined,\r\n        (progress) => {\r\n          // Update progress\r\n          setFiles(prevFiles =>\r\n            prevFiles.map(f =>\r\n              f.file.name === file.name\r\n                ? { ...f, faissProgress: progress }\r\n                : f\r\n            )\r\n          );\r\n        }\r\n      );\r\n\r\n      // Update status to success\r\n      setFiles(prevFiles => \r\n        prevFiles.map(f => \r\n          f.file.name === file.name \r\n            ? { \r\n                ...f, \r\n                status: 'faiss-success', \r\n                faissResponse: response,\r\n                faissProgress: 100 \r\n              }\r\n            : f\r\n        )\r\n      );\r\n\r\n      if (onFaissUploadSuccess) {\r\n        onFaissUploadSuccess(response);\r\n      }\r\n\r\n    } catch (error) {\r\n      // Update status to error\r\n      setFiles(prevFiles => \r\n        prevFiles.map(f => \r\n          f.file.name === file.name \r\n            ? { \r\n                ...f, \r\n                status: 'faiss-error', \r\n                error: error instanceof Error ? error.message : 'Upload failed'\r\n              }\r\n            : f\r\n        )\r\n      );\r\n\r\n      if (onFaissUploadError) {\r\n        onFaissUploadError(error instanceof Error ? error.message : 'Upload failed');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle file removal\r\n  const removeFile = (fileName: string) => {\r\n    setFiles(prevFiles => prevFiles.filter(file => file.file.name !== fileName));\r\n  };\r\n\r\n  // Handle sample file download\r\n  const handleSampleFileDownload = async (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    setDownloadError(null);\r\n    try {\r\n      const success = await downloadSampleFile('querry.csv');\r\n      if (!success) {\r\n        setDownloadError('Failed to download sample file. Please try again.');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error downloading sample file:', error);\r\n      setDownloadError('An error occurred while downloading the sample file.');\r\n    }\r\n  };\r\n\r\n  const text = getLanguageText();\r\n\r\n  return (\r\n    <div className=\"file-upload-container\">\r\n      <div\r\n        ref={dropAreaRef}\r\n        className={`drop-area ${isDragging ? 'dragging' : ''} bg-primaryColor/5 border border-dashed border-primaryColor/30 rounded-xl p-8 flex justify-center items-center flex-col transition-all duration-200 hover:bg-primaryColor/10 cursor-pointer`}\r\n        onClick={handleClick}\r\n        onDragEnter={handleDragEnter}\r\n        onDragLeave={handleDragLeave}\r\n        onDragOver={handleDragOver}\r\n        onDrop={handleDrop}\r\n      >\r\n        <div className=\"drop-icon text-primaryColor\">\r\n          <PiCloudArrowUp size={50} />\r\n        </div>\r\n        <p className=\"drop-text text-center text-lg font-medium pt-5 text-gray-700 dark:text-gray-300\">\r\n          {text.dragDrop}\r\n        </p>\r\n        <div className=\"file-restriction-notice mt-2 bg-primaryColor/10 px-3 py-1 rounded-full text-xs\">\r\n          <span className=\"restriction-icon\">ⓘ</span>\r\n          <span className=\"ml-1\">{text.indexInfo}</span>\r\n        </div>\r\n        <p className=\"file-types text-gray-500 dark:text-gray-400 text-sm pt-2\">\r\n          CSV & Excel files only • Max {maxFileSize}MB\r\n        </p>\r\n\r\n        {/* Download Sample File Button */}\r\n        <button\r\n          type=\"button\"\r\n          onClick={handleSampleFileDownload}\r\n          className=\"download-sample-btn mt-3 bg-white border border-primaryColor text-primaryColor hover:bg-primaryColor/5 px-4 py-2 rounded-md text-sm flex items-center transition-colors\"\r\n        >\r\n          <PiDownload className=\"h-4 w-4 mr-2\" />\r\n          {text.downloadSample}\r\n        </button>\r\n\r\n        {/* Error message for download */}\r\n        {downloadError && (\r\n          <div className=\"download-error mt-2 text-red-500 text-xs\">\r\n            {downloadError}\r\n          </div>\r\n        )}\r\n\r\n        <input\r\n          type=\"file\"\r\n          ref={fileInputRef}\r\n          onChange={handleInputChange}\r\n          className=\"file-input hidden\"\r\n          accept=\".csv,.xlsx,.xls,text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel\"\r\n        />\r\n      </div>\r\n\r\n      {files.length > 0 && (\r\n        <div className=\"file-list mt-4 bg-gray-50 dark:bg-gray-900 p-4 rounded-lg\">\r\n          <h3 className=\"text-lg font-medium mb-2 dark:text-gray-200\">{text.selectedFile}</h3>\r\n          <ul className=\"space-y-2\">\r\n            {files.map((fileWithStatus, index) => (\r\n              <li key={`${fileWithStatus.file.name}-${index}`} className=\"file-item bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n                <div className=\"file-info flex justify-between items-center mb-3\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <PiFile className=\"text-primaryColor\" size={20} />\r\n                    <span className=\"file-name font-medium dark:text-gray-200\">{fileWithStatus.file.name}</span>\r\n                  </div>\r\n                  <span className=\"file-size text-sm text-gray-500 dark:text-gray-400\">\r\n                    {formatFileSize(fileWithStatus.file.size)}\r\n                  </span>\r\n                </div>\r\n\r\n                {/* FAISS Upload Button and Status */}\r\n                {showFaissUpload && (\r\n                  <div className=\"faiss-upload-section\">\r\n                    {fileWithStatus.status === 'selected' && (\r\n                      <button\r\n                        className=\"upload-to-faiss-btn bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 flex items-center gap-2 transition-colors\"\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          uploadToFaiss(fileWithStatus);\r\n                        }}\r\n                      >\r\n                        <PiDatabase size={16} />\r\n                        {text.uploadToFaiss}\r\n                      </button>\r\n                    )}\r\n\r\n                    {fileWithStatus.status === 'faiss-uploading' && (\r\n                      <div className=\"faiss-upload-progress\">\r\n                        <div className=\"flex items-center gap-2 mb-2\">\r\n                          <PiSpinner className=\"animate-spin text-blue-600\" size={16} />\r\n                          <span className=\"text-sm text-blue-600\">{text.uploadingToFaiss}</span>\r\n                        </div>\r\n                        <div className=\"progress-container w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5\">\r\n                          <div\r\n                            className=\"progress-bar bg-blue-600 h-2.5 rounded-full transition-all duration-300\"\r\n                            style={{ width: `${fileWithStatus.faissProgress || 0}%` }}\r\n                          ></div>\r\n                        </div>\r\n                        <span className=\"progress-text text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                          {fileWithStatus.faissProgress || 0}%\r\n                        </span>\r\n                      </div>\r\n                    )}\r\n\r\n                    {fileWithStatus.status === 'faiss-success' && (\r\n                      <div className=\"faiss-success flex items-center gap-2 text-green-600\">\r\n                        <PiCheck size={16} />\r\n                        <span className=\"text-sm\">{text.success} - Uploaded to FAISS</span>\r\n                      </div>\r\n                    )}\r\n\r\n                    {fileWithStatus.status === 'faiss-error' && (\r\n                      <div className=\"faiss-error\">\r\n                        <div className=\"flex items-center gap-2 text-red-600 mb-1\">\r\n                          <PiX size={16} />\r\n                          <span className=\"text-sm\">{text.error}</span>\r\n                        </div>\r\n                        {fileWithStatus.error && (\r\n                          <div className=\"error-message text-red-500 text-xs\">\r\n                            {fileWithStatus.error}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Remove button */}\r\n                <div className=\"file-actions flex justify-end mt-3\">\r\n                  <button\r\n                    className=\"remove-btn text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 p-1\"\r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      removeFile(fileWithStatus.file.name);\r\n                    }}\r\n                  >\r\n                    <PiX size={16} />\r\n                  </button>\r\n                </div>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUploadWithFaiss;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAHA;;;AAHA;;;;;;AA+BA,MAAM,sBAA0D,CAAC,EAC/D,YAAY,EACZ,cAAc,EAAE,EAChB,eAAe;IAAC;IAAY;IAAqE;CAA2B,EAC5H,mBAAmB,SAAS,EAC5B,kBAAkB,IAAI,EACtB,YAAY,SAAS,EACrB,cAAc,kBAAkB,EAChC,aAAa,QAAQ,EACrB,aAAa,kBAAkB,EAC/B,oBAAoB,EACpB,kBAAkB,EACnB;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB,MAAM,QAAQ;YACZ,SAAS;gBACP,UAAU;gBACV,SAAS;gBACT,gBAAgB;gBAChB,cAAc;gBACd,QAAQ;gBACR,eAAe;gBACf,WAAW;gBACX,kBAAkB;gBAClB,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;YACA,OAAO;gBACL,UAAU;gBACV,SAAS;gBACT,gBAAgB;gBAChB,cAAc;gBACd,QAAQ;gBACR,eAAe;gBACf,WAAW;gBACX,kBAAkB;gBAClB,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;YACA,QAAQ;gBACN,UAAU;gBACV,SAAS;gBACT,gBAAgB;gBAChB,cAAc;gBACd,QAAQ;gBACR,eAAe;gBACf,WAAW;gBACX,kBAAkB;gBAClB,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;YACA,SAAS;gBACP,UAAU;gBACV,SAAS;gBACT,gBAAgB;gBAChB,cAAc;gBACd,QAAQ;gBACR,eAAe;gBACf,WAAW;gBACX,kBAAkB;gBAClB,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;QACF;QACA,OAAO,KAAK,CAAC,iBAAuC,IAAI,MAAM,OAAO;IACvE;IAEA,wBAAwB;IACxB,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QAExC,MAAM,UAAU,QAAQ,CAAC,EAAE;QAE3B,qBAAqB;QACrB,MAAM,WAAW,QAAQ,IAAI,CAAC,WAAW;QACzC,MAAM,QAAQ,QAAQ,IAAI,KAAK,cAAc,SAAS,QAAQ,CAAC;QAC/D,MAAM,UAAU,QAAQ,IAAI,KAAK,uEAClB,QAAQ,IAAI,KAAK,8BACjB,SAAS,QAAQ,CAAC,YAClB,SAAS,QAAQ,CAAC;QAEjC,IAAI,CAAC,SAAS,CAAC,SAAS;YACtB,MAAM,CAAC,yDAAyD,CAAC;YACjE;QACF;QAEA,qBAAqB;QACrB,IAAI,CAAC,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,IAAI,EAAE,cAAc;YAC/C,MAAM,CAAC,sBAAsB,EAAE,YAAY,SAAS,CAAC;YACrD;QACF;QAEA,0BAA0B;QAC1B,MAAM,iBAAiC;YACrC,MAAM;YACN,QAAQ;YACR,gBAAgB;YAChB,eAAe;QACjB;QAEA,SAAS;YAAC;SAAe;QAEzB,yCAAyC;QACzC,IAAI,cAAc;YAChB,aAAa;gBAAC;aAAQ;QACxB;IACF;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,CAAC;QACzB,iBAAiB,EAAE,MAAM,CAAC,KAAK;QAC/B,IAAI,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,KAAK,GAAG;IACjC;IAEA,gCAAgC;IAChC,MAAM,cAAc;QAClB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,aAAa,KAAK,EAAE,MAAM,EAAE;YAChC,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QACd,MAAM,eAAe,EAAE,YAAY,CAAC,KAAK;QACzC,iBAAiB;IACnB;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,OAAO;QAC3B,MAAM,EAAE,IAAI,EAAE,GAAG;QAEjB,6BAA6B;QAC7B,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,IACZ,EAAE,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,GACrB;oBAAE,GAAG,CAAC;oBAAE,QAAQ;oBAAmB,eAAe;gBAAE,IACpD;QAIR,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,oBAAiB,AAAD,EACrC,MACA,WACA,aACA,cAAc,UACd,YACA,WACA,CAAC;gBACC,kBAAkB;gBAClB,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,IACZ,EAAE,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,GACrB;4BAAE,GAAG,CAAC;4BAAE,eAAe;wBAAS,IAChC;YAGV;YAGF,2BAA2B;YAC3B,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,IACZ,EAAE,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,GACrB;wBACE,GAAG,CAAC;wBACJ,QAAQ;wBACR,eAAe;wBACf,eAAe;oBACjB,IACA;YAIR,IAAI,sBAAsB;gBACxB,qBAAqB;YACvB;QAEF,EAAE,OAAO,OAAO;YACd,yBAAyB;YACzB,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,IACZ,EAAE,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,GACrB;wBACE,GAAG,CAAC;wBACJ,QAAQ;wBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAClD,IACA;YAIR,IAAI,oBAAoB;gBACtB,mBAAmB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D;QACF;IACF;IAEA,sBAAsB;IACtB,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,YAAa,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,IAAI,KAAK;IACpE;IAEA,8BAA8B;IAC9B,MAAM,2BAA2B,OAAO;QACtC,EAAE,eAAe;QACjB,iBAAiB;QACjB,IAAI;YACF,MAAM,UAAU,MAAM,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;YACzC,IAAI,CAAC,SAAS;gBACZ,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,iBAAiB;QACnB;IACF;IAEA,MAAM,OAAO;IAEb,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,KAAK;gBACL,WAAW,CAAC,UAAU,EAAE,aAAa,aAAa,GAAG,2LAA2L,CAAC;gBACjP,SAAS;gBACT,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,QAAQ;;kCAER,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;4BAAC,MAAM;;;;;;;;;;;kCAExB,6LAAC;wBAAE,WAAU;kCACV,KAAK,QAAQ;;;;;;kCAEhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,6LAAC;gCAAK,WAAU;0CAAQ,KAAK,SAAS;;;;;;;;;;;;kCAExC,6LAAC;wBAAE,WAAU;;4BAA2D;4BACxC;4BAAY;;;;;;;kCAI5C,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BACrB,KAAK,cAAc;;;;;;;oBAIrB,+BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,6LAAC;wBACC,MAAK;wBACL,KAAK;wBACL,UAAU;wBACV,WAAU;wBACV,QAAO;;;;;;;;;;;;YAIV,MAAM,MAAM,GAAG,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA+C,KAAK,YAAY;;;;;;kCAC9E,6LAAC;wBAAG,WAAU;kCACX,MAAM,GAAG,CAAC,CAAC,gBAAgB,sBAC1B,6LAAC;gCAAgD,WAAU;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iJAAA,CAAA,SAAM;wDAAC,WAAU;wDAAoB,MAAM;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;kEAA4C,eAAe,IAAI,CAAC,IAAI;;;;;;;;;;;;0DAEtF,6LAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,IAAI,CAAC,IAAI;;;;;;;;;;;;oCAK3C,iCACC,6LAAC;wCAAI,WAAU;;4CACZ,eAAe,MAAM,KAAK,4BACzB,6LAAC;gDACC,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,cAAc;gDAChB;;kEAEA,6LAAC,iJAAA,CAAA,aAAU;wDAAC,MAAM;;;;;;oDACjB,KAAK,aAAa;;;;;;;4CAItB,eAAe,MAAM,KAAK,mCACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iJAAA,CAAA,YAAS;gEAAC,WAAU;gEAA6B,MAAM;;;;;;0EACxD,6LAAC;gEAAK,WAAU;0EAAyB,KAAK,gBAAgB;;;;;;;;;;;;kEAEhE,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,eAAe,aAAa,IAAI,EAAE,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAG5D,6LAAC;wDAAK,WAAU;;4DACb,eAAe,aAAa,IAAI;4DAAE;;;;;;;;;;;;;4CAKxC,eAAe,MAAM,KAAK,iCACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iJAAA,CAAA,UAAO;wDAAC,MAAM;;;;;;kEACf,6LAAC;wDAAK,WAAU;;4DAAW,KAAK,OAAO;4DAAC;;;;;;;;;;;;;4CAI3C,eAAe,MAAM,KAAK,+BACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iJAAA,CAAA,MAAG;gEAAC,MAAM;;;;;;0EACX,6LAAC;gEAAK,WAAU;0EAAW,KAAK,KAAK;;;;;;;;;;;;oDAEtC,eAAe,KAAK,kBACnB,6LAAC;wDAAI,WAAU;kEACZ,eAAe,KAAK;;;;;;;;;;;;;;;;;;kDASjC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,SAAS,CAAC;gDACR,EAAE,eAAe;gDACjB,WAAW,eAAe,IAAI,CAAC,IAAI;4CACrC;sDAEA,cAAA,6LAAC,iJAAA,CAAA,MAAG;gDAAC,MAAM;;;;;;;;;;;;;;;;;+BA7ER,GAAG,eAAe,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;AAuF7D;GAjZM;KAAA;uCAmZS"}}, {"offset": {"line": 1532, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1538, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/adminsidebar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { PiDatabase, PiPlus, PiList, PiGear } from 'react-icons/pi';\r\n\r\ninterface AdminSidebarProps {\r\n  currentView?: string;\r\n  onViewChange?: (view: string) => void;\r\n  isOpen?: boolean;\r\n  onToggle?: () => void;\r\n}\r\n\r\nconst AdminSidebar: React.FC<AdminSidebarProps> = ({\r\n  currentView = 'create',\r\n  onViewChange,\r\n  isOpen = true,\r\n  onToggle\r\n}) => {\r\n  const router = useRouter();\r\n\r\n  const handleNavigation = (view: string) => {\r\n    if (view === 'show') {\r\n      // Navigate to the show-index route\r\n      router.push('/show-index');\r\n    } else if (view === 'create') {\r\n      // Navigate to the file-upload-standalone route\r\n      router.push('/file-upload-standalone');\r\n    } else if (onViewChange) {\r\n      onViewChange(view);\r\n    }\r\n  };\r\n\r\n  const menuItems = [\r\n    {\r\n      id: 'create',\r\n      label: 'Create Index',\r\n      icon: PiPlus,\r\n      description: 'Upload CSV or Excel files to create new Fiass indexes'\r\n    },\r\n    {\r\n      id: 'show',\r\n      label: 'Show Index',\r\n      icon: PiDatabase,\r\n      description: 'View and manage existing PINE collection entries'\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className={`admin-sidebar w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 fixed left-0 top-0 p-4 h-full z-40 transition-transform duration-300 ${\r\n      isOpen ? 'translate-x-0' : '-translate-x-full'\r\n    } lg:translate-x-0`}>\r\n      {/* Header */}\r\n      <div className=\"sidebar-header mb-8\">\r\n        <div className=\"flex items-center gap-2 mb-2\">\r\n          <PiGear className=\"text-primaryColor text-xl\" />\r\n          <h2 className=\"text-xl font-semibold dark:text-white\">\r\n            Admin Panel\r\n          </h2>\r\n        </div>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n          Manage your FiassDB and data\r\n        </p>\r\n      </div>\r\n\r\n      {/* Navigation Menu */}\r\n      <nav className=\"space-y-2\">\r\n        {menuItems.map((item) => {\r\n          const Icon = item.icon;\r\n          const isActive = currentView === item.id;\r\n\r\n          return (\r\n            <button\r\n              key={item.id}\r\n              onClick={() => handleNavigation(item.id)}\r\n              className={`block w-full text-left px-4 py-3 rounded-md transition-colors group ${\r\n                isActive\r\n                  ? 'bg-primaryColor text-white shadow-sm'\r\n                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'\r\n              }`}\r\n            >\r\n              <div className=\"flex items-center gap-3\">\r\n                <Icon\r\n                  className={`text-lg ${\r\n                    isActive ? 'text-white' : 'text-primaryColor'\r\n                  }`}\r\n                />\r\n                <div>\r\n                  <div className=\"font-medium\">{item.label}</div>\r\n                  <div className={`text-xs mt-1 ${\r\n                    isActive\r\n                      ? 'text-white/80'\r\n                      : 'text-gray-500 dark:text-gray-400'\r\n                  }`}>\r\n                    {item.description}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </button>\r\n          );\r\n        })}\r\n      </nav>\r\n\r\n      {/* Footer */}\r\n      {/* <div className=\"absolute bottom-4 left-4 right-4\">\r\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3\">\r\n          <div className=\"flex items-center gap-2 mb-2\">\r\n            <PiList className=\"text-primaryColor\" />\r\n            <span className=\"text-sm font-medium dark:text-white\">Quick Info</span>\r\n          </div>\r\n          <p className=\"text-xs text-gray-600 dark:text-gray-400\">\r\n            Use this panel to manage your Pinecone vector databases and PINE collection data.\r\n          </p>\r\n        </div>\r\n      </div> */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminSidebar;"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAYA,MAAM,eAA4C,CAAC,EACjD,cAAc,QAAQ,EACtB,YAAY,EACZ,SAAS,IAAI,EACb,QAAQ,EACT;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS,QAAQ;YACnB,mCAAmC;YACnC,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,SAAS,UAAU;YAC5B,+CAA+C;YAC/C,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,cAAc;YACvB,aAAa;QACf;IACF;IAEA,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,MAAM,iJAAA,CAAA,SAAM;YACZ,aAAa;QACf;QACA;YACE,IAAI;YAC<PERSON>,OAAO;YACP,MAAM,iJAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,kKAAkK,EACjL,SAAS,kBAAkB,oBAC5B,iBAAiB,CAAC;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iJAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;;;;;;;kCAIxD,6LAAC;wBAAE,WAAU;kCAA2C;;;;;;;;;;;;0BAM1D,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC;oBACd,MAAM,OAAO,KAAK,IAAI;oBACtB,MAAM,WAAW,gBAAgB,KAAK,EAAE;oBAExC,qBACE,6LAAC;wBAEC,SAAS,IAAM,iBAAiB,KAAK,EAAE;wBACvC,WAAW,CAAC,oEAAoE,EAC9E,WACI,yCACA,6EACJ;kCAEF,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAW,CAAC,QAAQ,EAClB,WAAW,eAAe,qBAC1B;;;;;;8CAEJ,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAe,KAAK,KAAK;;;;;;sDACxC,6LAAC;4CAAI,WAAW,CAAC,aAAa,EAC5B,WACI,kBACA,oCACJ;sDACC,KAAK,WAAW;;;;;;;;;;;;;;;;;;uBArBlB,KAAK,EAAE;;;;;gBA2BlB;;;;;;;;;;;;AAiBR;GAxGM;;QAMW,qIAAA,CAAA,YAAS;;;KANpB;uCA0GS"}}, {"offset": {"line": 1699, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/FileUploadPage.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport FileUploadWithFaiss from './FileUploadWithFaiss';\r\nimport AdminSidebar from './adminsidebar';\r\nimport { uploadCSVToFaiss, formatFileSize, fetchEmails, checkIndexExists, getEmbeddingModels, createPineCollectionEntry } from '@/services/fileUploadService';\r\n\r\nconst FileUploadPage: React.FC = () => {\r\n  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);\r\n  const [uploadStatus, setUploadStatus] = useState<Record<string, any>>({});\r\n  const [processingStages, setProcessingStages] = useState<Record<string, any>>({});\r\n  const [selectedClient, setSelectedClient] = useState('');\r\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\r\n  const [emails, setEmails] = useState<string[]>([]);\r\n  const [isLoadingEmails, setIsLoadingEmails] = useState(false);\r\n  const [emailError, setEmailError] = useState('');\r\n  const [indexExists, setIndexExists] = useState(false);\r\n  const [isCheckingIndex, setIsCheckingIndex] = useState(false);\r\n  const [showUpdateModal, setShowUpdateModal] = useState(false);\r\n  const [updateMode, setUpdateMode] = useState<string | null>(null);\r\n  const [indexName, setIndexName] = useState('');\r\n  const [embedModel, setEmbedModel] = useState('all-MiniLM-L6-v2');\r\n  const [availableModels, setAvailableModels] = useState<Record<string, any>>({});\r\n  const [abortController, setAbortController] = useState<AbortController | null>(null);\r\n  const [currentView, setCurrentView] = useState('create');\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [apiKeyError, setApiKeyError] = useState('');\r\n\r\n  // Fetch emails and embedding models when component mounts\r\n  useEffect(() => {\r\n    const getEmails = async () => {\r\n      setIsLoadingEmails(true);\r\n      setEmailError('');\r\n      try {\r\n        const emailList = await fetchEmails();\r\n        setEmails(emailList);\r\n      } catch (error) {\r\n        console.error('Error fetching emails:', error);\r\n        setEmailError('Failed to load emails. Please try again later.');\r\n      } finally {\r\n        setIsLoadingEmails(false);\r\n      }\r\n    };\r\n\r\n    const getModels = async () => {\r\n      try {\r\n        const modelsData = await getEmbeddingModels();\r\n        if (modelsData.success) {\r\n          setAvailableModels(modelsData.models);\r\n        } else {\r\n          // Set fallback models if API response doesn't indicate success\r\n          setAvailableModels({\r\n            \"all-MiniLM-L6-v2\": {\r\n              \"name\": \"all-MiniLM-L6-v2\",\r\n              \"description\": \"Sentence Transformers model for semantic similarity\",\r\n              \"dimension\": 384\r\n            }\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching embedding models:', error);\r\n        // Set fallback models when API call fails\r\n        setAvailableModels({\r\n          \"all-MiniLM-L6-v2\": {\r\n            \"name\": \"all-MiniLM-L6-v2\",\r\n            \"description\": \"Sentence Transformers model for semantic similarity\",\r\n            \"dimension\": 384\r\n          }\r\n        });\r\n      }\r\n    };\r\n\r\n    getEmails();\r\n    getModels();\r\n  }, []);\r\n\r\n  // Check if index exists when index name is provided\r\n  useEffect(() => {\r\n    if (indexName.trim()) {\r\n      checkIfIndexExists();\r\n    }\r\n  }, [indexName]);\r\n\r\n  // Define processing stages\r\n  const stages = [\r\n    { id: 'uploading', label: 'Uploading File', description: 'Transferring file to server' },\r\n    { id: 'processing', label: 'Processing Data', description: 'Parsing CSV and preparing vectors' },\r\n    { id: 'indexing', label: 'Creating Index', description: 'Building FAISS vector database' },\r\n    { id: 'complete', label: 'Complete', description: 'Data successfully indexed and ready for queries' }\r\n  ];\r\n\r\n  // Check if index exists\r\n  const checkIfIndexExists = async () => {\r\n    if (!indexName.trim()) {\r\n      return;\r\n    }\r\n\r\n    // Store the index name in localStorage for immediate use\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('faiss_index_name', indexName);\r\n      console.log(`Set index_name in localStorage: ${indexName}`);\r\n    }\r\n\r\n    setIsCheckingIndex(true);\r\n    try {\r\n      console.log(`Checking if FAISS index exists: ${indexName}`);\r\n      const { exists } = await checkIndexExists(indexName, selectedClient, embedModel);\r\n      setIndexExists(exists);\r\n\r\n      if (exists) {\r\n        console.log(`Index ${indexName} exists, showing update modal`);\r\n        console.log(`Index ${indexName} exists, showing update modal`);\r\n        setShowUpdateModal(true);\r\n      } else {\r\n        console.log(`Index ${indexName} does not exist, will create new index`);\r\n        console.log(`Index ${indexName} does not exist, will create new index`);\r\n        // If index doesn't exist, set update mode to null\r\n        setUpdateMode(null);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error checking if index exists:', error);\r\n    } finally {\r\n      setIsCheckingIndex(false);\r\n    }\r\n  };\r\n\r\n  // Handle upload cancellation\r\n  const handleStopUpload = () => {\r\n    if (abortController) {\r\n      abortController.abort();\r\n      setAbortController(null);\r\n      if (selectedFile) {\r\n        setUploadStatus(prev => ({\r\n          ...prev,\r\n          [selectedFile.name]: {\r\n            status: 'error',\r\n            progress: 0,\r\n            message: 'Upload cancelled by user'\r\n          }\r\n        }));\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle submit button click\r\n  const handleSubmit = async () => {\r\n    if (!selectedFile) {\r\n      alert('Please select a file first');\r\n      return;\r\n    }\r\n\r\n    if (!indexName.trim()) {\r\n      alert('Please provide an index name');\r\n      return;\r\n    }\r\n\r\n    // Store the configuration in PINE collection with new structure\r\n    try {\r\n      console.log(`Storing configuration in PINE collection: embedModel=${embedModel}, indexName=${indexName}, clientEmail=${selectedClient}`);\r\n\r\n      // Create PINE collection entry with new structure:\r\n      // api_key -> embedding model name\r\n      // index_name -> provided index name\r\n      // client -> email which user selects\r\n      await createPineCollectionEntry(embedModel, indexName, selectedClient);\r\n      console.log('Successfully stored configuration in PINE collection');\r\n\r\n      // Also store in localStorage for immediate use\r\n      if (typeof window !== 'undefined') {\r\n        localStorage.setItem('faiss_index_name', indexName);\r\n        localStorage.setItem('faiss_embed_model', embedModel);\r\n        localStorage.setItem('faiss_client_email', selectedClient);\r\n        console.log(`Stored credentials in localStorage: index_name=${indexName}, embed_model=${embedModel}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error storing configuration in PINE collection:', error);\r\n      // Continue with upload even if PINE storage fails\r\n    }\r\n\r\n    const controller = new AbortController();\r\n    setAbortController(controller);\r\n    if (!selectedFile) {\r\n      alert('Please select a file first');\r\n      return;\r\n    }\r\n\r\n    if (!selectedClient) {\r\n      alert('Please select a client');\r\n      return;\r\n    }\r\n\r\n    // Print data to console in JSON format\r\n    const data = {\r\n      index_name: indexName,\r\n      email: selectedClient,\r\n      embed_model: embedModel,\r\n      update_mode: updateMode\r\n    };\r\n\r\n    console.log(JSON.stringify(data, null, 2));\r\n\r\n    // Add the file to the uploadedFiles state\r\n    setUploadedFiles([selectedFile]);\r\n\r\n    // Set initial upload status\r\n    setUploadStatus({\r\n      [selectedFile.name]: {\r\n        status: 'uploading',\r\n        progress: 0,\r\n        message: `Starting upload to FAISS index: ${indexName}...`\r\n      }\r\n    });\r\n\r\n    // Initialize processing stages\r\n    setProcessingStages({\r\n      [selectedFile.name]: {\r\n        currentStage: 'uploading',\r\n        startTime: new Date()\r\n      }\r\n    });\r\n\r\n    // Simulate progress updates for better UX\r\n    let progress = 0;\r\n    const progressInterval = setInterval(() => {\r\n      progress += 5;\r\n      if (progress <= 90) {\r\n        setUploadStatus(prev => ({\r\n          ...prev,\r\n          [selectedFile.name]: {\r\n            ...prev[selectedFile.name],\r\n            progress,\r\n            message: `Uploading and processing... ${progress}%`\r\n          }\r\n        }));\r\n\r\n        // Update processing stage based on progress\r\n        if (progress < 30) {\r\n          setProcessingStages(prev => ({\r\n            ...prev,\r\n            [selectedFile.name]: {\r\n              ...prev[selectedFile.name],\r\n              currentStage: 'uploading'\r\n            }\r\n          }));\r\n        } else if (progress < 60) {\r\n          setProcessingStages(prev => ({\r\n            ...prev,\r\n            [selectedFile.name]: {\r\n              ...prev[selectedFile.name],\r\n              currentStage: 'processing'\r\n            }\r\n          }));\r\n        } else {\r\n          setProcessingStages(prev => ({\r\n            ...prev,\r\n            [selectedFile.name]: {\r\n              ...prev[selectedFile.name],\r\n              currentStage: 'indexing'\r\n            }\r\n          }));\r\n        }\r\n      }\r\n    }, 300);\r\n\r\n    // Upload the file to FAISS with client information, index name, and update mode\r\n    uploadCSVToFaiss(selectedFile, selectedClient, indexName, updateMode, controller.signal, undefined, embedModel)\r\n      .then(response => {\r\n        clearInterval(progressInterval);\r\n\r\n        // Log the complete data including email information and update mode\r\n        const completeData = {\r\n          index_name: indexName,\r\n          email: response.client || selectedClient,\r\n          vector_count: response.vectorCount || 0,\r\n          embed_model: embedModel,\r\n          update_mode: updateMode\r\n        };\r\n\r\n        console.log('CSV uploaded to FAISS:', JSON.stringify(completeData, null, 2));\r\n\r\n        // Update localStorage with the correct index name\r\n        if (typeof window !== 'undefined') {\r\n          localStorage.setItem('faiss_index_name', indexName);\r\n          localStorage.setItem('faiss_embed_model', embedModel);\r\n          localStorage.setItem('faiss_client_email', selectedClient);\r\n          console.log(`Updated credentials in localStorage: index_name=${indexName}, embed_model=${embedModel}`);\r\n        }\r\n\r\n        // Update processing stages\r\n        setProcessingStages(prev => ({\r\n          ...prev,\r\n          [selectedFile.name]: {\r\n            ...prev[selectedFile.name],\r\n            currentStage: 'complete',\r\n            endTime: new Date(),\r\n            processingTime: ((new Date().getTime() - prev[selectedFile.name].startTime.getTime()) / 1000).toFixed(1) + ' seconds', // in seconds\r\n            timestamp: new Date().toLocaleString()\r\n          }\r\n        }));\r\n\r\n        // Update upload status\r\n        setUploadStatus(prev => ({\r\n          ...prev,\r\n          [selectedFile.name]: {\r\n            status: 'success',\r\n            progress: 100,\r\n            message: `Successfully uploaded to FAISS index: ${indexName}`,\r\n            vectorCount: response.vectorCount || 0\r\n          }\r\n        }));\r\n      })\r\n      .catch(error => {\r\n        clearInterval(progressInterval);\r\n        if (error.name === 'AbortError') {\r\n          console.log('Upload cancelled by user');\r\n          return;\r\n        }\r\n\r\n        console.error('Error uploading CSV to FAISS:', error);\r\n\r\n        // Update upload status\r\n        setUploadStatus(prev => ({\r\n          ...prev,\r\n          [selectedFile.name]: {\r\n            status: 'error',\r\n            progress: 0,\r\n            message: `Error: ${error.message}`\r\n          }\r\n        }));\r\n      });\r\n  };\r\n\r\n  // Render upload status item\r\n  const renderUploadStatusItem = (fileName: string, status: any) => {\r\n    const details = processingStages[fileName] || {};\r\n\r\n    return (\r\n      <div key={fileName} className={`upload-status-item ${status.status}`}>\r\n        <div className=\"status-header\">\r\n          <span className=\"file-name\">{fileName}</span>\r\n          <div className=\"file-status\">\r\n          {status.status === 'uploading' && (\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"uploading\">\r\n                <span className=\"spinner\"></span>\r\n                Uploading...\r\n              </span>\r\n              <button\r\n                onClick={handleStopUpload}\r\n                className=\"stop-button text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 text-sm font-medium px-2 py-1 rounded-md border border-red-200 dark:border-red-800 hover:border-red-300 dark:hover:border-red-700 transition-colors\"\r\n              >\r\n                Stop Upload\r\n              </button>\r\n            </div>\r\n          )}\r\n            {status.status === 'success' && <span className=\"success\">Completed</span>}\r\n            {status.status === 'error' && <span className=\"error\">Failed</span>}\r\n          </div>\r\n        </div>\r\n\r\n        {status.status === 'uploading' && (\r\n          <div className=\"progress-container\">\r\n            <div className=\"progress-bar\" style={{ width: `${status.progress}%` }}></div>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"status-message\">{status.message}</div>\r\n\r\n        {details.currentStage && (\r\n          <div className=\"processing-stages\">\r\n            {stages.map((stage, index) => (\r\n              <div key={stage.id} className=\"stage-item\">\r\n                <div className={`stage-indicator ${details.currentStage === stage.id || (details.currentStage === 'complete' && stage.id !== 'complete') ? 'active' : ''} ${details.currentStage === 'complete' && stage.id === 'complete' ? 'complete' : ''}`}>\r\n                  {details.currentStage === 'complete' && stage.id === 'complete' ? '✓' : index + 1}\r\n                </div>\r\n                <div className=\"stage-label\">{stage.label}</div>\r\n                <div className=\"stage-description\">{stage.description}</div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n\r\n        {details.currentStage === 'complete' && (\r\n          <div className=\"upload-details\">\r\n            <div className=\"detail-item\">\r\n              <span className=\"detail-label\">Vector Count:</span>\r\n              <span className=\"detail-value\">{status.vectorCount || 'N/A'}</span>\r\n            </div>\r\n            <div className=\"detail-item\">\r\n              <span className=\"detail-label\">Processing Time:</span>\r\n              <span className=\"detail-value\">{details.processingTime}</span>\r\n            </div>\r\n            <div className=\"detail-item\">\r\n              <span className=\"detail-label\">Completed At:</span>\r\n              <span className=\"detail-value\">{details.timestamp}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Handle update mode selection\r\n  const handleUpdateModeSelect = (mode: string) => {\r\n    setUpdateMode(mode);\r\n    setShowUpdateModal(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"file-upload-page flex min-h-screen\">\r\n      {/* Admin Sidebar */}\r\n      <AdminSidebar\r\n        currentView={currentView}\r\n        onViewChange={setCurrentView}\r\n        isOpen={sidebarOpen}\r\n        onToggle={() => setSidebarOpen(!sidebarOpen)}\r\n      />\r\n\r\n      {/* Overlay for mobile */}\r\n      {sidebarOpen && (\r\n        <div\r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden\"\r\n          onClick={() => setSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex-1 lg:ml-64 overflow-auto bg-white dark:bg-gray-800\">\r\n        {/* Mobile menu button */}\r\n        <div className=\"lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4\">\r\n          <button\r\n            onClick={() => setSidebarOpen(!sidebarOpen)}\r\n            className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-primaryColor transition-colors\"\r\n          >\r\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"max-w-4xl mx-auto p-6\">\r\n          {/* Update Mode Modal */}\r\n          {showUpdateModal && (\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n              <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full mx-4\">\r\n                <h3 className=\"text-xl font-semibold mb-4 text-gray-900 dark:text-white\">Index Already Exists</h3>\r\n                <p className=\"mb-4 text-gray-600 dark:text-gray-300\">\r\n                  An index with the name <span className=\"font-semibold\">{indexName}</span> already exists.\r\n                  How would you like to proceed?\r\n                </p>\r\n                <div className=\"flex flex-col space-y-3\">\r\n                  <button\r\n                    onClick={() => handleUpdateModeSelect('update')}\r\n                    className=\"bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-colors\"\r\n                  >\r\n                    Update Existing Index\r\n                    <p className=\"text-xs mt-1 text-blue-100\">\r\n                      Keep existing data and add new vectors\r\n                    </p>\r\n                  </button>\r\n                  <button\r\n                    onClick={() => handleUpdateModeSelect('new')}\r\n                    className=\"bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md transition-colors\"\r\n                  >\r\n                    Replace Existing Index\r\n                    <p className=\"text-xs mt-1 text-red-100\">\r\n                      Delete all existing data and start fresh\r\n                    </p>\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setShowUpdateModal(false)}\r\n                    className=\"bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-md transition-colors\"\r\n                  >\r\n                    Cancel\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          {/* Update Mode Modal */}\r\n          {showUpdateModal && (\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n              <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full mx-4\">\r\n                <h3 className=\"text-xl font-semibold mb-4 text-gray-900 dark:text-white\">Index Already Exists</h3>\r\n                <p className=\"mb-4 text-gray-600 dark:text-gray-300\">\r\n                  An index with the name <span className=\"font-semibold\">{indexName}</span> already exists.\r\n                  How would you like to proceed?\r\n                </p>\r\n                <div className=\"flex flex-col space-y-3\">\r\n                  <button\r\n                    onClick={() => handleUpdateModeSelect('update')}\r\n                    className=\"bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-colors\"\r\n                  >\r\n                    Update Existing Index\r\n                    <p className=\"text-xs mt-1 text-blue-100\">\r\n                      Keep existing data and add new vectors\r\n                    </p>\r\n                  </button>\r\n                  <button\r\n                    onClick={() => handleUpdateModeSelect('new')}\r\n                    className=\"bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md transition-colors\"\r\n                  >\r\n                    Replace Existing Index\r\n                    <p className=\"text-xs mt-1 text-red-100\">\r\n                      Delete all existing data and start fresh\r\n                    </p>\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setShowUpdateModal(false)}\r\n                    className=\"bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-md transition-colors\"\r\n                  >\r\n                    Cancel\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Header Section */}\r\n          <div className=\"file-upload-header text-center mb-8\">\r\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">CSV & Excel File Upload</h1>\r\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4 max-w-2xl mx-auto\">\r\n              Upload a CSV or Excel file to FAISS vector database for AI-powered search and analysis\r\n            </p>\r\n            <div className=\"upload-restriction-badge bg-primaryColor text-white text-sm font-medium px-4 py-2 rounded-full inline-block\">\r\n              <span>One file at a time</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Client Selection Section */}\r\n          <div className=\"client-selection mb-6\">\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600\">\r\n              <label htmlFor=\"client-dropdown\" className=\"block font-medium mb-2 text-gray-900 dark:text-white\">\r\n                Select Email:\r\n              </label>\r\n              <select\r\n                id=\"client-dropdown\"\r\n                value={selectedClient}\r\n                onChange={(e) => setSelectedClient(e.target.value)}\r\n                className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor focus:border-primaryColor bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-colors\"\r\n                disabled={isLoadingEmails}\r\n              >\r\n                <option value=\"\">-- Select Email --</option>\r\n                {isLoadingEmails ? (\r\n                  <option value=\"\" disabled>Loading emails...</option>\r\n                ) : emails.length > 0 ? (\r\n                  emails.map((email, index) => (\r\n                    <option key={index} value={email}>\r\n                      {email}\r\n                    </option>\r\n                  ))\r\n                ) : (\r\n                  <option value=\"\" disabled>No emails available</option>\r\n                )}\r\n              </select>\r\n              {emailError && <div className=\"text-red-500 text-sm mt-2\">{emailError}</div>}\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-2\">\r\n                Select the email address to associate with this upload.\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Embedding Model Selection Section */}\r\n          <div className=\"embedding-model-section mb-6\">\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600\">\r\n              <label htmlFor=\"embed-model\" className=\"block font-medium mb-2 text-gray-900 dark:text-white\">\r\n                Embedding Model:\r\n              </label>\r\n              <select\r\n                id=\"embed-model\"\r\n                value={embedModel}\r\n                onChange={(e) => setEmbedModel(e.target.value)}\r\n                className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor focus:border-primaryColor bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-colors\"\r\n              >\r\n                {Object.entries(availableModels).map(([key, model]) => (\r\n                  <option key={key} value={key}>\r\n                    {model.name} ({model.dimension}D) - {model.description}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-2\">\r\n                Choose the embedding model for converting text to vectors. Different models have different dimensions and capabilities.\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Index Name Section */}\r\n          <div className=\"index-name-section mb-6\">\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600\">\r\n              <label htmlFor=\"index-name\" className=\"block font-medium mb-2 text-gray-900 dark:text-white\">\r\n                Provide Index Name:\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"index-name\"\r\n                value={indexName}\r\n                onChange={(e) => {\r\n                  setIndexName(e.target.value);\r\n                  setApiKeyError('');\r\n                }}\r\n                placeholder=\"Enter your index name (e.g., my-data-index)\"\r\n                className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor focus:border-primaryColor bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-colors\"\r\n              />\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-2\">\r\n                Choose a unique name for your FAISS vector index. This will be used to store and retrieve your data.\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* File Upload Section */}\r\n          <div className=\"file-upload-section mb-6\">\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600\">\r\n              <FileUploadWithFaiss\r\n                onFileUpload={(files: File[]) => {\r\n                  if (files && files.length > 0) {\r\n                    // Store the selected file\r\n                    setSelectedFile(files[0]);\r\n\r\n                    // Initialize upload status\r\n                    setUploadStatus({\r\n                      [files[0].name]: {\r\n                        status: 'ready',\r\n                        progress: 0,\r\n                        message: 'File selected, ready to upload'\r\n                      }\r\n                    });\r\n                  }\r\n                }}\r\n                maxFileSize={50} // 50MB\r\n                allowedTypes={['text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']}\r\n                selectedLanguage=\"English\"\r\n                showFaissUpload={!!(selectedFile && selectedClient && indexName.trim())}\r\n                indexName={indexName || 'default'}\r\n                clientEmail={selectedClient}\r\n                updateMode={updateMode}\r\n                embedModel={embedModel}\r\n                onFaissUploadSuccess={async (response) => {\r\n                  console.log('FAISS upload successful:', response);\r\n\r\n                  // Create PINE collection entry after successful upload\r\n                  try {\r\n                    await createPineCollectionEntry(embedModel, indexName, selectedClient);\r\n                    console.log('PINE collection entry created after successful upload');\r\n                  } catch (error) {\r\n                    console.error('Error creating PINE collection entry after upload:', error);\r\n                  }\r\n\r\n                  // Update upload status\r\n                  if (selectedFile) {\r\n                    setUploadStatus(prev => ({\r\n                      ...prev,\r\n                      [selectedFile.name]: {\r\n                        status: 'success',\r\n                        progress: 100,\r\n                        message: `Successfully uploaded to FAISS index: ${indexName}`,\r\n                        vectorCount: response.vectorCount || 0\r\n                      }\r\n                    }));\r\n\r\n                    // Update processing stages\r\n                    setProcessingStages(prev => ({\r\n                      ...prev,\r\n                      [selectedFile.name]: {\r\n                        ...prev[selectedFile.name],\r\n                        currentStage: 'complete',\r\n                        endTime: new Date(),\r\n                        processingTime: 'Completed',\r\n                        timestamp: new Date().toLocaleString()\r\n                      }\r\n                    }));\r\n                  }\r\n\r\n                  // Store configuration in localStorage\r\n                  if (typeof window !== 'undefined') {\r\n                    localStorage.setItem('faiss_index_name', indexName);\r\n                    localStorage.setItem('faiss_embed_model', embedModel);\r\n                    localStorage.setItem('faiss_client_email', selectedClient);\r\n\r\n                    // Trigger a custom event to notify other components about the new upload\r\n                    window.dispatchEvent(new CustomEvent('faissIndexUpdated', {\r\n                      detail: { indexName, clientEmail: selectedClient, embedModel }\r\n                    }));\r\n                  }\r\n                }}\r\n                onFaissUploadError={(error) => {\r\n                  console.error('FAISS upload error:', error);\r\n\r\n                  // Update upload status\r\n                  if (selectedFile) {\r\n                    setUploadStatus(prev => ({\r\n                      ...prev,\r\n                      [selectedFile.name]: {\r\n                        status: 'error',\r\n                        progress: 0,\r\n                        message: `Error: ${error}`\r\n                      }\r\n                    }));\r\n                  }\r\n                }}\r\n              />\r\n\r\n              {/* Show update mode indicator if selected */}\r\n              {updateMode && (\r\n                <div className={`update-mode-indicator mt-4 p-3 rounded-md ${\r\n                  updateMode === 'update' ? 'bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800' :\r\n                  'bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800'\r\n                }`}>\r\n                  <div className=\"flex items-center\">\r\n                    <span className={`inline-block w-3 h-3 rounded-full mr-2 ${\r\n                      updateMode === 'update' ? 'bg-blue-500' : 'bg-red-500'\r\n                    }`}></span>\r\n                    <span className={`font-medium ${\r\n                      updateMode === 'update' ? 'text-blue-700 dark:text-blue-300' : 'text-red-700 dark:text-red-300'\r\n                    }`}>\r\n                      {updateMode === 'update' ? 'Update Existing Index' : 'Replace Existing Index'}\r\n                    </span>\r\n                  </div>\r\n                  <p className={`text-xs mt-1 ${\r\n                    updateMode === 'update' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400'\r\n                  }`}>\r\n                    {updateMode === 'update'\r\n                      ? 'New data will be added to the existing index without deleting current data.'\r\n                      : 'All existing data will be deleted before uploading new data.'}\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Upload Status */}\r\n          {Object.keys(uploadStatus).length > 0 && (\r\n            <div className=\"upload-status-container bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600\">\r\n              <h3 className=\"text-xl font-semibold mb-4 pb-2 border-b border-gray-200 dark:border-gray-600 text-gray-900 dark:text-white\">\r\n                Upload Status\r\n              </h3>\r\n              <div className=\"upload-status-list\">\r\n                {Object.entries(uploadStatus).map(([fileName, status]) =>\r\n                  renderUploadStatusItem(fileName, status)\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUploadPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IACvE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC/E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,mBAAmB;oBACnB,cAAc;oBACd,IAAI;wBACF,MAAM,YAAY,MAAM,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;wBAClC,UAAU;oBACZ,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0BAA0B;wBACxC,cAAc;oBAChB,SAAU;wBACR,mBAAmB;oBACrB;gBACF;;YAEA,MAAM;sDAAY;oBAChB,IAAI;wBACF,MAAM,aAAa,MAAM,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD;wBAC1C,IAAI,WAAW,OAAO,EAAE;4BACtB,mBAAmB,WAAW,MAAM;wBACtC,OAAO;4BACL,+DAA+D;4BAC/D,mBAAmB;gCACjB,oBAAoB;oCAClB,QAAQ;oCACR,eAAe;oCACf,aAAa;gCACf;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,0CAA0C;wBAC1C,mBAAmB;4BACjB,oBAAoB;gCAClB,QAAQ;gCACR,eAAe;gCACf,aAAa;4BACf;wBACF;oBACF;gBACF;;YAEA;YACA;QACF;mCAAG,EAAE;IAEL,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,IAAI,IAAI;gBACpB;YACF;QACF;mCAAG;QAAC;KAAU;IAEd,2BAA2B;IAC3B,MAAM,SAAS;QACb;YAAE,IAAI;YAAa,OAAO;YAAkB,aAAa;QAA8B;QACvF;YAAE,IAAI;YAAc,OAAO;YAAmB,aAAa;QAAoC;QAC/F;YAAE,IAAI;YAAY,OAAO;YAAkB,aAAa;QAAiC;QACzF;YAAE,IAAI;YAAY,OAAO;YAAY,aAAa;QAAkD;KACrG;IAED,wBAAwB;IACxB,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB;QACF;QAEA,yDAAyD;QACzD,wCAAmC;YACjC,aAAa,OAAO,CAAC,oBAAoB;YACzC,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,WAAW;QAC5D;QAEA,mBAAmB;QACnB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,WAAW;YAC1D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,gBAAgB;YACrE,eAAe;YAEf,IAAI,QAAQ;gBACV,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,UAAU,6BAA6B,CAAC;gBAC7D,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,UAAU,6BAA6B,CAAC;gBAC7D,mBAAmB;YACrB,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,UAAU,sCAAsC,CAAC;gBACtE,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,UAAU,sCAAsC,CAAC;gBACtE,kDAAkD;gBAClD,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,6BAA6B;IAC7B,MAAM,mBAAmB;QACvB,IAAI,iBAAiB;YACnB,gBAAgB,KAAK;YACrB,mBAAmB;YACnB,IAAI,cAAc;gBAChB,gBAAgB,CAAA,OAAQ,CAAC;wBACvB,GAAG,IAAI;wBACP,CAAC,aAAa,IAAI,CAAC,EAAE;4BACnB,QAAQ;4BACR,UAAU;4BACV,SAAS;wBACX;oBACF,CAAC;YACH;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc;YACjB,MAAM;YACN;QACF;QAEA,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB,MAAM;YACN;QACF;QAEA,gEAAgE;QAChE,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,WAAW,YAAY,EAAE,UAAU,cAAc,EAAE,gBAAgB;YAEvI,mDAAmD;YACnD,kCAAkC;YAClC,oCAAoC;YACpC,qCAAqC;YACrC,MAAM,CAAA,GAAA,gIAAA,CAAA,4BAAyB,AAAD,EAAE,YAAY,WAAW;YACvD,QAAQ,GAAG,CAAC;YAEZ,+CAA+C;YAC/C,wCAAmC;gBACjC,aAAa,OAAO,CAAC,oBAAoB;gBACzC,aAAa,OAAO,CAAC,qBAAqB;gBAC1C,aAAa,OAAO,CAAC,sBAAsB;gBAC3C,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,UAAU,cAAc,EAAE,YAAY;YACtG;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;QACjE,kDAAkD;QACpD;QAEA,MAAM,aAAa,IAAI;QACvB,mBAAmB;QACnB,IAAI,CAAC,cAAc;YACjB,MAAM;YACN;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB,MAAM;YACN;QACF;QAEA,uCAAuC;QACvC,MAAM,OAAO;YACX,YAAY;YACZ,OAAO;YACP,aAAa;YACb,aAAa;QACf;QAEA,QAAQ,GAAG,CAAC,KAAK,SAAS,CAAC,MAAM,MAAM;QAEvC,0CAA0C;QAC1C,iBAAiB;YAAC;SAAa;QAE/B,4BAA4B;QAC5B,gBAAgB;YACd,CAAC,aAAa,IAAI,CAAC,EAAE;gBACnB,QAAQ;gBACR,UAAU;gBACV,SAAS,CAAC,gCAAgC,EAAE,UAAU,GAAG,CAAC;YAC5D;QACF;QAEA,+BAA+B;QAC/B,oBAAoB;YAClB,CAAC,aAAa,IAAI,CAAC,EAAE;gBACnB,cAAc;gBACd,WAAW,IAAI;YACjB;QACF;QAEA,0CAA0C;QAC1C,IAAI,WAAW;QACf,MAAM,mBAAmB,YAAY;YACnC,YAAY;YACZ,IAAI,YAAY,IAAI;gBAClB,gBAAgB,CAAA,OAAQ,CAAC;wBACvB,GAAG,IAAI;wBACP,CAAC,aAAa,IAAI,CAAC,EAAE;4BACnB,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC;4BAC1B;4BACA,SAAS,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC;wBACrD;oBACF,CAAC;gBAED,4CAA4C;gBAC5C,IAAI,WAAW,IAAI;oBACjB,oBAAoB,CAAA,OAAQ,CAAC;4BAC3B,GAAG,IAAI;4BACP,CAAC,aAAa,IAAI,CAAC,EAAE;gCACnB,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC;gCAC1B,cAAc;4BAChB;wBACF,CAAC;gBACH,OAAO,IAAI,WAAW,IAAI;oBACxB,oBAAoB,CAAA,OAAQ,CAAC;4BAC3B,GAAG,IAAI;4BACP,CAAC,aAAa,IAAI,CAAC,EAAE;gCACnB,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC;gCAC1B,cAAc;4BAChB;wBACF,CAAC;gBACH,OAAO;oBACL,oBAAoB,CAAA,OAAQ,CAAC;4BAC3B,GAAG,IAAI;4BACP,CAAC,aAAa,IAAI,CAAC,EAAE;gCACnB,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC;gCAC1B,cAAc;4BAChB;wBACF,CAAC;gBACH;YACF;QACF,GAAG;QAEH,gFAAgF;QAChF,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,cAAc,gBAAgB,WAAW,YAAY,WAAW,MAAM,EAAE,WAAW,YACjG,IAAI,CAAC,CAAA;YACJ,cAAc;YAEd,oEAAoE;YACpE,MAAM,eAAe;gBACnB,YAAY;gBACZ,OAAO,SAAS,MAAM,IAAI;gBAC1B,cAAc,SAAS,WAAW,IAAI;gBACtC,aAAa;gBACb,aAAa;YACf;YAEA,QAAQ,GAAG,CAAC,0BAA0B,KAAK,SAAS,CAAC,cAAc,MAAM;YAEzE,kDAAkD;YAClD,wCAAmC;gBACjC,aAAa,OAAO,CAAC,oBAAoB;gBACzC,aAAa,OAAO,CAAC,qBAAqB;gBAC1C,aAAa,OAAO,CAAC,sBAAsB;gBAC3C,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,UAAU,cAAc,EAAE,YAAY;YACvG;YAEA,2BAA2B;YAC3B,oBAAoB,CAAA,OAAQ,CAAC;oBAC3B,GAAG,IAAI;oBACP,CAAC,aAAa,IAAI,CAAC,EAAE;wBACnB,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC;wBAC1B,cAAc;wBACd,SAAS,IAAI;wBACb,gBAAgB,CAAC,CAAC,IAAI,OAAO,OAAO,KAAK,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,IAAI,EAAE,OAAO,CAAC,KAAK;wBAC3G,WAAW,IAAI,OAAO,cAAc;oBACtC;gBACF,CAAC;YAED,uBAAuB;YACvB,gBAAgB,CAAA,OAAQ,CAAC;oBACvB,GAAG,IAAI;oBACP,CAAC,aAAa,IAAI,CAAC,EAAE;wBACnB,QAAQ;wBACR,UAAU;wBACV,SAAS,CAAC,sCAAsC,EAAE,WAAW;wBAC7D,aAAa,SAAS,WAAW,IAAI;oBACvC;gBACF,CAAC;QACH,GACC,KAAK,CAAC,CAAA;YACL,cAAc;YACd,IAAI,MAAM,IAAI,KAAK,cAAc;gBAC/B,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,QAAQ,KAAK,CAAC,iCAAiC;YAE/C,uBAAuB;YACvB,gBAAgB,CAAA,OAAQ,CAAC;oBACvB,GAAG,IAAI;oBACP,CAAC,aAAa,IAAI,CAAC,EAAE;wBACnB,QAAQ;wBACR,UAAU;wBACV,SAAS,CAAC,OAAO,EAAE,MAAM,OAAO,EAAE;oBACpC;gBACF,CAAC;QACH;IACJ;IAEA,4BAA4B;IAC5B,MAAM,yBAAyB,CAAC,UAAkB;QAChD,MAAM,UAAU,gBAAgB,CAAC,SAAS,IAAI,CAAC;QAE/C,qBACE,6LAAC;YAAmB,WAAW,CAAC,mBAAmB,EAAE,OAAO,MAAM,EAAE;;8BAClE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAa;;;;;;sCAC7B,6LAAC;4BAAI,WAAU;;gCACd,OAAO,MAAM,KAAK,6BACjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAK,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;gCAKF,OAAO,MAAM,KAAK,2BAAa,6LAAC;oCAAK,WAAU;8CAAU;;;;;;gCACzD,OAAO,MAAM,KAAK,yBAAW,6LAAC;oCAAK,WAAU;8CAAQ;;;;;;;;;;;;;;;;;;gBAIzD,OAAO,MAAM,KAAK,6BACjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAe,OAAO;4BAAE,OAAO,GAAG,OAAO,QAAQ,CAAC,CAAC,CAAC;wBAAC;;;;;;;;;;;8BAIxE,6LAAC;oBAAI,WAAU;8BAAkB,OAAO,OAAO;;;;;;gBAE9C,QAAQ,YAAY,kBACnB,6LAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4BAAmB,WAAU;;8CAC5B,6LAAC;oCAAI,WAAW,CAAC,gBAAgB,EAAE,QAAQ,YAAY,KAAK,MAAM,EAAE,IAAK,QAAQ,YAAY,KAAK,cAAc,MAAM,EAAE,KAAK,aAAc,WAAW,GAAG,CAAC,EAAE,QAAQ,YAAY,KAAK,cAAc,MAAM,EAAE,KAAK,aAAa,aAAa,IAAI;8CAC3O,QAAQ,YAAY,KAAK,cAAc,MAAM,EAAE,KAAK,aAAa,MAAM,QAAQ;;;;;;8CAElF,6LAAC;oCAAI,WAAU;8CAAe,MAAM,KAAK;;;;;;8CACzC,6LAAC;oCAAI,WAAU;8CAAqB,MAAM,WAAW;;;;;;;2BAL7C,MAAM,EAAE;;;;;;;;;;gBAWvB,QAAQ,YAAY,KAAK,4BACxB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAe;;;;;;8CAC/B,6LAAC;oCAAK,WAAU;8CAAgB,OAAO,WAAW,IAAI;;;;;;;;;;;;sCAExD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAe;;;;;;8CAC/B,6LAAC;oCAAK,WAAU;8CAAgB,QAAQ,cAAc;;;;;;;;;;;;sCAExD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAe;;;;;;8CAC/B,6LAAC;oCAAK,WAAU;8CAAgB,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;WAzD/C;;;;;IA+Dd;IAEA,+BAA+B;IAC/B,MAAM,yBAAyB,CAAC;QAC9B,cAAc;QACd,mBAAmB;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,8HAAA,CAAA,UAAY;gBACX,aAAa;gBACb,cAAc;gBACd,QAAQ;gBACR,UAAU,IAAM,eAAe,CAAC;;;;;;YAIjC,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,eAAe,CAAC;4BAC/B,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;kCAK3E,6LAAC;wBAAI,WAAU;;4BAEZ,iCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2D;;;;;;sDACzE,6LAAC;4CAAE,WAAU;;gDAAwC;8DAC5B,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;gDAAiB;;;;;;;sDAG3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,uBAAuB;oDACtC,WAAU;;wDACX;sEAEC,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;8DAI5C,6LAAC;oDACC,SAAS,IAAM,uBAAuB;oDACtC,WAAU;;wDACX;sEAEC,6LAAC;4DAAE,WAAU;sEAA4B;;;;;;;;;;;;8DAI3C,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;4BAQR,iCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2D;;;;;;sDACzE,6LAAC;4CAAE,WAAU;;gDAAwC;8DAC5B,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;gDAAiB;;;;;;;sDAG3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,uBAAuB;oDACtC,WAAU;;wDACX;sEAEC,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;8DAI5C,6LAAC;oDACC,SAAS,IAAM,uBAAuB;oDACtC,WAAU;;wDACX;sEAEC,6LAAC;4DAAE,WAAU;sEAA4B;;;;;;;;;;;;8DAI3C,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAST,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDACtE,6LAAC;wCAAE,WAAU;kDAA0D;;;;;;kDAGvE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAKV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAAuD;;;;;;sDAGlG,6LAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,WAAU;4CACV,UAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,gCACC,6LAAC;oDAAO,OAAM;oDAAG,QAAQ;8DAAC;;;;;2DACxB,OAAO,MAAM,GAAG,IAClB,OAAO,GAAG,CAAC,CAAC,OAAO,sBACjB,6LAAC;wDAAmB,OAAO;kEACxB;uDADU;;;;8EAKf,6LAAC;oDAAO,OAAM;oDAAG,QAAQ;8DAAC;;;;;;;;;;;;wCAG7B,4BAAc,6LAAC;4CAAI,WAAU;sDAA6B;;;;;;sDAC3D,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;;;;;;;;;;;;0CAOjE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAAuD;;;;;;sDAG9F,6LAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;sDAET,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAChD,6LAAC;oDAAiB,OAAO;;wDACtB,MAAM,IAAI;wDAAC;wDAAG,MAAM,SAAS;wDAAC;wDAAM,MAAM,WAAW;;mDAD3C;;;;;;;;;;sDAKjB,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;;;;;;;;;;;;0CAOjE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAa,WAAU;sDAAuD;;;;;;sDAG7F,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC;gDACT,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC3B,eAAe;4CACjB;4CACA,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;;;;;;;;;;;;0CAOjE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,UAAmB;4CAClB,cAAc,CAAC;gDACb,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;oDAC7B,0BAA0B;oDAC1B,gBAAgB,KAAK,CAAC,EAAE;oDAExB,2BAA2B;oDAC3B,gBAAgB;wDACd,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;4DACf,QAAQ;4DACR,UAAU;4DACV,SAAS;wDACX;oDACF;gDACF;4CACF;4CACA,aAAa;4CACb,cAAc;gDAAC;gDAAY;gDAAqE;6CAA2B;4CAC3H,kBAAiB;4CACjB,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,kBAAkB,UAAU,IAAI,EAAE;4CACtE,WAAW,aAAa;4CACxB,aAAa;4CACb,YAAY;4CACZ,YAAY;4CACZ,sBAAsB,OAAO;gDAC3B,QAAQ,GAAG,CAAC,4BAA4B;gDAExC,uDAAuD;gDACvD,IAAI;oDACF,MAAM,CAAA,GAAA,gIAAA,CAAA,4BAAyB,AAAD,EAAE,YAAY,WAAW;oDACvD,QAAQ,GAAG,CAAC;gDACd,EAAE,OAAO,OAAO;oDACd,QAAQ,KAAK,CAAC,sDAAsD;gDACtE;gDAEA,uBAAuB;gDACvB,IAAI,cAAc;oDAChB,gBAAgB,CAAA,OAAQ,CAAC;4DACvB,GAAG,IAAI;4DACP,CAAC,aAAa,IAAI,CAAC,EAAE;gEACnB,QAAQ;gEACR,UAAU;gEACV,SAAS,CAAC,sCAAsC,EAAE,WAAW;gEAC7D,aAAa,SAAS,WAAW,IAAI;4DACvC;wDACF,CAAC;oDAED,2BAA2B;oDAC3B,oBAAoB,CAAA,OAAQ,CAAC;4DAC3B,GAAG,IAAI;4DACP,CAAC,aAAa,IAAI,CAAC,EAAE;gEACnB,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC;gEAC1B,cAAc;gEACd,SAAS,IAAI;gEACb,gBAAgB;gEAChB,WAAW,IAAI,OAAO,cAAc;4DACtC;wDACF,CAAC;gDACH;gDAEA,sCAAsC;gDACtC,wCAAmC;oDACjC,aAAa,OAAO,CAAC,oBAAoB;oDACzC,aAAa,OAAO,CAAC,qBAAqB;oDAC1C,aAAa,OAAO,CAAC,sBAAsB;oDAE3C,yEAAyE;oDACzE,OAAO,aAAa,CAAC,IAAI,YAAY,qBAAqB;wDACxD,QAAQ;4DAAE;4DAAW,aAAa;4DAAgB;wDAAW;oDAC/D;gDACF;4CACF;4CACA,oBAAoB,CAAC;gDACnB,QAAQ,KAAK,CAAC,uBAAuB;gDAErC,uBAAuB;gDACvB,IAAI,cAAc;oDAChB,gBAAgB,CAAA,OAAQ,CAAC;4DACvB,GAAG,IAAI;4DACP,CAAC,aAAa,IAAI,CAAC,EAAE;gEACnB,QAAQ;gEACR,UAAU;gEACV,SAAS,CAAC,OAAO,EAAE,OAAO;4DAC5B;wDACF,CAAC;gDACH;4CACF;;;;;;wCAID,4BACC,6LAAC;4CAAI,WAAW,CAAC,0CAA0C,EACzD,eAAe,WAAW,+EAC1B,0EACA;;8DACA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,uCAAuC,EACvD,eAAe,WAAW,gBAAgB,cAC1C;;;;;;sEACF,6LAAC;4DAAK,WAAW,CAAC,YAAY,EAC5B,eAAe,WAAW,qCAAqC,kCAC/D;sEACC,eAAe,WAAW,0BAA0B;;;;;;;;;;;;8DAGzD,6LAAC;oDAAE,WAAW,CAAC,aAAa,EAC1B,eAAe,WAAW,qCAAqC,kCAC/D;8DACC,eAAe,WACZ,gFACA;;;;;;;;;;;;;;;;;;;;;;;4BAQb,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,mBAClC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8G;;;;;;kDAG5H,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,UAAU,OAAO,GACnD,uBAAuB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GAnuBM;KAAA;uCAquBS"}}, {"offset": {"line": 2975, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2986, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/public/images/logo5.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 223, height: 54, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/ACl4mJwhZ4GHBAoLTAEBAUcBAQFCAQEBTAEBAUcBAQEhABmVo54ajqCgBhMVOAUEBTcFBQY0BgUHMwUFBjAFBAUhnm0KkfiOWmoAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 2 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAI,aAAa;IAA8M,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 3002, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3013, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/public/images/logo6.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 500, height: 113, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/AAIlLzIGV3aAJjlBQTMzMzAsLCwpNjY2My8vLywKCgoJAAEgJSgDY3J6HTY8OzU1NS4vLy8oLy8vKC0tLSYJCQkIlTgLefW61qsAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 2 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8M,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 3029, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3035, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/ui/GradientBackground.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nfunction GradientBackground() {\r\n  return (\r\n    <div className=\" opacity-30\">\r\n      <div className=\" bg-primaryColor opacity-70 blur-[200px] size-[227px] fixed -top-56 -left-56\"></div>\r\n      <div className=\" bg-primaryColor opacity-70 blur-[200px] size-[227px] fixed -bottom-56 left-[50%]\"></div>\r\n      <div className=\" bg-[#00B8D9] opacity-70 blur-[200px] size-[227px] fixed -top-[300px] left-[30%]\"></div>\r\n      <div className=\" bg-warningColor opacity-70 blur-[200px] size-[227px] fixed top-[200px] left-[50%]\"></div>\r\n      <div className=\" bg-errorColor opacity-70 blur-[200px] size-[227px] fixed top-[400px] -left-[100px]\"></div>\r\n      <div className=\" bg-successColor opacity-70 blur-[200px] size-[227px] fixed -bottom-[150px] -left-[150px]\"></div>\r\n      <div className=\" bg-warningColor opacity-70 blur-[200px] size-[227px] fixed -bottom-[200px] left-[20%]\"></div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default GradientBackground;\r\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KAZS;uCAcM"}}, {"offset": {"line": 3107, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3113, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/app/file-upload-standalone/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport FileUploadPage from '@/components/FileUploadPage';\r\nimport { useRouter } from 'next/navigation';\r\nimport { PiSignOut } from 'react-icons/pi';\r\nimport Image from 'next/image';\r\nimport logoLight from \"@/public/images/logo5.png\";\r\nimport logoDark from \"@/public/images/logo6.png\";\r\nimport { useTheme } from 'next-themes';\r\nimport GradientBackground from '@/components/ui/GradientBackground';\r\n\r\nexport default function StandaloneFileUploadPage() {\r\n  const router = useRouter();\r\n  const [currentLogo, setCurrentLogo] = useState(logoLight);\r\n  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  // Update logo based on theme\r\n  useEffect(() => {\r\n    setCurrentLogo(resolvedTheme === 'dark' ? logoDark : logoLight);\r\n  }, [resolvedTheme]);\r\n\r\n  // Handle logout confirmation\r\n  const handleLogout = () => {\r\n    try {\r\n      // Clear session storage\r\n      sessionStorage.removeItem(\"resultUser\");\r\n\r\n      // Clear all user-related localStorage items\r\n      localStorage.removeItem(\"user_email\");\r\n      localStorage.removeItem(\"userEmail\"); // Keep this for backward compatibility\r\n      localStorage.removeItem(\"pinecone_api_key\");\r\n      localStorage.removeItem(\"pinecone_index_name\");\r\n      localStorage.removeItem(\"pineconeApiKeys\");\r\n      localStorage.removeItem(\"userPineconeIndexes\");\r\n      localStorage.removeItem(\"use_dev_environment\");\r\n      localStorage.removeItem(\"redirectAfterLogin\");\r\n      localStorage.removeItem(\"faiss_index_name\");\r\n      localStorage.removeItem(\"faiss_embed_model\");\r\n      localStorage.removeItem(\"faiss_client_email\");\r\n      localStorage.removeItem(\"selectedFaissIndex\");\r\n\r\n      console.log(\"✅ Successfully cleared all user data from storage\");\r\n\r\n      // Close modal and redirect\r\n      setShowLogoutConfirm(false);\r\n      router.push(\"/sign-in\");\r\n    } catch (error) {\r\n      console.error(\"❌ Error during logout:\", error);\r\n\r\n      // Still try to redirect even if clearing storage fails\r\n      setShowLogoutConfirm(false);\r\n      router.push(\"/sign-in\");\r\n    }\r\n  };\r\n\r\n  // Handle logout click\r\n  const handleLogoutClick = () => {\r\n    setShowLogoutConfirm(true);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white dark:bg-n0 text-n500 dark:text-n30 relative\">\r\n      <GradientBackground />\r\n      <div className=\"container mx-auto py-6 px-25  relative z-10\">\r\n        <div className=\"flex items-center justify-between mb-8\">\r\n          <div className=\"flex items-center\">\r\n            <Image src={currentLogo} alt=\"QuerryOne Logo\" className=\"mr-1\"  />\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-10\">\r\n            <button\r\n              onClick={handleLogoutClick}\r\n              className=\"flex items-center text-gray-600 dark:text-gray-300 hover:text-primaryColor transition-colors py-2 px-4 border border-gray-200 dark:border-gray-700 rounded-lg\"\r\n            >\r\n              <PiSignOut className=\"mr-2\" />\r\n              Sign Out\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-n0 rounded-xl shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden\">\r\n          <FileUploadPage />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Logout Confirmation Modal */}\r\n      {showLogoutConfirm && (\r\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\">\r\n          <div className=\"bg-white dark:bg-n0 p-6 rounded-lg shadow-xl max-w-sm w-full mx-4\">\r\n            <h2 className=\"text-lg font-semibold text-n800 dark:text-n10 mb-4\">\r\n              Confirm Logout\r\n            </h2>\r\n            <p className=\"text-sm text-n600 dark:text-n40 mb-6\">\r\n              Are you sure you want to log out? You will need to sign in again to access your account.\r\n            </p>\r\n            <div className=\"flex justify-end gap-3\">\r\n              <button\r\n                onClick={() => setShowLogoutConfirm(false)}\r\n                className=\"px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n800 rounded-md hover:bg-gray-300 dark:hover:bg-n700 transition-colors\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors flex items-center\"\r\n              >\r\n                <PiSignOut className=\"mr-2\" />\r\n                Log Out\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AALA;;;AALA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,4RAAA,CAAA,UAAS;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEjC,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,eAAe,kBAAkB,SAAS,4RAAA,CAAA,UAAQ,GAAG,4RAAA,CAAA,UAAS;QAChE;6CAAG;QAAC;KAAc;IAElB,6BAA6B;IAC7B,MAAM,eAAe;QACnB,IAAI;YACF,wBAAwB;YACxB,eAAe,UAAU,CAAC;YAE1B,4CAA4C;YAC5C,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC,cAAc,uCAAuC;YAC7E,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YAExB,QAAQ,GAAG,CAAC;YAEZ,2BAA2B;YAC3B,qBAAqB;YACrB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,uDAAuD;YACvD,qBAAqB;YACrB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAkB;;;;;0BACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK;oCAAa,KAAI;oCAAiB,WAAU;;;;;;;;;;;0CAG1D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,iJAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;kCAMpC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAc;;;;;;;;;;;;;;;;YAKlB,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,6LAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAGpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,iJAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GAzGwB;;QACP,qIAAA,CAAA,YAAS;QAGE,mJAAA,CAAA,WAAQ;;;KAJZ"}}, {"offset": {"line": 3351, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}