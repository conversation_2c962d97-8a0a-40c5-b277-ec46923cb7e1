(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_460feb._.js", {

"[project]/services/fileUploadService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Format file size to human-readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} - Formatted file size (e.g., "2.5 MB")
 */ __turbopack_esm__({
    "cancelUpload": (()=>cancelUpload),
    "checkIndexExists": (()=>checkIndexExists),
    "checkUploadStatus": (()=>checkUploadStatus),
    "createPineCollectionEntry": (()=>createPineCollectionEntry),
    "fetchEmails": (()=>fetchEmails),
    "formatFileSize": (()=>formatFileSize),
    "getCSVData": (()=>getCSVData),
    "getEmbeddingModels": (()=>getEmbeddingModels),
    "getIndexesByEmail": (()=>getIndexesByEmail),
    "isCancellation": (()=>isCancellation),
    "isFileSizeValid": (()=>isFileSizeValid),
    "isFileTypeAllowed": (()=>isFileTypeAllowed),
    "listCSVFiles": (()=>listCSVFiles),
    "uploadCSVToFaiss": (()=>uploadCSVToFaiss),
    "uploadCSVToPinecone": (()=>uploadCSVToPinecone),
    "uploadFile": (()=>uploadFile),
    "uploadMultipleFiles": (()=>uploadMultipleFiles)
});
const formatFileSize = (bytes)=>{
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB',
        'TB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
const uploadCSVToFaiss = async (file, clientEmail, indexName, updateMode, signal, onProgress, embedModel)=>{
    return new Promise((resolve, reject)=>{
        try {
            // Validate file type
            if (file.type !== 'text/csv') {
                reject(new Error('Only CSV files are supported for FAISS upload'));
                return;
            }
            // Create a new FormData instance
            const formData = new FormData();
            formData.append('file', file);
            // Add client information to form data if provided
            if (clientEmail) {
                formData.append('client', clientEmail);
            }
            // Add index name to form data
            if (indexName) {
                formData.append('index_name', indexName);
            }
            // Add index name to form data
            if (indexName) {
                formData.append('index_name', indexName);
            }
            // Add update mode to form data if provided
            if (updateMode) {
                formData.append('update_mode', updateMode);
            }
            // Add embedding model to form data if provided
            if (embedModel) {
                formData.append('embed_model', embedModel);
            }
            // Create a new XMLHttpRequest and connect abort signal
            const xhr = new XMLHttpRequest();
            // Handle abort signal for client-side cancellation
            if (signal) {
                signal.onabort = ()=>{
                    xhr.abort();
                    // Instead of rejecting with an error, resolve with a cancellation object
                    // This prevents the error from appearing in the console
                    resolve({
                        success: false,
                        cancelled: true,
                        message: 'Upload cancelled by user'
                    });
                };
            }
            // Configure the request to our backend endpoint
            xhr.open('POST', 'http://localhost:5010/api/upload-csv', true);
            // Add authentication header only (no Content-Type for FormData)
            xhr.setRequestHeader('xxxid', 'FAISS');
            // Track upload progress if callback provided
            if (onProgress) {
                xhr.upload.onprogress = (event)=>{
                    if (event.lengthComputable) {
                        const progress = Math.round(event.loaded / event.total * 100);
                        onProgress(progress);
                    }
                };
            }
            // Handle response
            xhr.onload = ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        resolve({
                            success: true,
                            message: 'CSV file uploaded successfully to FAISS',
                            indexName: indexName // Use the user-provided index name
                        });
                    }
                } else {
                    reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));
                }
            };
            // Handle network errors
            xhr.onerror = ()=>{
                reject(new Error('Network error occurred while uploading CSV file'));
            };
            // Send the request
            xhr.send(formData);
        } catch (error) {
            reject(error);
        }
    });
};
const uploadCSVToPinecone = uploadCSVToFaiss;
const uploadFile = async (file, onProgress)=>{
    return new Promise((resolve, reject)=>{
        try {
            // Create a new FormData instance
            const formData = new FormData();
            formData.append('file', file);
            // Create a new XMLHttpRequest
            const xhr = new XMLHttpRequest();
            // Configure the request
            xhr.open('POST', 'http://localhost:5010/api/upload', true);
            // Track upload progress
            xhr.upload.onprogress = (event)=>{
                if (event.lengthComputable && onProgress) {
                    const progress = Math.round(event.loaded / event.total * 100);
                    onProgress(progress);
                }
            };
            // Handle response
            xhr.onload = ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        resolve({
                            success: true,
                            message: 'File uploaded successfully',
                            fileName: file.name
                        });
                    }
                } else {
                    reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));
                }
            };
            // Handle network errors
            xhr.onerror = ()=>{
                reject(new Error('Network error occurred while uploading file'));
            };
            // Send the request
            xhr.send(formData);
        } catch (error) {
            reject(error);
        }
    });
};
const uploadMultipleFiles = async (files, onProgress)=>{
    const uploadPromises = files.map((file)=>{
        return uploadFile(file, (progress)=>{
            if (onProgress) {
                onProgress(file.name, progress);
            }
        });
    });
    return Promise.all(uploadPromises);
};
const isFileTypeAllowed = (fileType, allowedTypes)=>{
    return allowedTypes.includes(fileType);
};
const isFileSizeValid = (fileSize, maxSizeMB)=>{
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return fileSize <= maxSizeBytes;
};
const listCSVFiles = async (clientEmail)=>{
    try {
        const response = await fetch('http://localhost:5010/api/list-csv-files', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                client_email: clientEmail
            })
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error listing CSV files:', error);
        throw error;
    }
};
const getCSVData = async (indexName, limit = 100, offset = 0)=>{
    try {
        const response = await fetch('http://localhost:5010/api/get-csv-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                index_name: indexName,
                limit,
                offset
            })
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error getting CSV data:', error);
        throw error;
    }
};
const getEmbeddingModels = async ()=>{
    try {
        const response = await fetch('http://localhost:5010/api/list-embedding-models', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error getting embedding models:', error);
        // Return fallback data when backend is not available
        return {
            success: true,
            models: {
                "all-MiniLM-L6-v2": {
                    "name": "all-MiniLM-L6-v2",
                    "description": "Sentence Transformers model for semantic similarity",
                    "dimensions": 384
                },
                "all-mpnet-base-v2": {
                    "name": "all-mpnet-base-v2",
                    "description": "High-quality sentence embeddings",
                    "dimensions": 768
                },
                "paraphrase-MiniLM-L6-v2": {
                    "name": "paraphrase-MiniLM-L6-v2",
                    "description": "Paraphrase detection model",
                    "dimensions": 384
                }
            },
            default_model: "all-MiniLM-L6-v2"
        };
    }
};
const isCancellation = (errorOrResponse)=>{
    // Check for our custom cancellation response
    if (errorOrResponse && errorOrResponse.cancelled === true) {
        return true;
    }
    // Check for error message containing cancellation text
    if (errorOrResponse instanceof Error) {
        const errorMessage = errorOrResponse.message.toLowerCase();
        return errorMessage.includes('cancel') || errorMessage.includes('abort') || errorMessage.includes('user interrupt');
    }
    // Check for response with cancellation status
    if (errorOrResponse && errorOrResponse.status === 'cancelled') {
        return true;
    }
    // Check for response with error_type indicating cancellation
    if (errorOrResponse && errorOrResponse.error_type === 'upload_cancelled') {
        return true;
    }
    return false;
};
const fetchEmails = async ()=>{
    try {
        const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eSearch', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'xxxid': 'QUKTYWK'
            }
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        const data = await response.json();
        if (data.statusCode === 200 && Array.isArray(data.source)) {
            // Parse each JSON string in the source array and extract emails
            const emails = data.source.map((jsonStr)=>{
                try {
                    const userObj = JSON.parse(jsonStr);
                    return userObj.email || '';
                } catch (error) {
                    console.error('Error parsing JSON:', error);
                    return '';
                }
            }).filter(Boolean); // Remove empty strings
            return emails;
        }
        return [];
    } catch (error) {
        console.error('Error fetching emails:', error);
        return [];
    }
};
const createPineCollectionEntry = async (embedModel, indexName, clientEmail)=>{
    try {
        console.log(`Creating PINE collection entry: embedModel=${embedModel}, indexName=${indexName}, clientEmail=${clientEmail}`);
        const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eCreateCol?colname=PINE', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'xxxid': 'PINE'
            },
            body: JSON.stringify({
                api_key: embedModel,
                index_name: indexName,
                client: clientEmail // Store email as client
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }
        const result = await response.json();
        console.log('PINE collection entry created successfully:', result);
        return result;
    } catch (error) {
        console.error('Error creating PINE collection entry:', error);
        throw error;
    }
};
const getIndexesByEmail = async (clientEmail)=>{
    try {
        console.log(`Fetching indexes for email: ${clientEmail}`);
        const response = await fetch(`https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&filtercount=1&f1_field=client_S&f1_op=eq&f1_value=${encodeURIComponent(clientEmail)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'xxxid': 'PINE'
            }
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        const data = await response.json();
        console.log('PINE collection response for email:', data);
        if (data.statusCode === 200 && Array.isArray(data.source)) {
            // Parse each JSON string in the source array
            const indexes = data.source.map((jsonStr, index)=>{
                try {
                    const indexObj = JSON.parse(jsonStr);
                    return {
                        _id: indexObj._id?.$oid || indexObj._id || `pine-item-${index}`,
                        email: indexObj.client || clientEmail,
                        index_name: indexObj.index_name || 'N/A',
                        embed_model: indexObj.api_key || 'N/A',
                        source: 'PINE',
                        originalData: indexObj
                    };
                } catch (error) {
                    console.error('Error parsing PINE index JSON:', error);
                    return null;
                }
            }).filter((item)=>item !== null);
            return indexes;
        }
        return [];
    } catch (error) {
        console.error('Error fetching indexes by email:', error);
        return [];
    }
};
const checkIndexExists = async (indexName, client, embedModel)=>{
    try {
        const response = await fetch('http://localhost:5010/api/check-index', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                index_name: indexName,
                client: client,
                embed_model: embedModel
            })
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        const data = await response.json();
        if (data.success) {
            return {
                exists: data.exists,
                embedding_model: data.embedding_model
            };
        }
        return {
            exists: false
        };
    } catch (error) {
        console.error('Error checking if index exists:', error);
        return {
            exists: false
        };
    }
};
const cancelUpload = async (uploadId, abortController)=>{
    try {
        // First, abort the HTTP request if an AbortController is provided
        if (abortController) {
            try {
                abortController.abort();
                console.log('HTTP request aborted');
            } catch (abortError) {
                // Don't log this as an error since it's expected behavior
                console.log('Note: AbortController already used or not applicable');
            // Continue with server-side cancellation even if client-side abort fails
            }
        }
        // Then, send a cancellation request to the server
        console.log(`Sending cancellation request for upload ${uploadId}`);
        const response = await fetch('http://localhost:5010/api/cancel-upload', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                upload_id: uploadId
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }
        const data = await response.json();
        console.log('Cancellation response:', data);
        // Verify cancellation by checking status
        try {
            const statusResponse = await checkUploadStatus(uploadId);
            console.log('Status after cancellation:', statusResponse);
        } catch (statusError) {
            console.error('Error checking status after cancellation:', statusError);
        // Continue even if status check fails
        }
        return data;
    } catch (error) {
        console.error('Error cancelling upload:', error);
        throw error;
    }
};
const checkUploadStatus = async (uploadId, silent = false)=>{
    try {
        const response = await fetch('http://localhost:5010/api/upload-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                upload_id: uploadId
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }
        const data = await response.json();
        // Log cancellation status if detected
        if (data.success && data.cancelled) {
            console.log(`Upload ${uploadId} is marked as cancelled. Status: ${data.status}`);
        }
        return data;
    } catch (error) {
        if (!silent) {
            console.error('Error checking upload status:', error);
        }
        throw error;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/faissService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * FAISS Service - Handles all FAISS-related API calls
 * Replaces Pinecone-specific functionality with FAISS backend integration
 */ __turbopack_esm__({
    "cancelUpload": (()=>cancelUpload),
    "checkFaissIndexExists": (()=>checkFaissIndexExists),
    "getCSVData": (()=>getCSVData),
    "getEmbeddingModels": (()=>getEmbeddingModels),
    "getFaissCategories": (()=>getFaissCategories),
    "getFaissConfig": (()=>getFaissConfig),
    "getUploadStatus": (()=>getUploadStatus),
    "listCSVFiles": (()=>listCSVFiles),
    "listExcelFiles": (()=>listExcelFiles),
    "queryFaiss": (()=>queryFaiss),
    "setFaissConfig": (()=>setFaissConfig),
    "uploadCSVToFaiss": (()=>uploadCSVToFaiss),
    "uploadExcelToFaiss": (()=>uploadExcelToFaiss),
    "uploadFileToFaiss": (()=>uploadFileToFaiss)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
// Base URL for FAISS backend
const FAISS_BASE_URL = ("TURBOPACK compile-time truthy", 1) ? 'http://localhost:5010' : ("TURBOPACK unreachable", undefined);
const uploadCSVToFaiss = async (file, indexName, clientEmail, updateMode = 'update', embedModel = 'all-MiniLM-L6-v2', signal, onProgress)=>{
    return new Promise((resolve, reject)=>{
        try {
            // Validate file type
            if (file.type !== 'text/csv' && !file.name.toLowerCase().endsWith('.csv')) {
                reject(new Error('Only CSV files are supported for CSV upload'));
                return;
            }
            // Create FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('index_name', indexName);
            formData.append('update_mode', updateMode);
            formData.append('embed_model', embedModel);
            if (clientEmail) {
                formData.append('client', clientEmail);
            }
            // Create XMLHttpRequest for progress tracking
            const xhr = new XMLHttpRequest();
            // Handle response
            xhr.onload = ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        reject(new Error('Invalid JSON response from server'));
                    }
                } else {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        reject(new Error(errorResponse.error || `HTTP ${xhr.status}: ${xhr.statusText}`));
                    } catch (e) {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                }
            };
            xhr.onerror = ()=>{
                reject(new Error('Network error occurred during upload'));
            };
            xhr.onabort = ()=>{
                reject(new Error('Upload was cancelled'));
            };
            // Track upload progress
            if (onProgress) {
                xhr.upload.onprogress = (event)=>{
                    if (event.lengthComputable) {
                        const progress = Math.round(event.loaded / event.total * 100);
                        onProgress(progress);
                    }
                };
            }
            // Handle cancellation
            if (signal) {
                signal.addEventListener('abort', ()=>{
                    xhr.abort();
                });
            }
            // Send request
            xhr.open('POST', `${FAISS_BASE_URL}/api/upload-csv`, true);
            xhr.send(formData);
        } catch (error) {
            reject(error);
        }
    });
};
const uploadExcelToFaiss = async (file, indexName, clientId, updateMode = 'update', embedModel = 'all-MiniLM-L6-v2', signal, onProgress)=>{
    return new Promise((resolve, reject)=>{
        try {
            // Validate file type
            const fileName = file.name.toLowerCase();
            if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
                reject(new Error('Only Excel files (.xlsx, .xls) are supported for Excel upload'));
                return;
            }
            // Create FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('index_name', indexName);
            formData.append('client_id', clientId);
            formData.append('update_mode', updateMode);
            formData.append('embed_model', embedModel);
            // Create XMLHttpRequest for progress tracking
            const xhr = new XMLHttpRequest();
            // Handle response
            xhr.onload = ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        reject(new Error('Invalid JSON response from server'));
                    }
                } else {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        reject(new Error(errorResponse.error?.message || errorResponse.message || `HTTP ${xhr.status}: ${xhr.statusText}`));
                    } catch (e) {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                }
            };
            xhr.onerror = ()=>{
                reject(new Error('Network error occurred during upload'));
            };
            xhr.onabort = ()=>{
                reject(new Error('Upload was cancelled'));
            };
            // Track upload progress
            if (onProgress) {
                xhr.upload.onprogress = (event)=>{
                    if (event.lengthComputable) {
                        const progress = Math.round(event.loaded / event.total * 100);
                        onProgress(progress);
                    }
                };
            }
            // Handle cancellation
            if (signal) {
                signal.addEventListener('abort', ()=>{
                    xhr.abort();
                });
            }
            // Send request
            xhr.open('POST', `${FAISS_BASE_URL}/api/upload-excel`, true);
            xhr.send(formData);
        } catch (error) {
            reject(error);
        }
    });
};
const uploadFileToFaiss = async (file, indexName, clientEmail, updateMode = 'update', embedModel = 'all-MiniLM-L6-v2', signal, onProgress)=>{
    const fileName = file.name.toLowerCase();
    const isExcel = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');
    if (isExcel) {
        // For Excel files, client_id is required
        if (!clientEmail) {
            throw new Error('Client email is required for Excel file uploads');
        }
        return uploadExcelToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);
    } else {
        // For CSV files, use the existing CSV upload function
        return uploadCSVToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);
    }
};
const getEmbeddingModels = async ()=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/list-embedding-models`);
    if (!response.ok) {
        throw new Error(`Failed to fetch embedding models: ${response.statusText}`);
    }
    return response.json();
};
const getFaissCategories = async (clientEmail)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/list-categories`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientEmail ? {
            client_email: clientEmail
        } : {})
    });
    if (!response.ok) {
        throw new Error(`Failed to fetch FAISS categories: ${response.statusText}`);
    }
    const data = await response.json();
    return data.categories || [];
};
const checkFaissIndexExists = async (indexName, embedModel)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/check-index`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            index_name: indexName,
            embed_model: embedModel
        })
    });
    if (!response.ok) {
        throw new Error(`Failed to check FAISS index: ${response.statusText}`);
    }
    const data = await response.json();
    return data.exists || false;
};
/**
 * Get current user's email from session storage
 */ const getCurrentUserEmail = ()=>{
    try {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Try multiple sources for user email
        const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');
        if (directEmail) return directEmail;
        // Try from user session data
        const userSession = sessionStorage.getItem('resultUser');
        if (userSession) {
            const userData = JSON.parse(userSession);
            return userData.email || userData.username || null;
        }
        return null;
    } catch (error) {
        console.error('Error getting current user email:', error);
        return null;
    }
};
const queryFaiss = async (query, indexName, k = 5, userEmail)=>{
    // Get user email if not provided
    const emailToUse = userEmail || getCurrentUserEmail();
    const requestBody = {
        query,
        index_name: indexName,
        k
    };
    // Add user email for access validation if available
    if (emailToUse) {
        requestBody.user_email = emailToUse;
    }
    const response = await fetch(`${FAISS_BASE_URL}/api/query-faiss`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    });
    if (!response.ok) {
        throw new Error(`Failed to query FAISS index: ${response.statusText}`);
    }
    const data = await response.json();
    return data.results || [];
};
const getUploadStatus = async (uploadId)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/upload-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            upload_id: uploadId
        })
    });
    if (!response.ok) {
        throw new Error(`Failed to get upload status: ${response.statusText}`);
    }
    return response.json();
};
const cancelUpload = async (uploadId)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/cancel-upload`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            upload_id: uploadId
        })
    });
    if (!response.ok) {
        throw new Error(`Failed to cancel upload: ${response.statusText}`);
    }
};
const getCSVData = async (indexName, limit = 100, offset = 0)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/get-csv-data`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            index_name: indexName,
            limit,
            offset
        })
    });
    if (!response.ok) {
        throw new Error(`Failed to get CSV data: ${response.statusText}`);
    }
    return response.json();
};
const listCSVFiles = async (clientEmail)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/list-csv-files`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientEmail ? {
            client_email: clientEmail
        } : {})
    });
    if (!response.ok) {
        throw new Error(`Failed to list CSV files: ${response.statusText}`);
    }
    const data = await response.json();
    return data.files || [];
};
const listExcelFiles = async (clientId)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/list-excel-files`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientId ? {
            client_id: clientId
        } : {})
    });
    if (!response.ok) {
        throw new Error(`Failed to list Excel files: ${response.statusText}`);
    }
    const data = await response.json();
    return data.excel_files || [];
};
const getFaissConfig = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return {
        indexName: localStorage.getItem('faiss_index_name'),
        embedModel: localStorage.getItem('faiss_embed_model') || 'all-MiniLM-L6-v2',
        clientEmail: localStorage.getItem('faiss_client_email')
    };
};
const setFaissConfig = (config)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    if (config.indexName) {
        localStorage.setItem('faiss_index_name', config.indexName);
    }
    if (config.embedModel) {
        localStorage.setItem('faiss_embed_model', config.embedModel);
    }
    if (config.clientEmail) {
        localStorage.setItem('faiss_client_email', config.clientEmail);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/fileDownloadService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Service for handling file downloads
 */ /**
 * Download a sample CSV file
 * @param {string} fileName - Name of the sample file to download
 * @returns {Promise<boolean>} - Promise that resolves to true if download was successful
 */ __turbopack_esm__({
    "downloadSampleFile": (()=>downloadSampleFile)
});
const downloadSampleFile = async (fileName = 'querry.csv')=>{
    try {
        // First check if the file exists by making a HEAD request
        try {
            // Use the public directory path
            const checkResponse = await fetch(`/${fileName}`, {
                method: 'HEAD'
            });
            if (!checkResponse.ok) {
                console.error(`Sample file ${fileName} not found. Status: ${checkResponse.status}`);
                return false;
            }
        } catch (checkError) {
            console.error('Error checking if sample file exists:', checkError);
            return false;
        }
        // Create a URL to the file in the public directory
        const fileUrl = `/${fileName}`;
        // Create a link element
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        // Append to the document
        document.body.appendChild(link);
        // Trigger the download
        link.click();
        // Clean up
        document.body.removeChild(link);
        return true;
    } catch (error) {
        console.error('Error downloading sample file:', error);
        return false;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FileUploadWithFaiss.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/services/fileUploadService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$faissService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/services/faissService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileDownloadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/services/fileDownloadService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
;
;
;
const FileUploadWithFaiss = ({ onFileUpload, maxFileSize = 50, allowedTypes = [
    'text/csv',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel'
], selectedLanguage = 'English', showFaissUpload = true, indexName = 'default', clientEmail = '<EMAIL>', updateMode = 'update', embedModel = 'all-MiniLM-L6-v2', onFaissUploadSuccess, onFaissUploadError })=>{
    _s();
    const [files, setFiles] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isDragging, setIsDragging] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [downloadError, setDownloadError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const dropAreaRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Get language-specific text
    const getLanguageText = ()=>{
        const texts = {
            English: {
                dragDrop: 'Drag & drop a CSV or Excel file or click to browse',
                csvOnly: 'CSV & Excel format only, up to',
                downloadSample: 'Download Sample File',
                selectedFile: 'Selected File',
                upload: 'Upload',
                uploadToFaiss: 'Upload to FAISS',
                uploading: 'Uploading...',
                uploadingToFaiss: 'Uploading to FAISS...',
                success: 'Success',
                error: 'Error',
                remove: 'Remove',
                indexInfo: 'Your CSV or Excel file name is taken as index in DB and your data will be stored in that index'
            },
            Tamil: {
                dragDrop: 'CSV அல்லது Excel கோப்பை இழுத்து விடவும் அல்லது உலாவ கிளிக் செய்யவும்',
                csvOnly: 'CSV & Excel வடிவம் மட்டும், வரை',
                downloadSample: 'மாதிரி கோப்பை பதிவிறக்கவும்',
                selectedFile: 'தேர்ந்தெடுக்கப்பட்ட கோப்பு',
                upload: 'பதிவேற்று',
                uploadToFaiss: 'FAISS இல் பதிவேற்று',
                uploading: 'பதிவேற்றுகிறது...',
                uploadingToFaiss: 'FAISS இல் பதிவேற்றுகிறது...',
                success: 'வெற்றி',
                error: 'பிழை',
                remove: 'அகற்று',
                indexInfo: 'உங்கள் CSV அல்லது Excel கோப்பு பெயர் DB இல் குறியீடாக எடுக்கப்பட்டு உங்கள் தரவு அந்த குறியீட்டில் சேமிக்கப்படும்'
            },
            Telugu: {
                dragDrop: 'CSV లేదా Excel ఫైల్‌ను లాగి వదలండి లేదా బ్రౌజ్ చేయడానికి క్లిక్ చేయండి',
                csvOnly: 'CSV & Excel ఫార్మాట్ మాత్రమే, వరకు',
                downloadSample: 'నమూనా ఫైల్ డౌన్‌లోడ్ చేయండి',
                selectedFile: 'ఎంచుకున్న ఫైల్',
                upload: 'అప్‌లోడ్',
                uploadToFaiss: 'FAISS కు అప్‌లోడ్',
                uploading: 'అప్‌లోడ్ చేస్తోంది...',
                uploadingToFaiss: 'FAISS కు అప్‌లోడ్ చేస్తోంది...',
                success: 'విజయం',
                error: 'లోపం',
                remove: 'తొలగించు',
                indexInfo: 'మీ CSV లేదా Excel ఫైల్ పేరు DB లో ఇండెక్స్‌గా తీసుకోబడుతుంది మరియు మీ డేటా ఆ ఇండెక్స్‌లో నిల్వ చేయబడుతుంది'
            },
            Kannada: {
                dragDrop: 'CSV ಅಥವಾ Excel ಫೈಲ್ ಅನ್ನು ಎಳೆದು ಬಿಡಿ ಅಥವಾ ಬ್ರೌಸ್ ಮಾಡಲು ಕ್ಲಿಕ್ ಮಾಡಿ',
                csvOnly: 'CSV & Excel ಸ್ವರೂಪ ಮಾತ್ರ, ವರೆಗೆ',
                downloadSample: 'ಮಾದರಿ ಫೈಲ್ ಡೌನ್‌ಲೋಡ್ ಮಾಡಿ',
                selectedFile: 'ಆಯ್ಕೆಮಾಡಿದ ಫೈಲ್',
                upload: 'ಅಪ್‌ಲೋಡ್',
                uploadToFaiss: 'FAISS ಗೆ ಅಪ್‌ಲೋಡ್',
                uploading: 'ಅಪ್‌ಲೋಡ್ ಮಾಡುತ್ತಿದೆ...',
                uploadingToFaiss: 'FAISS ಗೆ ಅಪ್‌ಲೋಡ್ ಮಾಡುತ್ತಿದೆ...',
                success: 'ಯಶಸ್ಸು',
                error: 'ದೋಷ',
                remove: 'ತೆಗೆದುಹಾಕಿ',
                indexInfo: 'ನಿಮ್ಮ CSV ಅಥವಾ Excel ಫೈಲ್ ಹೆಸರನ್ನು DB ಯಲ್ಲಿ ಸೂಚ್ಯಂಕವಾಗಿ ತೆಗೆದುಕೊಳ್ಳಲಾಗುತ್ತದೆ ಮತ್ತು ನಿಮ್ಮ ಡೇಟಾವನ್ನು ಆ ಸೂಚ್ಯಂಕದಲ್ಲಿ ಸಂಗ್ರಹಿಸಲಾಗುತ್ತದೆ'
            }
        };
        return texts[selectedLanguage] || texts.English;
    };
    // Handle file selection
    const handleFileSelect = (fileList)=>{
        if (!fileList || fileList.length === 0) return;
        const newFile = fileList[0];
        // Validate file type
        const fileName = newFile.name.toLowerCase();
        const isCSV = newFile.type === 'text/csv' || fileName.endsWith('.csv');
        const isExcel = newFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || newFile.type === 'application/vnd.ms-excel' || fileName.endsWith('.xlsx') || fileName.endsWith('.xls');
        if (!isCSV && !isExcel) {
            alert(`File type not allowed. Please upload a CSV or Excel file.`);
            return;
        }
        // Validate file size
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFileSizeValid"])(newFile.size, maxFileSize)) {
            alert(`File size exceeds the ${maxFileSize}MB limit.`);
            return;
        }
        // Create file with status
        const fileWithStatus = {
            file: newFile,
            status: 'selected',
            uploadProgress: 0,
            faissProgress: 0
        };
        setFiles([
            fileWithStatus
        ]);
        // Call onFileUpload callback if provided
        if (onFileUpload) {
            onFileUpload([
                newFile
            ]);
        }
    };
    // Handle file input change
    const handleInputChange = (e)=>{
        handleFileSelect(e.target.files);
        if (e.target) e.target.value = '';
    };
    // Handle click on the drop area
    const handleClick = ()=>{
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };
    // Handle drag events
    const handleDragEnter = (e)=>{
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };
    const handleDragLeave = (e)=>{
        e.preventDefault();
        e.stopPropagation();
        if (e.currentTarget === e.target) {
            setIsDragging(false);
        }
    };
    const handleDragOver = (e)=>{
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    };
    const handleDrop = (e)=>{
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        const droppedFiles = e.dataTransfer.files;
        handleFileSelect(droppedFiles);
    };
    // Upload file to FAISS
    const uploadToFaiss = async (fileWithStatus)=>{
        const { file } = fileWithStatus;
        // Update status to uploading
        setFiles((prevFiles)=>prevFiles.map((f)=>f.file.name === file.name ? {
                    ...f,
                    status: 'faiss-uploading',
                    faissProgress: 0
                } : f));
        try {
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$faissService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["uploadFileToFaiss"])(file, indexName, clientEmail, updateMode || 'update', embedModel, undefined, (progress)=>{
                // Update progress
                setFiles((prevFiles)=>prevFiles.map((f)=>f.file.name === file.name ? {
                            ...f,
                            faissProgress: progress
                        } : f));
            });
            // Update status to success
            setFiles((prevFiles)=>prevFiles.map((f)=>f.file.name === file.name ? {
                        ...f,
                        status: 'faiss-success',
                        faissResponse: response,
                        faissProgress: 100
                    } : f));
            if (onFaissUploadSuccess) {
                onFaissUploadSuccess(response);
            }
        } catch (error) {
            // Update status to error
            setFiles((prevFiles)=>prevFiles.map((f)=>f.file.name === file.name ? {
                        ...f,
                        status: 'faiss-error',
                        error: error instanceof Error ? error.message : 'Upload failed'
                    } : f));
            if (onFaissUploadError) {
                onFaissUploadError(error instanceof Error ? error.message : 'Upload failed');
            }
        }
    };
    // Handle file removal
    const removeFile = (fileName)=>{
        setFiles((prevFiles)=>prevFiles.filter((file)=>file.file.name !== fileName));
    };
    // Handle sample file download
    const handleSampleFileDownload = async (e)=>{
        e.stopPropagation();
        setDownloadError(null);
        try {
            const success = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileDownloadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["downloadSampleFile"])('querry.csv');
            if (!success) {
                setDownloadError('Failed to download sample file. Please try again.');
            }
        } catch (error) {
            console.error('Error downloading sample file:', error);
            setDownloadError('An error occurred while downloading the sample file.');
        }
    };
    const text = getLanguageText();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "file-upload-container",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: dropAreaRef,
                className: `drop-area ${isDragging ? 'dragging' : ''} bg-primaryColor/5 border border-dashed border-primaryColor/30 rounded-xl p-8 flex justify-center items-center flex-col transition-all duration-200 hover:bg-primaryColor/10 cursor-pointer`,
                onClick: handleClick,
                onDragEnter: handleDragEnter,
                onDragLeave: handleDragLeave,
                onDragOver: handleDragOver,
                onDrop: handleDrop,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "drop-icon text-primaryColor",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCloudArrowUp"], {
                            size: 50
                        }, void 0, false, {
                            fileName: "[project]/components/FileUploadWithFaiss.tsx",
                            lineNumber: 302,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                        lineNumber: 301,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "drop-text text-center text-lg font-medium pt-5 text-gray-700 dark:text-gray-300",
                        children: text.dragDrop
                    }, void 0, false, {
                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                        lineNumber: 304,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "file-restriction-notice mt-2 bg-primaryColor/10 px-3 py-1 rounded-full text-xs",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "restriction-icon",
                                children: "ⓘ"
                            }, void 0, false, {
                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                lineNumber: 308,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "ml-1",
                                children: text.indexInfo
                            }, void 0, false, {
                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                lineNumber: 309,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                        lineNumber: 307,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "file-types text-gray-500 dark:text-gray-400 text-sm pt-2",
                        children: [
                            "CSV & Excel files only • Max ",
                            maxFileSize,
                            "MB"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                        lineNumber: 311,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: handleSampleFileDownload,
                        className: "download-sample-btn mt-3 bg-white border border-primaryColor text-primaryColor hover:bg-primaryColor/5 px-4 py-2 rounded-md text-sm flex items-center transition-colors",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiDownload"], {
                                className: "h-4 w-4 mr-2"
                            }, void 0, false, {
                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                lineNumber: 321,
                                columnNumber: 11
                            }, this),
                            text.downloadSample
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                        lineNumber: 316,
                        columnNumber: 9
                    }, this),
                    downloadError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "download-error mt-2 text-red-500 text-xs",
                        children: downloadError
                    }, void 0, false, {
                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                        lineNumber: 327,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        type: "file",
                        ref: fileInputRef,
                        onChange: handleInputChange,
                        className: "file-input hidden",
                        accept: ".csv,.xlsx,.xls,text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                    }, void 0, false, {
                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                        lineNumber: 332,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                lineNumber: 292,
                columnNumber: 7
            }, this),
            files.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "file-list mt-4 bg-gray-50 dark:bg-gray-900 p-4 rounded-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium mb-2 dark:text-gray-200",
                        children: text.selectedFile
                    }, void 0, false, {
                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                        lineNumber: 343,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                        className: "space-y-2",
                        children: files.map((fileWithStatus, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: "file-item bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "file-info flex justify-between items-center mb-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiFile"], {
                                                        className: "text-primaryColor",
                                                        size: 20
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                        lineNumber: 349,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "file-name font-medium dark:text-gray-200",
                                                        children: fileWithStatus.file.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                        lineNumber: 350,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                lineNumber: 348,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "file-size text-sm text-gray-500 dark:text-gray-400",
                                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatFileSize"])(fileWithStatus.file.size)
                                            }, void 0, false, {
                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                lineNumber: 352,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                        lineNumber: 347,
                                        columnNumber: 17
                                    }, this),
                                    showFaissUpload && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "faiss-upload-section",
                                        children: [
                                            fileWithStatus.status === 'selected' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "upload-to-faiss-btn bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 flex items-center gap-2 transition-colors",
                                                onClick: (e)=>{
                                                    e.stopPropagation();
                                                    uploadToFaiss(fileWithStatus);
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiDatabase"], {
                                                        size: 16
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                        lineNumber: 368,
                                                        columnNumber: 25
                                                    }, this),
                                                    text.uploadToFaiss
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                lineNumber: 361,
                                                columnNumber: 23
                                            }, this),
                                            fileWithStatus.status === 'faiss-uploading' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "faiss-upload-progress",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center gap-2 mb-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSpinner"], {
                                                                className: "animate-spin text-blue-600",
                                                                size: 16
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                                lineNumber: 376,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm text-blue-600",
                                                                children: text.uploadingToFaiss
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                                lineNumber: 377,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                        lineNumber: 375,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "progress-container w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "progress-bar bg-blue-600 h-2.5 rounded-full transition-all duration-300",
                                                            style: {
                                                                width: `${fileWithStatus.faissProgress || 0}%`
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                            lineNumber: 380,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                        lineNumber: 379,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "progress-text text-xs text-gray-500 dark:text-gray-400 mt-1",
                                                        children: [
                                                            fileWithStatus.faissProgress || 0,
                                                            "%"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                        lineNumber: 385,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                lineNumber: 374,
                                                columnNumber: 23
                                            }, this),
                                            fileWithStatus.status === 'faiss-success' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "faiss-success flex items-center gap-2 text-green-600",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCheck"], {
                                                        size: 16
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                        lineNumber: 393,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm",
                                                        children: [
                                                            text.success,
                                                            " - Uploaded to FAISS"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                        lineNumber: 394,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                lineNumber: 392,
                                                columnNumber: 23
                                            }, this),
                                            fileWithStatus.status === 'faiss-error' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "faiss-error",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center gap-2 text-red-600 mb-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {
                                                                size: 16
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                                lineNumber: 401,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm",
                                                                children: text.error
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                                lineNumber: 402,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                        lineNumber: 400,
                                                        columnNumber: 25
                                                    }, this),
                                                    fileWithStatus.error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "error-message text-red-500 text-xs",
                                                        children: fileWithStatus.error
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                        lineNumber: 405,
                                                        columnNumber: 27
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                lineNumber: 399,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                        lineNumber: 359,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "file-actions flex justify-end mt-3",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            className: "remove-btn text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 p-1",
                                            onClick: (e)=>{
                                                e.stopPropagation();
                                                removeFile(fileWithStatus.file.name);
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {
                                                size: 16
                                            }, void 0, false, {
                                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                                lineNumber: 423,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                            lineNumber: 416,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                        lineNumber: 415,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, `${fileWithStatus.file.name}-${index}`, true, {
                                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                                lineNumber: 346,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/components/FileUploadWithFaiss.tsx",
                        lineNumber: 344,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/FileUploadWithFaiss.tsx",
                lineNumber: 342,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/FileUploadWithFaiss.tsx",
        lineNumber: 291,
        columnNumber: 5
    }, this);
};
_s(FileUploadWithFaiss, "Zftmz3LJVRFb20QeXJbiL+pvbZ4=");
_c = FileUploadWithFaiss;
const __TURBOPACK__default__export__ = FileUploadWithFaiss;
var _c;
__turbopack_refresh__.register(_c, "FileUploadWithFaiss");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/adminsidebar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
const AdminSidebar = ({ currentView = 'create', onViewChange, isOpen = true, onToggle })=>{
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const handleNavigation = (view)=>{
        if (view === 'show') {
            // Navigate to the show-index route
            router.push('/show-index');
        } else if (view === 'create') {
            // Navigate to the file-upload-standalone route
            router.push('/file-upload-standalone');
        } else if (onViewChange) {
            onViewChange(view);
        }
    };
    const menuItems = [
        {
            id: 'create',
            label: 'Create Index',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiPlus"],
            description: 'Upload CSV or Excel files to create new Fiass indexes'
        },
        {
            id: 'show',
            label: 'Show Index',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiDatabase"],
            description: 'View and manage existing PINE collection entries'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `admin-sidebar w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 fixed left-0 top-0 p-4 h-full z-40 transition-transform duration-300 ${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "sidebar-header mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2 mb-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiGear"], {
                                className: "text-primaryColor text-xl"
                            }, void 0, false, {
                                fileName: "[project]/components/adminsidebar.tsx",
                                lineNumber: 55,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-xl font-semibold dark:text-white",
                                children: "Admin Panel"
                            }, void 0, false, {
                                fileName: "[project]/components/adminsidebar.tsx",
                                lineNumber: 56,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/adminsidebar.tsx",
                        lineNumber: 54,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400",
                        children: "Manage your FiassDB and data"
                    }, void 0, false, {
                        fileName: "[project]/components/adminsidebar.tsx",
                        lineNumber: 60,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/adminsidebar.tsx",
                lineNumber: 53,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                className: "space-y-2",
                children: menuItems.map((item)=>{
                    const Icon = item.icon;
                    const isActive = currentView === item.id;
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>handleNavigation(item.id),
                        className: `block w-full text-left px-4 py-3 rounded-md transition-colors group ${isActive ? 'bg-primaryColor text-white shadow-sm' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                    className: `text-lg ${isActive ? 'text-white' : 'text-primaryColor'}`
                                }, void 0, false, {
                                    fileName: "[project]/components/adminsidebar.tsx",
                                    lineNumber: 82,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-medium",
                                            children: item.label
                                        }, void 0, false, {
                                            fileName: "[project]/components/adminsidebar.tsx",
                                            lineNumber: 88,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `text-xs mt-1 ${isActive ? 'text-white/80' : 'text-gray-500 dark:text-gray-400'}`,
                                            children: item.description
                                        }, void 0, false, {
                                            fileName: "[project]/components/adminsidebar.tsx",
                                            lineNumber: 89,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/adminsidebar.tsx",
                                    lineNumber: 87,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/adminsidebar.tsx",
                            lineNumber: 81,
                            columnNumber: 15
                        }, this)
                    }, item.id, false, {
                        fileName: "[project]/components/adminsidebar.tsx",
                        lineNumber: 72,
                        columnNumber: 13
                    }, this);
                })
            }, void 0, false, {
                fileName: "[project]/components/adminsidebar.tsx",
                lineNumber: 66,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/adminsidebar.tsx",
        lineNumber: 49,
        columnNumber: 5
    }, this);
};
_s(AdminSidebar, "fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = AdminSidebar;
const __TURBOPACK__default__export__ = AdminSidebar;
var _c;
__turbopack_refresh__.register(_c, "AdminSidebar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/FileUploadPage.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FileUploadWithFaiss$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/FileUploadWithFaiss.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$adminsidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/adminsidebar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/services/fileUploadService.ts [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
;
;
const FileUploadPage = ()=>{
    _s();
    const [uploadedFiles, setUploadedFiles] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [uploadStatus, setUploadStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [processingStages, setProcessingStages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [selectedClient, setSelectedClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [selectedFile, setSelectedFile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [emails, setEmails] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoadingEmails, setIsLoadingEmails] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [emailError, setEmailError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [indexExists, setIndexExists] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isCheckingIndex, setIsCheckingIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showUpdateModal, setShowUpdateModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [updateMode, setUpdateMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [indexName, setIndexName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [embedModel, setEmbedModel] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('all-MiniLM-L6-v2');
    const [availableModels, setAvailableModels] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [abortController, setAbortController] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [currentView, setCurrentView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('create');
    const [sidebarOpen, setSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [apiKeyError, setApiKeyError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // Fetch emails and embedding models when component mounts
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FileUploadPage.useEffect": ()=>{
            const getEmails = {
                "FileUploadPage.useEffect.getEmails": async ()=>{
                    setIsLoadingEmails(true);
                    setEmailError('');
                    try {
                        const emailList = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchEmails"])();
                        setEmails(emailList);
                    } catch (error) {
                        console.error('Error fetching emails:', error);
                        setEmailError('Failed to load emails. Please try again later.');
                    } finally{
                        setIsLoadingEmails(false);
                    }
                }
            }["FileUploadPage.useEffect.getEmails"];
            const getModels = {
                "FileUploadPage.useEffect.getModels": async ()=>{
                    try {
                        const modelsData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getEmbeddingModels"])();
                        if (modelsData.success) {
                            setAvailableModels(modelsData.models);
                        } else {
                            // Set fallback models if API response doesn't indicate success
                            setAvailableModels({
                                "all-MiniLM-L6-v2": {
                                    "name": "all-MiniLM-L6-v2",
                                    "description": "Sentence Transformers model for semantic similarity",
                                    "dimension": 384
                                }
                            });
                        }
                    } catch (error) {
                        console.error('Error fetching embedding models:', error);
                        // Set fallback models when API call fails
                        setAvailableModels({
                            "all-MiniLM-L6-v2": {
                                "name": "all-MiniLM-L6-v2",
                                "description": "Sentence Transformers model for semantic similarity",
                                "dimension": 384
                            }
                        });
                    }
                }
            }["FileUploadPage.useEffect.getModels"];
            getEmails();
            getModels();
        }
    }["FileUploadPage.useEffect"], []);
    // Check if index exists when index name is provided
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FileUploadPage.useEffect": ()=>{
            if (indexName.trim()) {
                checkIfIndexExists();
            }
        }
    }["FileUploadPage.useEffect"], [
        indexName
    ]);
    // Define processing stages
    const stages = [
        {
            id: 'uploading',
            label: 'Uploading File',
            description: 'Transferring file to server'
        },
        {
            id: 'processing',
            label: 'Processing Data',
            description: 'Parsing CSV and preparing vectors'
        },
        {
            id: 'indexing',
            label: 'Creating Index',
            description: 'Building FAISS vector database'
        },
        {
            id: 'complete',
            label: 'Complete',
            description: 'Data successfully indexed and ready for queries'
        }
    ];
    // Check if index exists
    const checkIfIndexExists = async ()=>{
        if (!indexName.trim()) {
            return;
        }
        // Store the index name in localStorage for immediate use
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.setItem('faiss_index_name', indexName);
            console.log(`Set index_name in localStorage: ${indexName}`);
        }
        setIsCheckingIndex(true);
        try {
            console.log(`Checking if FAISS index exists: ${indexName}`);
            const { exists } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["checkIndexExists"])(indexName, selectedClient, embedModel);
            setIndexExists(exists);
            if (exists) {
                console.log(`Index ${indexName} exists, showing update modal`);
                console.log(`Index ${indexName} exists, showing update modal`);
                setShowUpdateModal(true);
            } else {
                console.log(`Index ${indexName} does not exist, will create new index`);
                console.log(`Index ${indexName} does not exist, will create new index`);
                // If index doesn't exist, set update mode to null
                setUpdateMode(null);
            }
        } catch (error) {
            console.error('Error checking if index exists:', error);
        } finally{
            setIsCheckingIndex(false);
        }
    };
    // Handle upload cancellation
    const handleStopUpload = ()=>{
        if (abortController) {
            abortController.abort();
            setAbortController(null);
            if (selectedFile) {
                setUploadStatus((prev)=>({
                        ...prev,
                        [selectedFile.name]: {
                            status: 'error',
                            progress: 0,
                            message: 'Upload cancelled by user'
                        }
                    }));
            }
        }
    };
    // Handle submit button click
    const handleSubmit = async ()=>{
        if (!selectedFile) {
            alert('Please select a file first');
            return;
        }
        if (!indexName.trim()) {
            alert('Please provide an index name');
            return;
        }
        // Store the configuration in PINE collection with new structure
        try {
            console.log(`Storing configuration in PINE collection: embedModel=${embedModel}, indexName=${indexName}, clientEmail=${selectedClient}`);
            // Create PINE collection entry with new structure:
            // api_key -> embedding model name
            // index_name -> provided index name
            // client -> email which user selects
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPineCollectionEntry"])(embedModel, indexName, selectedClient);
            console.log('Successfully stored configuration in PINE collection');
            // Also store in localStorage for immediate use
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.setItem('faiss_index_name', indexName);
                localStorage.setItem('faiss_embed_model', embedModel);
                localStorage.setItem('faiss_client_email', selectedClient);
                console.log(`Stored credentials in localStorage: index_name=${indexName}, embed_model=${embedModel}`);
            }
        } catch (error) {
            console.error('Error storing configuration in PINE collection:', error);
        // Continue with upload even if PINE storage fails
        }
        const controller = new AbortController();
        setAbortController(controller);
        if (!selectedFile) {
            alert('Please select a file first');
            return;
        }
        if (!selectedClient) {
            alert('Please select a client');
            return;
        }
        // Print data to console in JSON format
        const data = {
            index_name: indexName,
            email: selectedClient,
            embed_model: embedModel,
            update_mode: updateMode
        };
        console.log(JSON.stringify(data, null, 2));
        // Add the file to the uploadedFiles state
        setUploadedFiles([
            selectedFile
        ]);
        // Set initial upload status
        setUploadStatus({
            [selectedFile.name]: {
                status: 'uploading',
                progress: 0,
                message: `Starting upload to FAISS index: ${indexName}...`
            }
        });
        // Initialize processing stages
        setProcessingStages({
            [selectedFile.name]: {
                currentStage: 'uploading',
                startTime: new Date()
            }
        });
        // Simulate progress updates for better UX
        let progress = 0;
        const progressInterval = setInterval(()=>{
            progress += 5;
            if (progress <= 90) {
                setUploadStatus((prev)=>({
                        ...prev,
                        [selectedFile.name]: {
                            ...prev[selectedFile.name],
                            progress,
                            message: `Uploading and processing... ${progress}%`
                        }
                    }));
                // Update processing stage based on progress
                if (progress < 30) {
                    setProcessingStages((prev)=>({
                            ...prev,
                            [selectedFile.name]: {
                                ...prev[selectedFile.name],
                                currentStage: 'uploading'
                            }
                        }));
                } else if (progress < 60) {
                    setProcessingStages((prev)=>({
                            ...prev,
                            [selectedFile.name]: {
                                ...prev[selectedFile.name],
                                currentStage: 'processing'
                            }
                        }));
                } else {
                    setProcessingStages((prev)=>({
                            ...prev,
                            [selectedFile.name]: {
                                ...prev[selectedFile.name],
                                currentStage: 'indexing'
                            }
                        }));
                }
            }
        }, 300);
        // Upload the file to FAISS with client information, index name, and update mode
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["uploadCSVToFaiss"])(selectedFile, selectedClient, indexName, updateMode, controller.signal, undefined, embedModel).then((response)=>{
            clearInterval(progressInterval);
            // Log the complete data including email information and update mode
            const completeData = {
                index_name: indexName,
                email: response.client || selectedClient,
                vector_count: response.vectorCount || 0,
                embed_model: embedModel,
                update_mode: updateMode
            };
            console.log('CSV uploaded to FAISS:', JSON.stringify(completeData, null, 2));
            // Update localStorage with the correct index name
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.setItem('faiss_index_name', indexName);
                localStorage.setItem('faiss_embed_model', embedModel);
                localStorage.setItem('faiss_client_email', selectedClient);
                console.log(`Updated credentials in localStorage: index_name=${indexName}, embed_model=${embedModel}`);
            }
            // Update processing stages
            setProcessingStages((prev)=>({
                    ...prev,
                    [selectedFile.name]: {
                        ...prev[selectedFile.name],
                        currentStage: 'complete',
                        endTime: new Date(),
                        processingTime: ((new Date().getTime() - prev[selectedFile.name].startTime.getTime()) / 1000).toFixed(1) + ' seconds',
                        timestamp: new Date().toLocaleString()
                    }
                }));
            // Update upload status
            setUploadStatus((prev)=>({
                    ...prev,
                    [selectedFile.name]: {
                        status: 'success',
                        progress: 100,
                        message: `Successfully uploaded to FAISS index: ${indexName}`,
                        vectorCount: response.vectorCount || 0
                    }
                }));
        }).catch((error)=>{
            clearInterval(progressInterval);
            if (error.name === 'AbortError') {
                console.log('Upload cancelled by user');
                return;
            }
            console.error('Error uploading CSV to FAISS:', error);
            // Update upload status
            setUploadStatus((prev)=>({
                    ...prev,
                    [selectedFile.name]: {
                        status: 'error',
                        progress: 0,
                        message: `Error: ${error.message}`
                    }
                }));
        });
    };
    // Render upload status item
    const renderUploadStatusItem = (fileName, status)=>{
        const details = processingStages[fileName] || {};
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `upload-status-item ${status.status}`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "status-header",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "file-name",
                            children: fileName
                        }, void 0, false, {
                            fileName: "[project]/components/FileUploadPage.tsx",
                            lineNumber: 340,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "file-status",
                            children: [
                                status.status === 'uploading' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "uploading",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "spinner"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 345,
                                                    columnNumber: 17
                                                }, this),
                                                "Uploading..."
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 344,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: handleStopUpload,
                                            className: "stop-button text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 text-sm font-medium px-2 py-1 rounded-md border border-red-200 dark:border-red-800 hover:border-red-300 dark:hover:border-red-700 transition-colors",
                                            children: "Stop Upload"
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 348,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 343,
                                    columnNumber: 13
                                }, this),
                                status.status === 'success' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "success",
                                    children: "Completed"
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 356,
                                    columnNumber: 45
                                }, this),
                                status.status === 'error' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "error",
                                    children: "Failed"
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 357,
                                    columnNumber: 43
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/FileUploadPage.tsx",
                            lineNumber: 341,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/FileUploadPage.tsx",
                    lineNumber: 339,
                    columnNumber: 9
                }, this),
                status.status === 'uploading' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "progress-container",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "progress-bar",
                        style: {
                            width: `${status.progress}%`
                        }
                    }, void 0, false, {
                        fileName: "[project]/components/FileUploadPage.tsx",
                        lineNumber: 363,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/FileUploadPage.tsx",
                    lineNumber: 362,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "status-message",
                    children: status.message
                }, void 0, false, {
                    fileName: "[project]/components/FileUploadPage.tsx",
                    lineNumber: 367,
                    columnNumber: 9
                }, this),
                details.currentStage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "processing-stages",
                    children: stages.map((stage, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "stage-item",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `stage-indicator ${details.currentStage === stage.id || details.currentStage === 'complete' && stage.id !== 'complete' ? 'active' : ''} ${details.currentStage === 'complete' && stage.id === 'complete' ? 'complete' : ''}`,
                                    children: details.currentStage === 'complete' && stage.id === 'complete' ? '✓' : index + 1
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 373,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "stage-label",
                                    children: stage.label
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 376,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "stage-description",
                                    children: stage.description
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 377,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, stage.id, true, {
                            fileName: "[project]/components/FileUploadPage.tsx",
                            lineNumber: 372,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/components/FileUploadPage.tsx",
                    lineNumber: 370,
                    columnNumber: 11
                }, this),
                details.currentStage === 'complete' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "upload-details",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "detail-item",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "detail-label",
                                    children: "Vector Count:"
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 386,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "detail-value",
                                    children: status.vectorCount || 'N/A'
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 387,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/FileUploadPage.tsx",
                            lineNumber: 385,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "detail-item",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "detail-label",
                                    children: "Processing Time:"
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 390,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "detail-value",
                                    children: details.processingTime
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 391,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/FileUploadPage.tsx",
                            lineNumber: 389,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "detail-item",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "detail-label",
                                    children: "Completed At:"
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 394,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "detail-value",
                                    children: details.timestamp
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 395,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/FileUploadPage.tsx",
                            lineNumber: 393,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/FileUploadPage.tsx",
                    lineNumber: 384,
                    columnNumber: 11
                }, this)
            ]
        }, fileName, true, {
            fileName: "[project]/components/FileUploadPage.tsx",
            lineNumber: 338,
            columnNumber: 7
        }, this);
    };
    // Handle update mode selection
    const handleUpdateModeSelect = (mode)=>{
        setUpdateMode(mode);
        setShowUpdateModal(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "file-upload-page flex min-h-screen",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$adminsidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                currentView: currentView,
                onViewChange: setCurrentView,
                isOpen: sidebarOpen,
                onToggle: ()=>setSidebarOpen(!sidebarOpen)
            }, void 0, false, {
                fileName: "[project]/components/FileUploadPage.tsx",
                lineNumber: 412,
                columnNumber: 7
            }, this),
            sidebarOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden",
                onClick: ()=>setSidebarOpen(false)
            }, void 0, false, {
                fileName: "[project]/components/FileUploadPage.tsx",
                lineNumber: 421,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 lg:ml-64 overflow-auto bg-white dark:bg-gray-800",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>setSidebarOpen(!sidebarOpen),
                            className: "p-2 text-gray-600 dark:text-gray-300 hover:text-primaryColor transition-colors",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "w-6 h-6",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M4 6h16M4 12h16M4 18h16"
                                }, void 0, false, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 436,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/FileUploadPage.tsx",
                                lineNumber: 435,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/FileUploadPage.tsx",
                            lineNumber: 431,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/FileUploadPage.tsx",
                        lineNumber: 430,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-w-4xl mx-auto p-6",
                        children: [
                            showUpdateModal && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full mx-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-xl font-semibold mb-4 text-gray-900 dark:text-white",
                                            children: "Index Already Exists"
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 446,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "mb-4 text-gray-600 dark:text-gray-300",
                                            children: [
                                                "An index with the name ",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-semibold",
                                                    children: indexName
                                                }, void 0, false, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 448,
                                                    columnNumber: 42
                                                }, this),
                                                " already exists. How would you like to proceed?"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 447,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-col space-y-3",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>handleUpdateModeSelect('update'),
                                                    className: "bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-colors",
                                                    children: [
                                                        "Update Existing Index",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs mt-1 text-blue-100",
                                                            children: "Keep existing data and add new vectors"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/FileUploadPage.tsx",
                                                            lineNumber: 457,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 452,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>handleUpdateModeSelect('new'),
                                                    className: "bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md transition-colors",
                                                    children: [
                                                        "Replace Existing Index",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs mt-1 text-red-100",
                                                            children: "Delete all existing data and start fresh"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/FileUploadPage.tsx",
                                                            lineNumber: 466,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 461,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setShowUpdateModal(false),
                                                    className: "bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-md transition-colors",
                                                    children: "Cancel"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 470,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 451,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 445,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/FileUploadPage.tsx",
                                lineNumber: 444,
                                columnNumber: 13
                            }, this),
                            showUpdateModal && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full mx-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-xl font-semibold mb-4 text-gray-900 dark:text-white",
                                            children: "Index Already Exists"
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 484,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "mb-4 text-gray-600 dark:text-gray-300",
                                            children: [
                                                "An index with the name ",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-semibold",
                                                    children: indexName
                                                }, void 0, false, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 486,
                                                    columnNumber: 42
                                                }, this),
                                                " already exists. How would you like to proceed?"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 485,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-col space-y-3",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>handleUpdateModeSelect('update'),
                                                    className: "bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition-colors",
                                                    children: [
                                                        "Update Existing Index",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs mt-1 text-blue-100",
                                                            children: "Keep existing data and add new vectors"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/FileUploadPage.tsx",
                                                            lineNumber: 495,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 490,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>handleUpdateModeSelect('new'),
                                                    className: "bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md transition-colors",
                                                    children: [
                                                        "Replace Existing Index",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs mt-1 text-red-100",
                                                            children: "Delete all existing data and start fresh"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/FileUploadPage.tsx",
                                                            lineNumber: 504,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 499,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>setShowUpdateModal(false),
                                                    className: "bg-gray-300 dark:bg-gray-700 hover:bg-gray-400 dark:hover:bg-gray-600 text-gray-800 dark:text-white py-2 px-4 rounded-md transition-colors",
                                                    children: "Cancel"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 508,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 489,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 483,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/FileUploadPage.tsx",
                                lineNumber: 482,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "file-upload-header text-center mb-8",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-3xl font-bold text-gray-900 dark:text-white mb-4",
                                        children: "CSV & Excel File Upload"
                                    }, void 0, false, {
                                        fileName: "[project]/components/FileUploadPage.tsx",
                                        lineNumber: 521,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-600 dark:text-gray-400 mb-4 max-w-2xl mx-auto",
                                        children: "Upload a CSV or Excel file to FAISS vector database for AI-powered search and analysis"
                                    }, void 0, false, {
                                        fileName: "[project]/components/FileUploadPage.tsx",
                                        lineNumber: 522,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "upload-restriction-badge bg-primaryColor text-white text-sm font-medium px-4 py-2 rounded-full inline-block",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            children: "One file at a time"
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 526,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/FileUploadPage.tsx",
                                        lineNumber: 525,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/FileUploadPage.tsx",
                                lineNumber: 520,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "client-selection mb-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            htmlFor: "client-dropdown",
                                            className: "block font-medium mb-2 text-gray-900 dark:text-white",
                                            children: "Select Email:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 533,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                            id: "client-dropdown",
                                            value: selectedClient,
                                            onChange: (e)=>setSelectedClient(e.target.value),
                                            className: "w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor focus:border-primaryColor bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-colors",
                                            disabled: isLoadingEmails,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "",
                                                    children: "-- Select Email --"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 543,
                                                    columnNumber: 17
                                                }, this),
                                                isLoadingEmails ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "",
                                                    disabled: true,
                                                    children: "Loading emails..."
                                                }, void 0, false, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 545,
                                                    columnNumber: 19
                                                }, this) : emails.length > 0 ? emails.map((email, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: email,
                                                        children: email
                                                    }, index, false, {
                                                        fileName: "[project]/components/FileUploadPage.tsx",
                                                        lineNumber: 548,
                                                        columnNumber: 21
                                                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "",
                                                    disabled: true,
                                                    children: "No emails available"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 553,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 536,
                                            columnNumber: 15
                                        }, this),
                                        emailError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-red-500 text-sm mt-2",
                                            children: emailError
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 556,
                                            columnNumber: 30
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-gray-500 dark:text-gray-400 mt-2",
                                            children: "Select the email address to associate with this upload."
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 557,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 532,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/FileUploadPage.tsx",
                                lineNumber: 531,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "embedding-model-section mb-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            htmlFor: "embed-model",
                                            className: "block font-medium mb-2 text-gray-900 dark:text-white",
                                            children: "Embedding Model:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 566,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                            id: "embed-model",
                                            value: embedModel,
                                            onChange: (e)=>setEmbedModel(e.target.value),
                                            className: "w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor focus:border-primaryColor bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-colors",
                                            children: Object.entries(availableModels).map(([key, model])=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: key,
                                                    children: [
                                                        model.name,
                                                        " (",
                                                        model.dimension,
                                                        "D) - ",
                                                        model.description
                                                    ]
                                                }, key, true, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 576,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 569,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-gray-500 dark:text-gray-400 mt-2",
                                            children: "Choose the embedding model for converting text to vectors. Different models have different dimensions and capabilities."
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 581,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 565,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/FileUploadPage.tsx",
                                lineNumber: 564,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "index-name-section mb-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            htmlFor: "index-name",
                                            className: "block font-medium mb-2 text-gray-900 dark:text-white",
                                            children: "Provide Index Name:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 590,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "text",
                                            id: "index-name",
                                            value: indexName,
                                            onChange: (e)=>{
                                                setIndexName(e.target.value);
                                                setApiKeyError('');
                                            },
                                            placeholder: "Enter your index name (e.g., my-data-index)",
                                            className: "w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor focus:border-primaryColor bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-colors"
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 593,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-gray-500 dark:text-gray-400 mt-2",
                                            children: "Choose a unique name for your FAISS vector index. This will be used to store and retrieve your data."
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 604,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 589,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/FileUploadPage.tsx",
                                lineNumber: 588,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "file-upload-section mb-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FileUploadWithFaiss$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            onFileUpload: (files)=>{
                                                if (files && files.length > 0) {
                                                    // Store the selected file
                                                    setSelectedFile(files[0]);
                                                    // Initialize upload status
                                                    setUploadStatus({
                                                        [files[0].name]: {
                                                            status: 'ready',
                                                            progress: 0,
                                                            message: 'File selected, ready to upload'
                                                        }
                                                    });
                                                }
                                            },
                                            maxFileSize: 50,
                                            allowedTypes: [
                                                'text/csv',
                                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                                'application/vnd.ms-excel'
                                            ],
                                            selectedLanguage: "English",
                                            showFaissUpload: !!(selectedFile && selectedClient && indexName.trim()),
                                            indexName: indexName || 'default',
                                            clientEmail: selectedClient,
                                            updateMode: updateMode,
                                            embedModel: embedModel,
                                            onFaissUploadSuccess: async (response)=>{
                                                console.log('FAISS upload successful:', response);
                                                // Create PINE collection entry after successful upload
                                                try {
                                                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPineCollectionEntry"])(embedModel, indexName, selectedClient);
                                                    console.log('PINE collection entry created after successful upload');
                                                } catch (error) {
                                                    console.error('Error creating PINE collection entry after upload:', error);
                                                }
                                                // Update upload status
                                                if (selectedFile) {
                                                    setUploadStatus((prev)=>({
                                                            ...prev,
                                                            [selectedFile.name]: {
                                                                status: 'success',
                                                                progress: 100,
                                                                message: `Successfully uploaded to FAISS index: ${indexName}`,
                                                                vectorCount: response.vectorCount || 0
                                                            }
                                                        }));
                                                    // Update processing stages
                                                    setProcessingStages((prev)=>({
                                                            ...prev,
                                                            [selectedFile.name]: {
                                                                ...prev[selectedFile.name],
                                                                currentStage: 'complete',
                                                                endTime: new Date(),
                                                                processingTime: 'Completed',
                                                                timestamp: new Date().toLocaleString()
                                                            }
                                                        }));
                                                }
                                                // Store configuration in localStorage
                                                if ("TURBOPACK compile-time truthy", 1) {
                                                    localStorage.setItem('faiss_index_name', indexName);
                                                    localStorage.setItem('faiss_embed_model', embedModel);
                                                    localStorage.setItem('faiss_client_email', selectedClient);
                                                    // Trigger a custom event to notify other components about the new upload
                                                    window.dispatchEvent(new CustomEvent('faissIndexUpdated', {
                                                        detail: {
                                                            indexName,
                                                            clientEmail: selectedClient,
                                                            embedModel
                                                        }
                                                    }));
                                                }
                                            },
                                            onFaissUploadError: (error)=>{
                                                console.error('FAISS upload error:', error);
                                                // Update upload status
                                                if (selectedFile) {
                                                    setUploadStatus((prev)=>({
                                                            ...prev,
                                                            [selectedFile.name]: {
                                                                status: 'error',
                                                                progress: 0,
                                                                message: `Error: ${error}`
                                                            }
                                                        }));
                                                }
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 613,
                                            columnNumber: 15
                                        }, this),
                                        updateMode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `update-mode-indicator mt-4 p-3 rounded-md ${updateMode === 'update' ? 'bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800' : 'bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800'}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: `inline-block w-3 h-3 rounded-full mr-2 ${updateMode === 'update' ? 'bg-blue-500' : 'bg-red-500'}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/FileUploadPage.tsx",
                                                            lineNumber: 709,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: `font-medium ${updateMode === 'update' ? 'text-blue-700 dark:text-blue-300' : 'text-red-700 dark:text-red-300'}`,
                                                            children: updateMode === 'update' ? 'Update Existing Index' : 'Replace Existing Index'
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/FileUploadPage.tsx",
                                                            lineNumber: 712,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 708,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: `text-xs mt-1 ${updateMode === 'update' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400'}`,
                                                    children: updateMode === 'update' ? 'New data will be added to the existing index without deleting current data.' : 'All existing data will be deleted before uploading new data.'
                                                }, void 0, false, {
                                                    fileName: "[project]/components/FileUploadPage.tsx",
                                                    lineNumber: 718,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/FileUploadPage.tsx",
                                            lineNumber: 704,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/FileUploadPage.tsx",
                                    lineNumber: 612,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/FileUploadPage.tsx",
                                lineNumber: 611,
                                columnNumber: 11
                            }, this),
                            Object.keys(uploadStatus).length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "upload-status-container bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-xl font-semibold mb-4 pb-2 border-b border-gray-200 dark:border-gray-600 text-gray-900 dark:text-white",
                                        children: "Upload Status"
                                    }, void 0, false, {
                                        fileName: "[project]/components/FileUploadPage.tsx",
                                        lineNumber: 733,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "upload-status-list",
                                        children: Object.entries(uploadStatus).map(([fileName, status])=>renderUploadStatusItem(fileName, status))
                                    }, void 0, false, {
                                        fileName: "[project]/components/FileUploadPage.tsx",
                                        lineNumber: 736,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/FileUploadPage.tsx",
                                lineNumber: 732,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/FileUploadPage.tsx",
                        lineNumber: 441,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/FileUploadPage.tsx",
                lineNumber: 428,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/FileUploadPage.tsx",
        lineNumber: 410,
        columnNumber: 5
    }, this);
};
_s(FileUploadPage, "mT4Nkh1JjXejwY3kG2SuSb8Fm1A=");
_c = FileUploadPage;
const __TURBOPACK__default__export__ = FileUploadPage;
var _c;
__turbopack_refresh__.register(_c, "FileUploadPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/logo5.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/logo5.e93f6c78.png");}}),
"[project]/public/images/logo5.png.mjs { IMAGE => \"[project]/public/images/logo5.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo5$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/logo5.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo5$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 223,
    height: 54,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/ACl4mJwhZ4GHBAoLTAEBAUcBAQFCAQEBTAEBAUcBAQEhABmVo54ajqCgBhMVOAUEBTcFBQY0BgUHMwUFBjAFBAUhnm0KkfiOWmoAAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 2
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/logo6.png [app-client] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/logo6.200288d9.png");}}),
"[project]/public/images/logo6.png.mjs { IMAGE => \"[project]/public/images/logo6.png [app-client] (static)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo6$2e$png__$5b$app$2d$client$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/logo6.png [app-client] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo6$2e$png__$5b$app$2d$client$5d$__$28$static$29$__["default"],
    width: 500,
    height: 113,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/AAIlLzIGV3aAJjlBQTMzMzAsLCwpNjY2My8vLywKCgoJAAEgJSgDY3J6HTY8OzU1NS4vLy8oLy8vKC0tLSYJCQkIlTgLefW61qsAAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 2
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/GradientBackground.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function GradientBackground() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: " opacity-30",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: " bg-primaryColor opacity-70 blur-[200px] size-[227px] fixed -top-56 -left-56"
            }, void 0, false, {
                fileName: "[project]/components/ui/GradientBackground.tsx",
                lineNumber: 6,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: " bg-primaryColor opacity-70 blur-[200px] size-[227px] fixed -bottom-56 left-[50%]"
            }, void 0, false, {
                fileName: "[project]/components/ui/GradientBackground.tsx",
                lineNumber: 7,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: " bg-[#00B8D9] opacity-70 blur-[200px] size-[227px] fixed -top-[300px] left-[30%]"
            }, void 0, false, {
                fileName: "[project]/components/ui/GradientBackground.tsx",
                lineNumber: 8,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: " bg-warningColor opacity-70 blur-[200px] size-[227px] fixed top-[200px] left-[50%]"
            }, void 0, false, {
                fileName: "[project]/components/ui/GradientBackground.tsx",
                lineNumber: 9,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: " bg-errorColor opacity-70 blur-[200px] size-[227px] fixed top-[400px] -left-[100px]"
            }, void 0, false, {
                fileName: "[project]/components/ui/GradientBackground.tsx",
                lineNumber: 10,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: " bg-successColor opacity-70 blur-[200px] size-[227px] fixed -bottom-[150px] -left-[150px]"
            }, void 0, false, {
                fileName: "[project]/components/ui/GradientBackground.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: " bg-warningColor opacity-70 blur-[200px] size-[227px] fixed -bottom-[200px] left-[20%]"
            }, void 0, false, {
                fileName: "[project]/components/ui/GradientBackground.tsx",
                lineNumber: 12,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/GradientBackground.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
_c = GradientBackground;
const __TURBOPACK__default__export__ = GradientBackground;
var _c;
__turbopack_refresh__.register(_c, "GradientBackground");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/file-upload-standalone/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>StandaloneFileUploadPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FileUploadPage$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/FileUploadPage.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo5$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$logo5$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/logo5.png.mjs { IMAGE => "[project]/public/images/logo5.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo6$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$logo6$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/logo6.png.mjs { IMAGE => "[project]/public/images/logo6.png [app-client] (static)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$GradientBackground$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/ui/GradientBackground.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
;
;
;
;
;
;
;
function StandaloneFileUploadPage() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [currentLogo, setCurrentLogo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo5$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$logo5$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]);
    const [showLogoutConfirm, setShowLogoutConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { resolvedTheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    // Update logo based on theme
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StandaloneFileUploadPage.useEffect": ()=>{
            setCurrentLogo(resolvedTheme === 'dark' ? __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo6$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$logo6$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"] : __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$logo5$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$logo5$2e$png__$5b$app$2d$client$5d$__$28$static$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]);
        }
    }["StandaloneFileUploadPage.useEffect"], [
        resolvedTheme
    ]);
    // Handle logout confirmation
    const handleLogout = ()=>{
        try {
            // Clear session storage
            sessionStorage.removeItem("resultUser");
            // Clear all user-related localStorage items
            localStorage.removeItem("user_email");
            localStorage.removeItem("userEmail"); // Keep this for backward compatibility
            localStorage.removeItem("pinecone_api_key");
            localStorage.removeItem("pinecone_index_name");
            localStorage.removeItem("pineconeApiKeys");
            localStorage.removeItem("userPineconeIndexes");
            localStorage.removeItem("use_dev_environment");
            localStorage.removeItem("redirectAfterLogin");
            localStorage.removeItem("faiss_index_name");
            localStorage.removeItem("faiss_embed_model");
            localStorage.removeItem("faiss_client_email");
            localStorage.removeItem("selectedFaissIndex");
            console.log("✅ Successfully cleared all user data from storage");
            // Close modal and redirect
            setShowLogoutConfirm(false);
            router.push("/sign-in");
        } catch (error) {
            console.error("❌ Error during logout:", error);
            // Still try to redirect even if clearing storage fails
            setShowLogoutConfirm(false);
            router.push("/sign-in");
        }
    };
    // Handle logout click
    const handleLogoutClick = ()=>{
        setShowLogoutConfirm(true);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-white dark:bg-n0 text-n500 dark:text-n30 relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$GradientBackground$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/file-upload-standalone/page.tsx",
                lineNumber: 65,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "container mx-auto py-6 px-25  relative z-10",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: currentLogo,
                                    alt: "QuerryOne Logo",
                                    className: "mr-1"
                                }, void 0, false, {
                                    fileName: "[project]/app/file-upload-standalone/page.tsx",
                                    lineNumber: 69,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/file-upload-standalone/page.tsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-10",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleLogoutClick,
                                    className: "flex items-center text-gray-600 dark:text-gray-300 hover:text-primaryColor transition-colors py-2 px-4 border border-gray-200 dark:border-gray-700 rounded-lg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSignOut"], {
                                            className: "mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/file-upload-standalone/page.tsx",
                                            lineNumber: 77,
                                            columnNumber: 15
                                        }, this),
                                        "Sign Out"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/file-upload-standalone/page.tsx",
                                    lineNumber: 73,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/file-upload-standalone/page.tsx",
                                lineNumber: 72,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/file-upload-standalone/page.tsx",
                        lineNumber: 67,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white dark:bg-n0 rounded-xl shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$FileUploadPage$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/app/file-upload-standalone/page.tsx",
                            lineNumber: 84,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/file-upload-standalone/page.tsx",
                        lineNumber: 83,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/file-upload-standalone/page.tsx",
                lineNumber: 66,
                columnNumber: 7
            }, this),
            showLogoutConfirm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white dark:bg-n0 p-6 rounded-lg shadow-xl max-w-sm w-full mx-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-lg font-semibold text-n800 dark:text-n10 mb-4",
                            children: "Confirm Logout"
                        }, void 0, false, {
                            fileName: "[project]/app/file-upload-standalone/page.tsx",
                            lineNumber: 92,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-n600 dark:text-n40 mb-6",
                            children: "Are you sure you want to log out? You will need to sign in again to access your account."
                        }, void 0, false, {
                            fileName: "[project]/app/file-upload-standalone/page.tsx",
                            lineNumber: 95,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-end gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setShowLogoutConfirm(false),
                                    className: "px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n800 rounded-md hover:bg-gray-300 dark:hover:bg-n700 transition-colors",
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/app/file-upload-standalone/page.tsx",
                                    lineNumber: 99,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleLogout,
                                    className: "px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSignOut"], {
                                            className: "mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/file-upload-standalone/page.tsx",
                                            lineNumber: 109,
                                            columnNumber: 17
                                        }, this),
                                        "Log Out"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/file-upload-standalone/page.tsx",
                                    lineNumber: 105,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/file-upload-standalone/page.tsx",
                            lineNumber: 98,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/file-upload-standalone/page.tsx",
                    lineNumber: 91,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/file-upload-standalone/page.tsx",
                lineNumber: 90,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/file-upload-standalone/page.tsx",
        lineNumber: 64,
        columnNumber: 5
    }, this);
}
_s(StandaloneFileUploadPage, "4Yc+E7AAg4Hyag7d5K8lKDArei0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = StandaloneFileUploadPage;
var _c;
__turbopack_refresh__.register(_c, "StandaloneFileUploadPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/file-upload-standalone/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=_460feb._.js.map