(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_3fb893._.js", {

"[project]/components/adminsidebar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
const AdminSidebar = ({ currentView = 'create', onViewChange, isOpen = true, onToggle })=>{
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const handleNavigation = (view)=>{
        if (view === 'show') {
            // Navigate to the show-index route
            router.push('/show-index');
        } else if (view === 'create') {
            // Navigate to the file-upload-standalone route
            router.push('/file-upload-standalone');
        } else if (onViewChange) {
            onViewChange(view);
        }
    };
    const menuItems = [
        {
            id: 'create',
            label: 'Create Index',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiPlus"],
            description: 'Upload CSV or Excel files to create new Fiass indexes'
        },
        {
            id: 'show',
            label: 'Show Index',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiDatabase"],
            description: 'View and manage existing PINE collection entries'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `admin-sidebar w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 fixed left-0 top-0 p-4 h-full z-40 transition-transform duration-300 ${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "sidebar-header mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2 mb-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiGear"], {
                                className: "text-primaryColor text-xl"
                            }, void 0, false, {
                                fileName: "[project]/components/adminsidebar.tsx",
                                lineNumber: 55,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-xl font-semibold dark:text-white",
                                children: "Admin Panel"
                            }, void 0, false, {
                                fileName: "[project]/components/adminsidebar.tsx",
                                lineNumber: 56,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/adminsidebar.tsx",
                        lineNumber: 54,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400",
                        children: "Manage your FiassDB and data"
                    }, void 0, false, {
                        fileName: "[project]/components/adminsidebar.tsx",
                        lineNumber: 60,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/adminsidebar.tsx",
                lineNumber: 53,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                className: "space-y-2",
                children: menuItems.map((item)=>{
                    const Icon = item.icon;
                    const isActive = currentView === item.id;
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>handleNavigation(item.id),
                        className: `block w-full text-left px-4 py-3 rounded-md transition-colors group ${isActive ? 'bg-primaryColor text-white shadow-sm' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                    className: `text-lg ${isActive ? 'text-white' : 'text-primaryColor'}`
                                }, void 0, false, {
                                    fileName: "[project]/components/adminsidebar.tsx",
                                    lineNumber: 82,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-medium",
                                            children: item.label
                                        }, void 0, false, {
                                            fileName: "[project]/components/adminsidebar.tsx",
                                            lineNumber: 88,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `text-xs mt-1 ${isActive ? 'text-white/80' : 'text-gray-500 dark:text-gray-400'}`,
                                            children: item.description
                                        }, void 0, false, {
                                            fileName: "[project]/components/adminsidebar.tsx",
                                            lineNumber: 89,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/adminsidebar.tsx",
                                    lineNumber: 87,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/adminsidebar.tsx",
                            lineNumber: 81,
                            columnNumber: 15
                        }, this)
                    }, item.id, false, {
                        fileName: "[project]/components/adminsidebar.tsx",
                        lineNumber: 72,
                        columnNumber: 13
                    }, this);
                })
            }, void 0, false, {
                fileName: "[project]/components/adminsidebar.tsx",
                lineNumber: 66,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/adminsidebar.tsx",
        lineNumber: 49,
        columnNumber: 5
    }, this);
};
_s(AdminSidebar, "fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = AdminSidebar;
const __TURBOPACK__default__export__ = AdminSidebar;
var _c;
__turbopack_refresh__.register(_c, "AdminSidebar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "checkFaissIndex": (()=>checkFaissIndex),
    "default": (()=>__TURBOPACK__default__export__),
    "deleteFaissIndex": (()=>deleteFaissIndex),
    "deleteIndexRows": (()=>deleteIndexRows),
    "fetchNews": (()=>fetchNews),
    "getIndexData": (()=>getIndexData),
    "listEmbeddingModels": (()=>listEmbeddingModels),
    "listFaissCategories": (()=>listFaissCategories),
    "queryFaissIndex": (()=>queryFaissIndex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
// Create an axios instance with default config
// Configure base URL based on environment
const baseURL = ("TURBOPACK compile-time truthy", 1) ? 'http://localhost:5010' : ("TURBOPACK unreachable", undefined);
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL,
    headers: {
        'Content-Type': 'application/json'
    }
});
const fetchNews = async (category = 'all')=>{
    try {
        const response = await api.get('/api/news', {
            params: {
                category
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching news:', error);
        throw error;
    }
};
const listEmbeddingModels = async ()=>{
    try {
        const response = await api.get('/api/list-embedding-models');
        return response.data;
    } catch (error) {
        console.error('Error fetching embedding models:', error);
        throw error;
    }
};
const listFaissCategories = async ()=>{
    try {
        const response = await api.post('/api/list-categories');
        return response.data;
    } catch (error) {
        console.error('Error fetching FAISS categories:', error);
        throw error;
    }
};
const checkFaissIndex = async (indexName, embedModel)=>{
    try {
        const response = await api.post('/api/check-index', {
            index_name: indexName,
            embed_model: embedModel
        });
        return response.data;
    } catch (error) {
        console.error('Error checking FAISS index:', error);
        throw error;
    }
};
/**
 * Get current user's email from session storage
 */ const getCurrentUserEmail = ()=>{
    try {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Try multiple sources for user email
        const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');
        if (directEmail) return directEmail;
        // Try from user session data
        const userSession = sessionStorage.getItem('resultUser');
        if (userSession) {
            const userData = JSON.parse(userSession);
            return userData.email || userData.username || null;
        }
        return null;
    } catch (error) {
        console.error('Error getting current user email:', error);
        return null;
    }
};
const queryFaissIndex = async (query, indexName, k = 5, userEmail)=>{
    try {
        // Get user email if not provided
        const emailToUse = userEmail || getCurrentUserEmail();
        const requestBody = {
            query,
            index_name: indexName,
            k
        };
        // Add user email for access validation if available
        if (emailToUse) {
            requestBody.user_email = emailToUse;
        }
        const response = await api.post('/api/query-faiss', requestBody);
        return response.data;
    } catch (error) {
        console.error('Error querying FAISS index:', error);
        throw error;
    }
};
const deleteFaissIndex = async (indexName)=>{
    try {
        const response = await api.post('/api/delete-faiss-index', {
            index_name: indexName
        });
        return response.data;
    } catch (error) {
        console.error('Error deleting FAISS index:', error);
        throw error;
    }
};
const getIndexData = async (indexName, limit = 1000, offset = 0)=>{
    try {
        const response = await api.post('/api/get-index-data', {
            index_name: indexName,
            limit,
            offset
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching index data:', error);
        throw error;
    }
};
const deleteIndexRows = async (indexName, rowIds)=>{
    try {
        const response = await api.delete('/api/delete-index-rows', {
            data: {
                index_name: indexName,
                row_ids: rowIds
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error deleting index rows:', error);
        throw error;
    }
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/fileUploadService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Format file size to human-readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} - Formatted file size (e.g., "2.5 MB")
 */ __turbopack_esm__({
    "cancelUpload": (()=>cancelUpload),
    "checkIndexExists": (()=>checkIndexExists),
    "checkUploadStatus": (()=>checkUploadStatus),
    "createPineCollectionEntry": (()=>createPineCollectionEntry),
    "fetchEmails": (()=>fetchEmails),
    "formatFileSize": (()=>formatFileSize),
    "getCSVData": (()=>getCSVData),
    "getEmbeddingModels": (()=>getEmbeddingModels),
    "getIndexesByEmail": (()=>getIndexesByEmail),
    "isCancellation": (()=>isCancellation),
    "isFileSizeValid": (()=>isFileSizeValid),
    "isFileTypeAllowed": (()=>isFileTypeAllowed),
    "listCSVFiles": (()=>listCSVFiles),
    "uploadCSVToFaiss": (()=>uploadCSVToFaiss),
    "uploadCSVToPinecone": (()=>uploadCSVToPinecone),
    "uploadFile": (()=>uploadFile),
    "uploadMultipleFiles": (()=>uploadMultipleFiles)
});
const formatFileSize = (bytes)=>{
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB',
        'TB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
const uploadCSVToFaiss = async (file, clientEmail, indexName, updateMode, signal, onProgress, embedModel)=>{
    return new Promise((resolve, reject)=>{
        try {
            // Validate file type
            if (file.type !== 'text/csv') {
                reject(new Error('Only CSV files are supported for FAISS upload'));
                return;
            }
            // Create a new FormData instance
            const formData = new FormData();
            formData.append('file', file);
            // Add client information to form data if provided
            if (clientEmail) {
                formData.append('client', clientEmail);
            }
            // Add index name to form data
            if (indexName) {
                formData.append('index_name', indexName);
            }
            // Add index name to form data
            if (indexName) {
                formData.append('index_name', indexName);
            }
            // Add update mode to form data if provided
            if (updateMode) {
                formData.append('update_mode', updateMode);
            }
            // Add embedding model to form data if provided
            if (embedModel) {
                formData.append('embed_model', embedModel);
            }
            // Create a new XMLHttpRequest and connect abort signal
            const xhr = new XMLHttpRequest();
            // Handle abort signal for client-side cancellation
            if (signal) {
                signal.onabort = ()=>{
                    xhr.abort();
                    // Instead of rejecting with an error, resolve with a cancellation object
                    // This prevents the error from appearing in the console
                    resolve({
                        success: false,
                        cancelled: true,
                        message: 'Upload cancelled by user'
                    });
                };
            }
            // Configure the request to our backend endpoint
            xhr.open('POST', 'http://localhost:5010/api/upload-csv', true);
            // Add authentication header only (no Content-Type for FormData)
            xhr.setRequestHeader('xxxid', 'FAISS');
            // Track upload progress if callback provided
            if (onProgress) {
                xhr.upload.onprogress = (event)=>{
                    if (event.lengthComputable) {
                        const progress = Math.round(event.loaded / event.total * 100);
                        onProgress(progress);
                    }
                };
            }
            // Handle response
            xhr.onload = ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        resolve({
                            success: true,
                            message: 'CSV file uploaded successfully to FAISS',
                            indexName: indexName // Use the user-provided index name
                        });
                    }
                } else {
                    reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));
                }
            };
            // Handle network errors
            xhr.onerror = ()=>{
                reject(new Error('Network error occurred while uploading CSV file'));
            };
            // Send the request
            xhr.send(formData);
        } catch (error) {
            reject(error);
        }
    });
};
const uploadCSVToPinecone = uploadCSVToFaiss;
const uploadFile = async (file, onProgress)=>{
    return new Promise((resolve, reject)=>{
        try {
            // Create a new FormData instance
            const formData = new FormData();
            formData.append('file', file);
            // Create a new XMLHttpRequest
            const xhr = new XMLHttpRequest();
            // Configure the request
            xhr.open('POST', 'http://localhost:5010/api/upload', true);
            // Track upload progress
            xhr.upload.onprogress = (event)=>{
                if (event.lengthComputable && onProgress) {
                    const progress = Math.round(event.loaded / event.total * 100);
                    onProgress(progress);
                }
            };
            // Handle response
            xhr.onload = ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        resolve({
                            success: true,
                            message: 'File uploaded successfully',
                            fileName: file.name
                        });
                    }
                } else {
                    reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));
                }
            };
            // Handle network errors
            xhr.onerror = ()=>{
                reject(new Error('Network error occurred while uploading file'));
            };
            // Send the request
            xhr.send(formData);
        } catch (error) {
            reject(error);
        }
    });
};
const uploadMultipleFiles = async (files, onProgress)=>{
    const uploadPromises = files.map((file)=>{
        return uploadFile(file, (progress)=>{
            if (onProgress) {
                onProgress(file.name, progress);
            }
        });
    });
    return Promise.all(uploadPromises);
};
const isFileTypeAllowed = (fileType, allowedTypes)=>{
    return allowedTypes.includes(fileType);
};
const isFileSizeValid = (fileSize, maxSizeMB)=>{
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return fileSize <= maxSizeBytes;
};
const listCSVFiles = async (clientEmail)=>{
    try {
        const response = await fetch('http://localhost:5010/api/list-csv-files', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                client_email: clientEmail
            })
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error listing CSV files:', error);
        throw error;
    }
};
const getCSVData = async (indexName, limit = 100, offset = 0)=>{
    try {
        const response = await fetch('http://localhost:5010/api/get-csv-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                index_name: indexName,
                limit,
                offset
            })
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error getting CSV data:', error);
        throw error;
    }
};
const getEmbeddingModels = async ()=>{
    try {
        const response = await fetch('http://localhost:5010/api/list-embedding-models', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error getting embedding models:', error);
        // Return fallback data when backend is not available
        return {
            success: true,
            models: {
                "all-MiniLM-L6-v2": {
                    "name": "all-MiniLM-L6-v2",
                    "description": "Sentence Transformers model for semantic similarity",
                    "dimensions": 384
                },
                "all-mpnet-base-v2": {
                    "name": "all-mpnet-base-v2",
                    "description": "High-quality sentence embeddings",
                    "dimensions": 768
                },
                "paraphrase-MiniLM-L6-v2": {
                    "name": "paraphrase-MiniLM-L6-v2",
                    "description": "Paraphrase detection model",
                    "dimensions": 384
                }
            },
            default_model: "all-MiniLM-L6-v2"
        };
    }
};
const isCancellation = (errorOrResponse)=>{
    // Check for our custom cancellation response
    if (errorOrResponse && errorOrResponse.cancelled === true) {
        return true;
    }
    // Check for error message containing cancellation text
    if (errorOrResponse instanceof Error) {
        const errorMessage = errorOrResponse.message.toLowerCase();
        return errorMessage.includes('cancel') || errorMessage.includes('abort') || errorMessage.includes('user interrupt');
    }
    // Check for response with cancellation status
    if (errorOrResponse && errorOrResponse.status === 'cancelled') {
        return true;
    }
    // Check for response with error_type indicating cancellation
    if (errorOrResponse && errorOrResponse.error_type === 'upload_cancelled') {
        return true;
    }
    return false;
};
const fetchEmails = async ()=>{
    try {
        const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eSearch', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'xxxid': 'QUKTYWK'
            }
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        const data = await response.json();
        if (data.statusCode === 200 && Array.isArray(data.source)) {
            // Parse each JSON string in the source array and extract emails
            const emails = data.source.map((jsonStr)=>{
                try {
                    const userObj = JSON.parse(jsonStr);
                    return userObj.email || '';
                } catch (error) {
                    console.error('Error parsing JSON:', error);
                    return '';
                }
            }).filter(Boolean); // Remove empty strings
            return emails;
        }
        return [];
    } catch (error) {
        console.error('Error fetching emails:', error);
        return [];
    }
};
const createPineCollectionEntry = async (embedModel, indexName, clientEmail)=>{
    try {
        console.log(`Creating PINE collection entry: embedModel=${embedModel}, indexName=${indexName}, clientEmail=${clientEmail}`);
        const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eCreateCol?colname=PINE', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'xxxid': 'PINE'
            },
            body: JSON.stringify({
                api_key: embedModel,
                index_name: indexName,
                client: clientEmail // Store email as client
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }
        const result = await response.json();
        console.log('PINE collection entry created successfully:', result);
        return result;
    } catch (error) {
        console.error('Error creating PINE collection entry:', error);
        throw error;
    }
};
const getIndexesByEmail = async (clientEmail)=>{
    try {
        console.log(`Fetching indexes for email: ${clientEmail}`);
        const response = await fetch(`https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&filtercount=1&f1_field=client_S&f1_op=eq&f1_value=${encodeURIComponent(clientEmail)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'xxxid': 'PINE'
            }
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        const data = await response.json();
        console.log('PINE collection response for email:', data);
        if (data.statusCode === 200 && Array.isArray(data.source)) {
            // Parse each JSON string in the source array
            const indexes = data.source.map((jsonStr, index)=>{
                try {
                    const indexObj = JSON.parse(jsonStr);
                    return {
                        _id: indexObj._id?.$oid || indexObj._id || `pine-item-${index}`,
                        email: indexObj.client || clientEmail,
                        index_name: indexObj.index_name || 'N/A',
                        embed_model: indexObj.api_key || 'N/A',
                        source: 'PINE',
                        originalData: indexObj
                    };
                } catch (error) {
                    console.error('Error parsing PINE index JSON:', error);
                    return null;
                }
            }).filter((item)=>item !== null);
            return indexes;
        }
        return [];
    } catch (error) {
        console.error('Error fetching indexes by email:', error);
        return [];
    }
};
const checkIndexExists = async (indexName, client, embedModel)=>{
    try {
        const response = await fetch('http://localhost:5010/api/check-index', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                index_name: indexName,
                client: client,
                embed_model: embedModel
            })
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        const data = await response.json();
        if (data.success) {
            return {
                exists: data.exists,
                embedding_model: data.embedding_model
            };
        }
        return {
            exists: false
        };
    } catch (error) {
        console.error('Error checking if index exists:', error);
        return {
            exists: false
        };
    }
};
const cancelUpload = async (uploadId, abortController)=>{
    try {
        // First, abort the HTTP request if an AbortController is provided
        if (abortController) {
            try {
                abortController.abort();
                console.log('HTTP request aborted');
            } catch (abortError) {
                // Don't log this as an error since it's expected behavior
                console.log('Note: AbortController already used or not applicable');
            // Continue with server-side cancellation even if client-side abort fails
            }
        }
        // Then, send a cancellation request to the server
        console.log(`Sending cancellation request for upload ${uploadId}`);
        const response = await fetch('http://localhost:5010/api/cancel-upload', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                upload_id: uploadId
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }
        const data = await response.json();
        console.log('Cancellation response:', data);
        // Verify cancellation by checking status
        try {
            const statusResponse = await checkUploadStatus(uploadId);
            console.log('Status after cancellation:', statusResponse);
        } catch (statusError) {
            console.error('Error checking status after cancellation:', statusError);
        // Continue even if status check fails
        }
        return data;
    } catch (error) {
        console.error('Error cancelling upload:', error);
        throw error;
    }
};
const checkUploadStatus = async (uploadId, silent = false)=>{
    try {
        const response = await fetch('http://localhost:5010/api/upload-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                upload_id: uploadId
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }
        const data = await response.json();
        // Log cancellation status if detected
        if (data.success && data.cancelled) {
            console.log(`Upload ${uploadId} is marked as cancelled. Status: ${data.status}`);
        }
        return data;
    } catch (error) {
        if (!silent) {
            console.error('Error checking upload status:', error);
        }
        throw error;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/services/dataManagementService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Data Management Service for FAISS Index Data
 * Handles fetching, filtering, and exporting JSON data from FAISS indexes
 */ __turbopack_esm__({
    "deleteIndexRows": (()=>deleteIndexRows),
    "exportToCSV": (()=>exportToCSV),
    "exportToJSON": (()=>exportToJSON),
    "fetchIndexData": (()=>fetchIndexData),
    "filterData": (()=>filterData),
    "getUniqueColumnValues": (()=>getUniqueColumnValues)
});
const fetchIndexData = async (indexName, limit = 1000, offset = 0)=>{
    try {
        // First try to get the JSON metadata file directly
        const response = await fetch(`http://localhost:5010/api/get-faiss-metadata/${indexName}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        const result = await response.json();
        if (result.success) {
            // Transform the data to ensure each row has a unique ID
            const transformedData = result.data.map((row, index)=>({
                    id: row.id || `row_${offset + index}`,
                    ...row
                }));
            return {
                success: true,
                data: transformedData,
                total: result.total || transformedData.length
            };
        } else {
            return {
                success: false,
                data: [],
                total: 0,
                error: result.error || 'Failed to fetch data'
            };
        }
    } catch (error) {
        console.error('Error fetching index data:', error);
        return {
            success: false,
            data: [],
            total: 0,
            error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
    }
};
const deleteIndexRows = async (indexName, rowIds)=>{
    try {
        const response = await fetch(`http://localhost:5010/api/delete-faiss-rows/${indexName}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                row_ids: rowIds
            })
        });
        if (!response.ok) {
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
        const result = await response.json();
        if (result.success) {
            return {
                success: true,
                deletedCount: result.deleted_count || rowIds.length
            };
        } else {
            return {
                success: false,
                deletedCount: 0,
                error: result.error || 'Failed to delete rows'
            };
        }
    } catch (error) {
        console.error('Error deleting index rows:', error);
        return {
            success: false,
            deletedCount: 0,
            error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
    }
};
const exportToCSV = (data, filename)=>{
    if (data.length === 0) {
        console.warn('No data to export');
        return;
    }
    // Get all unique keys from the data
    const allKeys = Array.from(new Set(data.flatMap((row)=>Object.keys(row))));
    // Create CSV header
    const csvHeader = allKeys.join(',');
    // Create CSV rows
    const csvRows = data.map((row)=>{
        return allKeys.map((key)=>{
            const value = row[key];
            // Handle values that might contain commas or quotes
            if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                return `"${value.replace(/"/g, '""')}"`;
            }
            return value || '';
        }).join(',');
    });
    // Combine header and rows
    const csvContent = [
        csvHeader,
        ...csvRows
    ].join('\n');
    // Create and download the file
    const blob = new Blob([
        csvContent
    ], {
        type: 'text/csv;charset=utf-8;'
    });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename || `index_data_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
};
const exportToJSON = (data, filename)=>{
    if (data.length === 0) {
        console.warn('No data to export');
        return;
    }
    const jsonContent = JSON.stringify(data, null, 2);
    // Create and download the file
    const blob = new Blob([
        jsonContent
    ], {
        type: 'application/json;charset=utf-8;'
    });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename || `index_data_${new Date().toISOString().split('T')[0]}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
};
const filterData = (data, searchTerm)=>{
    if (!searchTerm.trim()) {
        return data;
    }
    const searchLower = searchTerm.toLowerCase().trim();
    return data.filter((row)=>{
        return Object.values(row).some((value)=>{
            if (value === null || value === undefined) return false;
            return String(value).toLowerCase().includes(searchLower);
        });
    });
};
const getUniqueColumnValues = (data, columnKey)=>{
    const values = data.map((row)=>row[columnKey]).filter((value)=>value !== null && value !== undefined).map((value)=>String(value));
    return Array.from(new Set(values)).sort();
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/modals/EditDataModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$dataManagementService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/services/dataManagementService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
const EditDataModal = ({ isOpen, onClose, indexData })=>{
    _s();
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [selectedRows, setSelectedRows] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Set());
    const [isDeleting, setIsDeleting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Filter states
    const [urlFilter, setUrlFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [categoryFilter, setCategoryFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [showFilters, setShowFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Pagination state
    const [currentPage, setCurrentPage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const [pageSize] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(20); // Fixed page size, can be made configurable
    const [totalRows, setTotalRows] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [allData, setAllData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]); // Store all data for search/export
    // Enhanced filtering function that includes URL and category filters
    const applyAllFilters = (dataToFilter)=>{
        let filtered = dataToFilter;
        // Apply search term filter
        if (searchTerm.trim()) {
            filtered = (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$dataManagementService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["filterData"])(filtered, searchTerm);
        }
        // Apply URL filter
        if (urlFilter.trim()) {
            filtered = filtered.filter((row)=>{
                const url = row.url || '';
                return String(url).toLowerCase().includes(urlFilter.toLowerCase());
            });
        }
        // Apply category filter
        if (categoryFilter.trim()) {
            filtered = filtered.filter((row)=>{
                const category = row.category || '';
                return String(category).toLowerCase().includes(categoryFilter.toLowerCase());
            });
        }
        return filtered;
    };
    // Filter data based on all filters (for current page)
    const filteredData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "EditDataModal.useMemo[filteredData]": ()=>{
            return applyAllFilters(data);
        }
    }["EditDataModal.useMemo[filteredData]"], [
        data,
        searchTerm,
        urlFilter,
        categoryFilter
    ]);
    // For search functionality, we need to work with all data
    const allFilteredData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "EditDataModal.useMemo[allFilteredData]": ()=>{
            return applyAllFilters(allData);
        }
    }["EditDataModal.useMemo[allFilteredData]"], [
        allData,
        searchTerm,
        urlFilter,
        categoryFilter
    ]);
    // Calculate pagination values (after filtered data is calculated)
    const totalPages = searchTerm ? Math.ceil(allFilteredData.length / pageSize) : Math.ceil(totalRows / pageSize);
    const hasNextPage = currentPage < totalPages;
    const hasPrevPage = currentPage > 1;
    // Load data when modal opens or page changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EditDataModal.useEffect": ()=>{
            if (isOpen && indexData) {
                loadData();
            }
        }
    }["EditDataModal.useEffect"], [
        isOpen,
        indexData,
        currentPage
    ]);
    // Reset pagination when filters change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EditDataModal.useEffect": ()=>{
            if (searchTerm || urlFilter || categoryFilter) {
                setCurrentPage(1);
            }
        }
    }["EditDataModal.useEffect"], [
        searchTerm,
        urlFilter,
        categoryFilter
    ]);
    // Clear state when modal closes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EditDataModal.useEffect": ()=>{
            if (!isOpen) {
                setData([]);
                setAllData([]);
                setSearchTerm("");
                setUrlFilter("");
                setCategoryFilter("");
                setShowFilters(false);
                setSelectedRows(new Set());
                setError(null);
                setShowDeleteConfirm(false);
                setCurrentPage(1);
                setTotalRows(0);
            }
        }
    }["EditDataModal.useEffect"], [
        isOpen
    ]);
    const loadData = async ()=>{
        if (!indexData) return;
        setLoading(true);
        setError(null);
        try {
            // Calculate offset for pagination
            const offset = (currentPage - 1) * pageSize;
            // If we have any filters, we need to load all data first for proper filtering
            if (searchTerm || urlFilter || categoryFilter) {
                // Load all data for filtering
                const allResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$dataManagementService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchIndexData"])(indexData.index_name, 10000, 0); // Large limit to get all data
                if (allResult.success) {
                    setAllData(allResult.data);
                    const filtered = applyAllFilters(allResult.data);
                    // Get the current page of filtered data
                    const paginatedData = filtered.slice(offset, offset + pageSize);
                    setData(paginatedData);
                    setTotalRows(filtered.length);
                } else {
                    setError(allResult.error || 'Failed to load data');
                }
            } else {
                // Load paginated data directly
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$dataManagementService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchIndexData"])(indexData.index_name, pageSize, offset);
                if (result.success) {
                    setData(result.data);
                    setTotalRows(result.total);
                    // Also load all data for export functionality (only on first page load)
                    if (currentPage === 1) {
                        const allResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$dataManagementService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchIndexData"])(indexData.index_name, 10000, 0);
                        if (allResult.success) {
                            setAllData(allResult.data);
                        }
                    }
                } else {
                    setError(result.error || 'Failed to load data');
                }
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Unknown error occurred');
        } finally{
            setLoading(false);
        }
    };
    // Pagination navigation functions
    const handleNextPage = ()=>{
        if (hasNextPage) {
            setCurrentPage((prev)=>prev + 1);
        }
    };
    const handlePrevPage = ()=>{
        if (hasPrevPage) {
            setCurrentPage((prev)=>prev - 1);
        }
    };
    const handleRowSelect = (rowId)=>{
        const newSelected = new Set(selectedRows);
        if (newSelected.has(rowId)) {
            newSelected.delete(rowId);
        } else {
            newSelected.add(rowId);
        }
        setSelectedRows(newSelected);
    };
    const handleSelectAll = ()=>{
        if (selectedRows.size === filteredData.length) {
            setSelectedRows(new Set());
        } else {
            setSelectedRows(new Set(filteredData.map((row)=>row.id)));
        }
    };
    const handleDeleteSelected = async ()=>{
        if (!indexData || selectedRows.size === 0) return;
        setIsDeleting(true);
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$dataManagementService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteIndexRows"])(indexData.index_name, Array.from(selectedRows));
            if (result.success) {
                // Remove deleted rows from local state
                setData((prevData)=>prevData.filter((row)=>!selectedRows.has(row.id)));
                setSelectedRows(new Set());
                setShowDeleteConfirm(false);
            } else {
                setError(result.error || 'Failed to delete rows');
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Unknown error occurred');
        } finally{
            setIsDeleting(false);
        }
    };
    const handleExportCSV = ()=>{
        const dataToExport = searchTerm ? allFilteredData : allData;
        const filename = `${indexData?.index_name || 'index'}_data_${new Date().toISOString().split('T')[0]}.csv`;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$dataManagementService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["exportToCSV"])(dataToExport, filename);
    };
    const handleExportJSON = ()=>{
        const dataToExport = searchTerm ? allFilteredData : allData;
        const filename = `${indexData?.index_name || 'index'}_data_${new Date().toISOString().split('T')[0]}.json`;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$dataManagementService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["exportToJSON"])(dataToExport, filename);
    };
    // Get unique values for filters
    const uniqueUrls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "EditDataModal.useMemo[uniqueUrls]": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$dataManagementService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getUniqueColumnValues"])(allData, 'url').slice(0, 20); // Limit to 20 for performance
        }
    }["EditDataModal.useMemo[uniqueUrls]"], [
        allData
    ]);
    const uniqueCategories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "EditDataModal.useMemo[uniqueCategories]": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$dataManagementService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getUniqueColumnValues"])(allData, 'category').slice(0, 20); // Limit to 20 for performance
        }
    }["EditDataModal.useMemo[uniqueCategories]"], [
        allData
    ]);
    // Get column headers from the first row of data (prefer allData if available)
    // This useMemo must be called before any early returns to maintain hook order
    const columns = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "EditDataModal.useMemo[columns]": ()=>{
            if (data.length > 0) return Object.keys(data[0]);
            if (allData.length > 0) return Object.keys(allData[0]);
            return [];
        }
    }["EditDataModal.useMemo[columns]"], [
        data,
        allData
    ]);
    if (!isOpen || !indexData) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white dark:bg-n800 rounded-lg w-full max-w-7xl max-h-[90vh] flex flex-col",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between p-6 border-b border-gray-200 dark:border-n600",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiTable"], {
                                    className: "text-blue-500 text-2xl mr-3"
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 289,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-lg font-semibold text-n700 dark:text-n20",
                                            children: [
                                                "Edit Data: ",
                                                indexData.index_name
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 291,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-n500 dark:text-n40",
                                            children: indexData.email
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 294,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 290,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditDataModal.tsx",
                            lineNumber: 288,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onClose,
                            className: "p-2 hover:bg-gray-100 dark:hover:bg-n700 rounded-md transition-colors",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {
                                className: "text-xl text-n500 dark:text-n40"
                            }, void 0, false, {
                                fileName: "[project]/components/modals/EditDataModal.tsx",
                                lineNumber: 303,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/modals/EditDataModal.tsx",
                            lineNumber: 299,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/modals/EditDataModal.tsx",
                    lineNumber: 287,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "p-6 border-b border-gray-200 dark:border-n600",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative flex-1 max-w-md",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiMagnifyingGlass"], {
                                                    className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-n400 dark:text-n500"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 314,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "text",
                                                    placeholder: "Search across all fields...",
                                                    value: searchTerm,
                                                    onChange: (e)=>setSearchTerm(e.target.value),
                                                    className: "w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 transition-all duration-200"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 315,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 313,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>setShowFilters(!showFilters),
                                            className: `px-4 py-2 rounded-lg border transition-all duration-200 flex items-center gap-2 ${showFilters || urlFilter || categoryFilter ? 'bg-primaryColor text-white border-primaryColor shadow-md' : 'bg-white dark:bg-n800 text-n700 dark:text-n20 border-gray-300 dark:border-n600 hover:bg-gray-50 dark:hover:bg-n700'}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiFunnel"], {
                                                    className: "text-sm"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 333,
                                                    columnNumber: 17
                                                }, this),
                                                "Filters",
                                                (urlFilter || categoryFilter) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "bg-white bg-opacity-20 text-xs px-2 py-1 rounded-full",
                                                    children: [
                                                        urlFilter,
                                                        categoryFilter
                                                    ].filter(Boolean).length
                                                }, void 0, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 336,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 325,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 311,
                                    columnNumber: 13
                                }, this),
                                showFilters && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-50 dark:bg-n700 rounded-lg p-4 border border-gray-200 dark:border-n600",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "relative",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            className: "block text-sm font-medium text-n700 dark:text-white mb-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiGlobe"], {
                                                                    className: "inline mr-2 text-primaryColor"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                    lineNumber: 350,
                                                                    columnNumber: 23
                                                                }, this),
                                                                "Filter by URL"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                            lineNumber: 349,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "relative",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                    type: "text",
                                                                    placeholder: "Enter URL to filter...",
                                                                    value: urlFilter,
                                                                    onChange: (e)=>setUrlFilter(e.target.value),
                                                                    className: "w-full px-3 py-2 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 transition-all duration-200"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                    lineNumber: 354,
                                                                    columnNumber: 23
                                                                }, this),
                                                                urlFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    onClick: ()=>setUrlFilter(""),
                                                                    className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-n400 hover:text-n600 dark:hover:text-n300 transition-colors",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {}, void 0, false, {
                                                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                        lineNumber: 366,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                    lineNumber: 362,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                            lineNumber: 353,
                                                            columnNumber: 21
                                                        }, this),
                                                        uniqueUrls.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mt-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-xs text-n500 dark:text-n400 mb-1",
                                                                    children: "Common URLs:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                    lineNumber: 372,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex flex-wrap gap-1",
                                                                    children: uniqueUrls.slice(0, 5).map((url, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                            onClick: ()=>setUrlFilter(url),
                                                                            className: "text-xs px-2 py-1 bg-white dark:bg-n800 border border-gray-300 dark:border-n600 rounded text-n700 dark:text-n20 hover:bg-primaryColor hover:text-white hover:border-primaryColor transition-all duration-200",
                                                                            title: url,
                                                                            children: url.length > 20 ? `${url.substring(0, 20)}...` : url
                                                                        }, index, false, {
                                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                            lineNumber: 375,
                                                                            columnNumber: 29
                                                                        }, this))
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                    lineNumber: 373,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                            lineNumber: 371,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 348,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "relative",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            className: "block text-sm font-medium text-n700 dark:text-n20 mb-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiTag"], {
                                                                    className: "inline mr-2 text-primaryColor"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                    lineNumber: 392,
                                                                    columnNumber: 23
                                                                }, this),
                                                                "Filter by Category"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                            lineNumber: 391,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "relative",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                    type: "text",
                                                                    placeholder: "Enter category to filter...",
                                                                    value: categoryFilter,
                                                                    onChange: (e)=>setCategoryFilter(e.target.value),
                                                                    className: "w-full px-3 py-2 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 transition-all duration-200"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                    lineNumber: 396,
                                                                    columnNumber: 23
                                                                }, this),
                                                                categoryFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    onClick: ()=>setCategoryFilter(""),
                                                                    className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-n400 hover:text-n600 dark:hover:text-n300 transition-colors",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {}, void 0, false, {
                                                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                        lineNumber: 408,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                    lineNumber: 404,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                            lineNumber: 395,
                                                            columnNumber: 21
                                                        }, this),
                                                        uniqueCategories.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mt-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-xs text-n500 dark:text-n400 mb-1",
                                                                    children: "Available Categories:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                    lineNumber: 414,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex flex-wrap gap-1",
                                                                    children: uniqueCategories.map((category, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                            onClick: ()=>setCategoryFilter(category),
                                                                            className: "text-xs px-2 py-1 bg-white dark:bg-n800 border border-gray-300 dark:border-n600 rounded text-n700 dark:text-n20 hover:bg-primaryColor hover:text-white hover:border-primaryColor transition-all duration-200",
                                                                            children: category
                                                                        }, index, false, {
                                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                            lineNumber: 417,
                                                                            columnNumber: 29
                                                                        }, this))
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                                    lineNumber: 415,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                            lineNumber: 413,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 390,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 346,
                                            columnNumber: 17
                                        }, this),
                                        (urlFilter || categoryFilter) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "mt-4 pt-4 border-t border-gray-200 dark:border-n600",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>{
                                                    setUrlFilter("");
                                                    setCategoryFilter("");
                                                },
                                                className: "text-sm text-primaryColor hover:bg-gray-50 dark:hover:bg-gray-800/10 px-2 py-1 rounded-md transition-colors flex items-center gap-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {
                                                        className: "text-xs"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                                        lineNumber: 441,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Clear all filters"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/modals/EditDataModal.tsx",
                                                lineNumber: 434,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 433,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 345,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditDataModal.tsx",
                            lineNumber: 309,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-2 flex-wrap mt-4",
                            children: [
                                selectedRows.size > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setShowDeleteConfirm(true),
                                    className: "px-4 py-2 bg-errorColor text-white rounded-lg hover:bg-errorColor/90 transition-all duration-200 flex items-center shadow-sm hover:shadow-md",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiTrash"], {
                                            className: "mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 457,
                                            columnNumber: 17
                                        }, this),
                                        "Delete (",
                                        selectedRows.size,
                                        ")"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 453,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleExportCSV,
                                    disabled: totalRows === 0,
                                    className: "px-4 py-2 bg-successColor text-white rounded-lg hover:bg-successColor/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-sm hover:shadow-md",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiFileCsv"], {
                                            className: "mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 467,
                                            columnNumber: 15
                                        }, this),
                                        "Export CSV"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 462,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleExportJSON,
                                    disabled: totalRows === 0,
                                    className: "px-4 py-2 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-sm hover:shadow-md",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiFileText"], {
                                            className: "mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 476,
                                            columnNumber: 15
                                        }, this),
                                        "Export JSON"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 471,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditDataModal.tsx",
                            lineNumber: 451,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-4 flex flex-col sm:flex-row gap-2 sm:gap-0 justify-between items-start sm:items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm text-n500 dark:text-n40",
                                    children: searchTerm || urlFilter || categoryFilter ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    "Showing ",
                                                    filteredData.length,
                                                    " of ",
                                                    allFilteredData.length,
                                                    " filtered results (Page ",
                                                    currentPage,
                                                    ")"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/modals/EditDataModal.tsx",
                                                lineNumber: 486,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex flex-wrap gap-2 text-xs",
                                                children: [
                                                    searchTerm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "bg-primaryColor/10 text-primaryColor px-2 py-1 rounded-full",
                                                        children: [
                                                            'Search: "',
                                                            searchTerm,
                                                            '"'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                                        lineNumber: 489,
                                                        columnNumber: 23
                                                    }, this),
                                                    urlFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "bg-infoColor/10 text-infoColor px-2 py-1 rounded-full",
                                                        children: [
                                                            'URL: "',
                                                            urlFilter,
                                                            '"'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                                        lineNumber: 494,
                                                        columnNumber: 23
                                                    }, this),
                                                    categoryFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "bg-warningColor/10 text-warningColor px-2 py-1 rounded-full",
                                                        children: [
                                                            'Category: "',
                                                            categoryFilter,
                                                            '"'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                                        lineNumber: 499,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/modals/EditDataModal.tsx",
                                                lineNumber: 487,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                        lineNumber: 485,
                                        columnNumber: 17
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            "Showing ",
                                            (currentPage - 1) * pageSize + 1,
                                            "-",
                                            Math.min(currentPage * pageSize, totalRows),
                                            " of ",
                                            totalRows,
                                            " rows"
                                        ]
                                    }, void 0, true)
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 483,
                                    columnNumber: 13
                                }, this),
                                ((searchTerm || urlFilter || categoryFilter) && Math.ceil(allFilteredData.length / pageSize) > 1 || !(searchTerm || urlFilter || categoryFilter) && totalPages > 1) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: handlePrevPage,
                                            disabled: !hasPrevPage || loading,
                                            className: `px-3 py-2 text-sm rounded-lg border transition-colors flex items-center gap-1 ${!hasPrevPage || loading ? 'bg-gray-100 dark:bg-n700 text-n400 dark:text-n500 border-gray-200 dark:border-n600 cursor-not-allowed' : 'bg-white dark:bg-n800 text-n700 dark:text-n20 border-gray-300 dark:border-n600 hover:bg-gray-50 dark:hover:bg-gray-800/10'}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCaretLeft"], {}, void 0, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 524,
                                                    columnNumber: 19
                                                }, this),
                                                "Previous"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 515,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-n500 dark:text-n40 px-2",
                                            children: [
                                                "Page ",
                                                currentPage,
                                                " of ",
                                                searchTerm || urlFilter || categoryFilter ? Math.ceil(allFilteredData.length / pageSize) : totalPages
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 528,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: handleNextPage,
                                            disabled: !hasNextPage || loading,
                                            className: `px-3 py-2 text-sm rounded-lg border transition-colors flex items-center gap-1 ${!hasNextPage || loading ? 'bg-gray-100 dark:bg-n700 text-n400 dark:text-n500 border-gray-200 dark:border-n600 cursor-not-allowed' : 'bg-white dark:bg-n800 text-n700 dark:text-n20 border-gray-300 dark:border-n600 hover:bg-gray-50 dark:hover:bg-gray-800/10'}`,
                                            children: [
                                                "Next",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCaretRight"], {}, void 0, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 542,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 532,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 514,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditDataModal.tsx",
                            lineNumber: 482,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/modals/EditDataModal.tsx",
                    lineNumber: 308,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-1 overflow-hidden",
                    children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-center h-64",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSpinner"], {
                                    className: "animate-spin text-4xl text-primaryColor mx-auto mb-4"
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 554,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-n600 dark:text-n30 font-medium",
                                    children: "Loading data..."
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 555,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-2 w-32 h-1 bg-gray-200 dark:bg-n600 rounded-full mx-auto overflow-hidden",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-full bg-primaryColor rounded-full animate-pulse"
                                    }, void 0, false, {
                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                        lineNumber: 557,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 556,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditDataModal.tsx",
                            lineNumber: 553,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/modals/EditDataModal.tsx",
                        lineNumber: 552,
                        columnNumber: 13
                    }, this) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-center h-64",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center max-w-md",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiWarning"], {
                                    className: "text-4xl text-errorColor mx-auto mb-4"
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 564,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-errorColor dark:text-errorColor mb-4 font-medium",
                                    children: error
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 565,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: loadData,
                                    className: "px-6 py-2 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 transition-all duration-200 shadow-sm hover:shadow-md",
                                    children: "Try Again"
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 566,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditDataModal.tsx",
                            lineNumber: 563,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/modals/EditDataModal.tsx",
                        lineNumber: 562,
                        columnNumber: 13
                    }, this) : totalRows === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-center h-64",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiTable"], {
                                    className: "text-4xl text-n300 dark:text-n600 mx-auto mb-4"
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 577,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-n600 dark:text-n30 font-medium",
                                    children: searchTerm || urlFilter || categoryFilter ? 'No data matches your filters' : 'No data found for this index'
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 578,
                                    columnNumber: 17
                                }, this),
                                (searchTerm || urlFilter || categoryFilter) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>{
                                        setSearchTerm("");
                                        setUrlFilter("");
                                        setCategoryFilter("");
                                    },
                                    className: "mt-3 text-sm text-primaryColor hover:bg-gray-50 dark:hover:bg-gray-800/10 px-3 py-2 rounded-md transition-colors flex items-center gap-1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {
                                            className: "text-xs"
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 593,
                                            columnNumber: 21
                                        }, this),
                                        "Clear all filters"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 585,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditDataModal.tsx",
                            lineNumber: 576,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/modals/EditDataModal.tsx",
                        lineNumber: 575,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "overflow-auto h-full",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                            className: "w-full",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                    className: "bg-gray-50 dark:bg-n700 sticky top-0 z-10 shadow-sm",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "px-4 py-3 text-left border-b border-gray-200 dark:border-n600",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: handleSelectAll,
                                                    className: "p-1 hover:bg-gray-200 dark:hover:bg-n600 rounded transition-colors duration-200",
                                                    children: selectedRows.size === filteredData.length && filteredData.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCheckSquare"], {
                                                        className: "text-primaryColor"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                                        lineNumber: 610,
                                                        columnNumber: 27
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSquare"], {
                                                        className: "text-n400 dark:text-n500 hover:text-primaryColor transition-colors duration-200"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                                        lineNumber: 612,
                                                        columnNumber: 27
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 605,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/components/modals/EditDataModal.tsx",
                                                lineNumber: 604,
                                                columnNumber: 21
                                            }, this),
                                            columns.map((column)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: "px-4 py-3 text-left text-xs font-semibold text-n600 dark:text-n30 uppercase tracking-wider border-b border-gray-200 dark:border-n600",
                                                    children: column.replace(/_/g, ' ')
                                                }, column, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 617,
                                                    columnNumber: 23
                                                }, this))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                        lineNumber: 603,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 602,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                    className: "bg-white dark:bg-n800 divide-y divide-gray-200 dark:divide-n600",
                                    children: filteredData.map((row, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                            className: `${selectedRows.has(row.id) ? 'bg-primaryColor/5 dark:bg-primaryColor/10 border-l-2 border-primaryColor' : index % 2 === 0 ? 'bg-white dark:bg-n800' : 'bg-gray-25 dark:bg-n800/50'}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "px-4 py-3",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>handleRowSelect(row.id),
                                                        className: "p-1 hover:bg-gray-200 dark:hover:bg-n600 rounded transition-all duration-200 hover:scale-110",
                                                        children: selectedRows.has(row.id) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCheckSquare"], {
                                                            className: "text-primaryColor"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                            lineNumber: 644,
                                                            columnNumber: 29
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSquare"], {
                                                            className: "text-n400 dark:text-n500 hover:text-primaryColor transition-colors duration-200"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                            lineNumber: 646,
                                                            columnNumber: 29
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                                        lineNumber: 639,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 638,
                                                    columnNumber: 23
                                                }, this),
                                                columns.map((column)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "px-4 py-3 text-sm text-n700 dark:text-n20 max-w-xs truncate",
                                                        title: String(row[column] || ''),
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: `${column === 'url' ? 'text-infoColor hover:underline cursor-pointer' : column === 'category' ? 'text-warningColor font-medium' : column === 'title' ? 'font-medium text-n700 dark:text-n20' : 'text-n700 dark:text-n20'}`,
                                                            children: String(row[column] || '')
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                                            lineNumber: 656,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, column, false, {
                                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                                        lineNumber: 651,
                                                        columnNumber: 25
                                                    }, this))
                                            ]
                                        }, row.id, true, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 628,
                                            columnNumber: 21
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                    lineNumber: 626,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/modals/EditDataModal.tsx",
                            lineNumber: 601,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/modals/EditDataModal.tsx",
                        lineNumber: 600,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/modals/EditDataModal.tsx",
                    lineNumber: 550,
                    columnNumber: 9
                }, this),
                showDeleteConfirm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white dark:bg-n800 rounded-xl p-6 max-w-md w-full mx-4 shadow-2xl border border-gray-200 dark:border-n600",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center mb-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-2 bg-errorColor/10 rounded-full mr-3",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiWarning"], {
                                            className: "text-errorColor text-2xl"
                                        }, void 0, false, {
                                            fileName: "[project]/components/modals/EditDataModal.tsx",
                                            lineNumber: 680,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                        lineNumber: 679,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "text-lg font-semibold text-n700 dark:text-n20",
                                        children: "Confirm Deletion"
                                    }, void 0, false, {
                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                        lineNumber: 682,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/modals/EditDataModal.tsx",
                                lineNumber: 678,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-n600 dark:text-n30 mb-6 leading-relaxed",
                                children: [
                                    "Are you sure you want to delete ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-semibold text-errorColor",
                                        children: selectedRows.size
                                    }, void 0, false, {
                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                        lineNumber: 687,
                                        columnNumber: 49
                                    }, this),
                                    " selected row",
                                    selectedRows.size !== 1 ? 's' : '',
                                    "?",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                        lineNumber: 688,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm text-n500 dark:text-n400",
                                        children: "This action cannot be undone."
                                    }, void 0, false, {
                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                        lineNumber: 689,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/modals/EditDataModal.tsx",
                                lineNumber: 686,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-end space-x-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setShowDeleteConfirm(false),
                                        disabled: isDeleting,
                                        className: "px-6 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n700 rounded-lg hover:bg-gray-300 dark:hover:bg-n600 disabled:opacity-50 transition-all duration-200",
                                        children: "Cancel"
                                    }, void 0, false, {
                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                        lineNumber: 692,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handleDeleteSelected,
                                        disabled: isDeleting,
                                        className: "px-6 py-2 text-sm font-medium text-white bg-errorColor hover:bg-errorColor/90 rounded-lg disabled:opacity-50 flex items-center transition-all duration-200 shadow-sm hover:shadow-md",
                                        children: isDeleting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSpinner"], {
                                                    className: "animate-spin mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 706,
                                                    columnNumber: 23
                                                }, this),
                                                "Deleting..."
                                            ]
                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiTrash"], {
                                                    className: "mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/modals/EditDataModal.tsx",
                                                    lineNumber: 711,
                                                    columnNumber: 23
                                                }, this),
                                                "Delete"
                                            ]
                                        }, void 0, true)
                                    }, void 0, false, {
                                        fileName: "[project]/components/modals/EditDataModal.tsx",
                                        lineNumber: 699,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/modals/EditDataModal.tsx",
                                lineNumber: 691,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/modals/EditDataModal.tsx",
                        lineNumber: 677,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/modals/EditDataModal.tsx",
                    lineNumber: 676,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/modals/EditDataModal.tsx",
            lineNumber: 285,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/modals/EditDataModal.tsx",
        lineNumber: 284,
        columnNumber: 5
    }, this);
};
_s(EditDataModal, "IWWVSpV3CwtyDGQD0Q6zlbQEVKM=");
_c = EditDataModal;
const __TURBOPACK__default__export__ = EditDataModal;
var _c;
__turbopack_refresh__.register(_c, "EditDataModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/showdeleteindex.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$adminsidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/adminsidebar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/services/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/services/fileUploadService.ts [app-client] (ecmascript)");
// @ts-ignore
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$EditDataModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/modals/EditDataModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
;
;
;
;
const DeleteConfirmationModal = ({ isOpen, onClose, onConfirm, itemData, selectedItems = [], isDeleting, isBulkDelete = false, bulkProgress = {} })=>{
    if (!isOpen || !itemData && !isBulkDelete) return null;
    const isMultiple = isBulkDelete && selectedItems.length > 0;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white dark:bg-n800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center mb-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiWarning"], {
                            className: "text-red-500 text-2xl mr-3"
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 80,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg font-semibold text-n700 dark:text-n20",
                            children: isMultiple ? `Confirm Bulk Deletion (${selectedItems.length} items)` : 'Confirm Deletion'
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 81,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/showdeleteindex.tsx",
                    lineNumber: 79,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-n600 dark:text-n30 mb-4",
                            children: [
                                "Are you sure you want to delete ",
                                isMultiple ? 'these indexes' : 'this index',
                                "? This action will:"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 87,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            className: "list-disc list-inside text-sm text-n500 dark:text-n40 space-y-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: [
                                        "Remove the record",
                                        isMultiple ? 's' : '',
                                        " from PINE collection database"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 91,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: [
                                        "Delete the entire FAISS index director",
                                        isMultiple ? 'ies' : 'y',
                                        " and all files"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 92,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "Permanently remove all vector embeddings and metadata"
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 93,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "This action cannot be undone"
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 94,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 90,
                            columnNumber: 11
                        }, this),
                        !isMultiple && itemData && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-4 p-3 bg-gray-50 dark:bg-n700 rounded-md",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm font-medium text-n700 dark:text-n20",
                                    children: [
                                        "Category: ",
                                        itemData.index_name
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 100,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-n500 dark:text-n40",
                                    children: [
                                        "Email: ",
                                        itemData.email
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 103,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-n500 dark:text-n40",
                                    children: [
                                        "Model: ",
                                        itemData.embed_model || 'N/A'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 106,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 99,
                            columnNumber: 13
                        }, this),
                        isMultiple && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-4 space-y-2 max-h-60 overflow-y-auto",
                            children: selectedItems.map((item)=>{
                                const progress = bulkProgress[item._id];
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-3 bg-gray-50 dark:bg-n700 rounded-md",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm font-medium text-n700 dark:text-n20",
                                                            children: item.index_name
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 121,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-n500 dark:text-n40",
                                                            children: item.email
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 124,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                    lineNumber: 120,
                                                    columnNumber: 23
                                                }, this),
                                                progress && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center ml-2",
                                                    children: [
                                                        progress.status === 'pending' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-xs text-gray-500",
                                                            children: "Pending"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 131,
                                                            columnNumber: 29
                                                        }, this),
                                                        progress.status === 'deleting' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 135,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-xs text-blue-600",
                                                                    children: "Deleting..."
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 136,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 134,
                                                            columnNumber: 29
                                                        }, this),
                                                        progress.status === 'success' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCheck"], {
                                                                    className: "text-green-500 text-sm mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 141,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-xs text-green-600",
                                                                    children: "Success"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 142,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 140,
                                                            columnNumber: 29
                                                        }, this),
                                                        progress.status === 'error' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {
                                                                    className: "text-red-500 text-sm mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 147,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-xs text-red-600",
                                                                    children: "Error"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 148,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 146,
                                                            columnNumber: 29
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                    lineNumber: 129,
                                                    columnNumber: 25
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 119,
                                            columnNumber: 21
                                        }, this),
                                        progress?.message && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-n500 dark:text-n40 mt-1",
                                            children: progress.message
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 155,
                                            columnNumber: 23
                                        }, this)
                                    ]
                                }, item._id, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 118,
                                    columnNumber: 19
                                }, this);
                            })
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 114,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/showdeleteindex.tsx",
                    lineNumber: 86,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-end space-x-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onClose,
                            disabled: isDeleting,
                            className: "px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n700 rounded-md hover:bg-gray-300 dark:hover:bg-n600 disabled:opacity-50 disabled:cursor-not-allowed",
                            children: isDeleting ? 'Close' : 'Cancel'
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 167,
                            columnNumber: 11
                        }, this),
                        !isDeleting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onConfirm,
                            className: "px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiTrash"], {
                                    className: "mr-2"
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 179,
                                    columnNumber: 15
                                }, this),
                                "Delete ",
                                isMultiple ? `${selectedItems.length} Items` : 'Item'
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 175,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/showdeleteindex.tsx",
                    lineNumber: 166,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/showdeleteindex.tsx",
            lineNumber: 78,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/showdeleteindex.tsx",
        lineNumber: 77,
        columnNumber: 5
    }, this);
};
_c = DeleteConfirmationModal;
// Logout Confirmation Modal Component
const LogoutConfirmationModal = ({ isOpen, onClose, onConfirm, isProcessing, userName })=>{
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white dark:bg-n800 rounded-lg p-6 max-w-md w-full mx-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center mb-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSignOut"], {
                            className: "text-red-500 text-2xl mr-3"
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 203,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg font-semibold text-n700 dark:text-n20",
                            children: "Confirm Logout"
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 204,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/showdeleteindex.tsx",
                    lineNumber: 202,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-n600 dark:text-n30 mb-2",
                            children: [
                                "Are you sure you want to log out, ",
                                userName,
                                "?"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 210,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-n500 dark:text-n40",
                            children: "You will be redirected to the sign-in page and will need to log in again to access your data."
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 213,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/showdeleteindex.tsx",
                    lineNumber: 209,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-end space-x-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onClose,
                            disabled: isProcessing,
                            className: "px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n700 rounded-md hover:bg-gray-300 dark:hover:bg-n600 disabled:opacity-50 disabled:cursor-not-allowed",
                            children: "Cancel"
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 219,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onConfirm,
                            disabled: isProcessing,
                            className: "px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md flex items-center disabled:opacity-50 disabled:cursor-not-allowed",
                            children: isProcessing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/components/showdeleteindex.tsx",
                                        lineNumber: 233,
                                        columnNumber: 17
                                    }, this),
                                    "Logging out..."
                                ]
                            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSignOut"], {
                                        className: "mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/components/showdeleteindex.tsx",
                                        lineNumber: 238,
                                        columnNumber: 17
                                    }, this),
                                    "Log Out"
                                ]
                            }, void 0, true)
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 226,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/showdeleteindex.tsx",
                    lineNumber: 218,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/showdeleteindex.tsx",
            lineNumber: 201,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/showdeleteindex.tsx",
        lineNumber: 200,
        columnNumber: 5
    }, this);
};
_c1 = LogoutConfirmationModal;
const ShowDeleteIndex = ()=>{
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [indexData, setIndexData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [deleteModal, setDeleteModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isOpen: false,
        item: null
    });
    const [deleteLoading, setDeleteLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [sidebarOpen, setSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [emails, setEmails] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectedEmail, setSelectedEmail] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    // User authentication state
    const [currentUser, setCurrentUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showLogoutConfirm, setShowLogoutConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLogoutProcessing, setIsLogoutProcessing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Multi-select state
    const [selectedItems, setSelectedItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Set());
    const [isMultiSelectMode, setIsMultiSelectMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [bulkDeleteModal, setBulkDeleteModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isOpen: false,
        items: []
    });
    const [bulkDeleteProgress, setBulkDeleteProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isBulkDeleting, setIsBulkDeleting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Edit Data Modal state
    const [editDataModal, setEditDataModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isOpen: false,
        indexData: null
    });
    // Notification state
    const [notification, setNotification] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        type: 'info',
        message: '',
        isVisible: false
    });
    // Notification helper function
    const showNotification = (type, message)=>{
        setNotification({
            type,
            message,
            isVisible: true
        });
        setTimeout(()=>{
            setNotification((prev)=>({
                    ...prev,
                    isVisible: false
                }));
        }, 5000);
    };
    // Initialize user data from localStorage
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ShowDeleteIndex.useEffect": ()=>{
            if ("TURBOPACK compile-time truthy", 1) {
                const userEmail = localStorage.getItem('user_email');
                const sessionData = sessionStorage.getItem('resultUser');
                if (userEmail) {
                    let userName = 'User';
                    // Try to get user name from session storage
                    if (sessionData) {
                        try {
                            const userData = JSON.parse(sessionData);
                            userName = userData.name || 'User';
                        } catch (error) {
                            console.error('Error parsing user session data:', error);
                        }
                    }
                    setCurrentUser({
                        name: userName,
                        email: userEmail
                    });
                }
            }
        }
    }["ShowDeleteIndex.useEffect"], []);
    // Handle logout functionality
    const handleLogout = ()=>{
        if (isLogoutProcessing) {
            console.log("Logout already in progress, ignoring...");
            return;
        }
        setIsLogoutProcessing(true);
        console.log("Logout initiated from Index Management");
        try {
            // Clear all storage
            console.log("Clearing all storage...");
            sessionStorage.clear();
            localStorage.clear();
            // Close any open modals
            setShowLogoutConfirm(false);
            // Show logout notification
            showNotification('info', 'Logging out...');
            // Force redirect to sign-in page
            console.log("Redirecting to sign-in page...");
            setTimeout(()=>{
                window.location.href = "/sign-in";
            }, 100);
        } catch (error) {
            console.error("Error during logout:", error);
            // Even if there's an error, try to redirect
            setTimeout(()=>{
                window.location.href = "/sign-in";
            }, 100);
        }
    };
    // Handle logout confirmation
    const handleLogoutClick = ()=>{
        setShowLogoutConfirm(true);
    };
    // Filter data based on search term
    const filteredData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ShowDeleteIndex.useMemo[filteredData]": ()=>{
            if (!searchTerm.trim()) {
                return indexData;
            }
            const searchLower = searchTerm.toLowerCase().trim();
            return indexData.filter({
                "ShowDeleteIndex.useMemo[filteredData]": (item)=>{
                    return item.email.toLowerCase().includes(searchLower) || item.index_name.toLowerCase().includes(searchLower) || item.embed_model && item.embed_model.toLowerCase().includes(searchLower);
                }
            }["ShowDeleteIndex.useMemo[filteredData]"]);
        }
    }["ShowDeleteIndex.useMemo[filteredData]"], [
        indexData,
        searchTerm
    ]);
    // Clear search
    const clearSearch = ()=>{
        setSearchTerm("");
        showNotification('info', 'Search cleared');
    };
    // Fetch emails and index data
    const fetchIndexData = async ()=>{
        try {
            setLoading(true);
            setError(null);
            // First, fetch all emails
            const emailList = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchEmails"])();
            setEmails(emailList);
            let allIndexData = [];
            // If a specific email is selected, fetch indexes for that email
            if (selectedEmail) {
                console.log(`Fetching indexes for selected email: ${selectedEmail}`);
                const emailIndexes = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getIndexesByEmail"])(selectedEmail);
                allIndexData = [
                    ...allIndexData,
                    ...emailIndexes
                ];
            } else {
                // If no email is selected, fetch indexes for all emails
                console.log('Fetching indexes for all emails...');
                for (const email of emailList){
                    try {
                        const emailIndexes = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$fileUploadService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getIndexesByEmail"])(email);
                        allIndexData = [
                            ...allIndexData,
                            ...emailIndexes
                        ];
                    } catch (error) {
                        console.error(`Error fetching indexes for email ${email}:`, error);
                    // Continue with other emails even if one fails
                    }
                }
            }
            console.log('All fetched index data:', allIndexData);
            setIndexData(allIndexData);
            // Show success notification if data was fetched successfully
            if (allIndexData.length > 0) {
                showNotification('success', `Successfully loaded ${allIndexData.length} indexes`);
            } else {
                showNotification('info', 'No indexes found');
            }
        } catch (err) {
            console.error('Error fetching index data:', err);
            const errorMessage = err instanceof Error ? err.message : 'Failed to fetch data';
            setError(errorMessage);
            showNotification('error', `Failed to load data: ${errorMessage}`);
        } finally{
            setLoading(false);
        }
    };
    // Delete function - deletes both from PINE collection and FAISS directory
    const handleDelete = async (item)=>{
        try {
            setDeleteLoading(item._id);
            // Use the original _id format if it exists, otherwise use the processed _id
            const resourceId = item._id?.includes('$oid') ? item._id : item._id;
            console.log('Deleting item with resourceId:', resourceId);
            console.log('Full item data:', item);
            let pineDeleteSuccess = false;
            let faissDirectoryDeleteSuccess = false;
            let pineError = null;
            let faissDirectoryError = null;
            // Step 1: Delete from PINE collection
            try {
                console.log('Step 1: Deleting from PINE collection...');
                const pineResponse = await fetch(`https://dev-commonmannit.mannit.co/mannit/eDeleteWCol?resourceId=${resourceId}&ColName=PINE`, {
                    method: 'DELETE',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'xxxid': 'PINE'
                    }
                });
                if (pineResponse.ok) {
                    console.log('PINE collection entry deleted successfully');
                    pineDeleteSuccess = true;
                } else {
                    const errorText = await pineResponse.text();
                    console.error('PINE collection delete API error:', errorText);
                    pineError = `PINE collection deletion failed: ${pineResponse.status} - ${errorText}`;
                }
            } catch (err) {
                console.error('Error deleting from PINE collection:', err);
                pineError = `PINE collection deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;
            }
            // Step 2: Delete FAISS directory (only if we have an index_name)
            if (item.index_name && item.index_name !== 'N/A') {
                try {
                    console.log(`Step 2: Deleting FAISS directory for index: ${item.index_name}...`);
                    const faissResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteFaissIndex"])(item.index_name);
                    console.log('FAISS directory delete response:', faissResult);
                    if (faissResult.success) {
                        faissDirectoryDeleteSuccess = true;
                    } else {
                        faissDirectoryError = `FAISS directory deletion failed: ${faissResult.error}`;
                    }
                } catch (err) {
                    console.error('Error deleting FAISS directory:', err);
                    faissDirectoryError = `FAISS directory deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;
                }
            } else {
                console.log('Step 2: Skipping FAISS directory deletion - no valid index name');
                faissDirectoryDeleteSuccess = true; // Consider it successful if there's nothing to delete
            }
            // Determine overall success and handle results
            const overallSuccess = pineDeleteSuccess && faissDirectoryDeleteSuccess;
            if (overallSuccess) {
                console.log('✅ Successfully deleted both PINE collection entry and FAISS directory');
                // Remove item from local state
                setIndexData((prevData)=>prevData.filter((dataItem)=>dataItem._id !== item._id));
                // Close modal
                setDeleteModal({
                    isOpen: false,
                    item: null
                });
                // Show success notification
                showNotification('success', `Successfully deleted index "${item.index_name}"`);
            } else {
                // Partial or complete failure
                let errorMessage = 'Deletion completed with issues:\n';
                if (pineDeleteSuccess) {
                    errorMessage += '✅ PINE collection entry deleted successfully\n';
                } else {
                    errorMessage += `❌ PINE collection deletion failed: ${pineError}\n`;
                }
                if (faissDirectoryDeleteSuccess) {
                    errorMessage += '✅ FAISS directory deleted successfully';
                } else {
                    errorMessage += `❌ FAISS directory deletion failed: ${faissDirectoryError}`;
                }
                console.error('Partial deletion result:', errorMessage);
                // If PINE collection was deleted successfully, still remove from UI
                if (pineDeleteSuccess) {
                    setIndexData((prevData)=>prevData.filter((dataItem)=>dataItem._id !== item._id));
                    setDeleteModal({
                        isOpen: false,
                        item: null
                    });
                }
                // Show error to user
                setError(errorMessage);
                showNotification('error', 'Deletion completed with some issues. Check the details above.');
            }
        } catch (err) {
            console.error('Error in delete process:', err);
            setError(err instanceof Error ? err.message : 'Failed to delete item');
        } finally{
            setDeleteLoading(null);
        }
    };
    // Load data on component mount and when selectedEmail changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ShowDeleteIndex.useEffect": ()=>{
            fetchIndexData();
        }
    }["ShowDeleteIndex.useEffect"], [
        selectedEmail
    ]);
    // Clear selections when data changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ShowDeleteIndex.useEffect": ()=>{
            setSelectedItems(new Set());
        }
    }["ShowDeleteIndex.useEffect"], [
        indexData
    ]);
    // Listen for FAISS index updates
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ShowDeleteIndex.useEffect": ()=>{
            const handleFaissIndexUpdate = {
                "ShowDeleteIndex.useEffect.handleFaissIndexUpdate": ()=>{
                    console.log('FAISS index updated, refreshing data...');
                    fetchIndexData();
                }
            }["ShowDeleteIndex.useEffect.handleFaissIndexUpdate"];
            window.addEventListener('faissIndexUpdated', handleFaissIndexUpdate);
            return ({
                "ShowDeleteIndex.useEffect": ()=>{
                    window.removeEventListener('faissIndexUpdated', handleFaissIndexUpdate);
                }
            })["ShowDeleteIndex.useEffect"];
        }
    }["ShowDeleteIndex.useEffect"], []);
    const openDeleteModal = (item)=>{
        setDeleteModal({
            isOpen: true,
            item
        });
    };
    const closeDeleteModal = ()=>{
        setDeleteModal({
            isOpen: false,
            item: null
        });
    };
    const confirmDelete = ()=>{
        if (deleteModal.item) {
            handleDelete(deleteModal.item);
        }
    };
    const toggleSidebar = ()=>{
        setSidebarOpen(!sidebarOpen);
    };
    // Edit Data Modal functions
    const openEditDataModal = (item)=>{
        setEditDataModal({
            isOpen: true,
            indexData: item
        });
        showNotification('info', `Loading data for index "${item.index_name}"`);
    };
    const closeEditDataModal = ()=>{
        setEditDataModal({
            isOpen: false,
            indexData: null
        });
    };
    // Multi-select functions
    const toggleMultiSelectMode = ()=>{
        const newMode = !isMultiSelectMode;
        setIsMultiSelectMode(newMode);
        setSelectedItems(new Set());
        if (newMode) {
            showNotification('info', 'Multi-select mode enabled. Click checkboxes to select items for bulk operations.');
        } else {
            showNotification('info', 'Multi-select mode disabled.');
        }
    };
    const toggleItemSelection = (itemId)=>{
        const newSelected = new Set(selectedItems);
        if (newSelected.has(itemId)) {
            newSelected.delete(itemId);
        } else {
            newSelected.add(itemId);
        }
        setSelectedItems(newSelected);
    };
    const selectAllItems = ()=>{
        const allIds = new Set(filteredData.map((item)=>item._id));
        setSelectedItems(allIds);
        showNotification('info', `Selected all ${filteredData.length} visible items`);
    };
    const deselectAllItems = ()=>{
        setSelectedItems(new Set());
        showNotification('info', 'Deselected all items');
    };
    const openBulkDeleteModal = ()=>{
        const itemsToDelete = filteredData.filter((item)=>selectedItems.has(item._id));
        setBulkDeleteModal({
            isOpen: true,
            items: itemsToDelete
        });
    };
    const closeBulkDeleteModal = ()=>{
        setBulkDeleteModal({
            isOpen: false,
            items: []
        });
        setBulkDeleteProgress({});
    };
    // Bulk delete function
    const handleBulkDelete = async ()=>{
        if (bulkDeleteModal.items.length === 0) return;
        setIsBulkDeleting(true);
        // Initialize progress for all items
        const initialProgress = {};
        bulkDeleteModal.items.forEach((item)=>{
            initialProgress[item._id] = {
                status: 'pending'
            };
        });
        setBulkDeleteProgress(initialProgress);
        const deletedItems = [];
        // Process each item sequentially to avoid overwhelming the server
        for (const item of bulkDeleteModal.items){
            try {
                // Update status to deleting
                setBulkDeleteProgress((prev)=>({
                        ...prev,
                        [item._id]: {
                            status: 'deleting',
                            message: 'Deleting...'
                        }
                    }));
                // Use the same delete logic as single item delete
                const resourceId = item._id?.includes('$oid') ? item._id : item._id;
                let pineDeleteSuccess = false;
                let faissDirectoryDeleteSuccess = false;
                let pineError = null;
                let faissDirectoryError = null;
                // Step 1: Delete from PINE collection
                try {
                    const pineResponse = await fetch(`https://dev-commonmannit.mannit.co/mannit/eDeleteWCol?resourceId=${resourceId}&ColName=PINE`, {
                        method: 'DELETE',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                            'xxxid': 'PINE'
                        }
                    });
                    if (pineResponse.ok) {
                        pineDeleteSuccess = true;
                    } else {
                        const errorText = await pineResponse.text();
                        pineError = `PINE deletion failed: ${pineResponse.status} - ${errorText}`;
                    }
                } catch (err) {
                    pineError = `PINE deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;
                }
                // Step 2: Delete FAISS directory
                if (item.index_name && item.index_name !== 'N/A') {
                    try {
                        const faissResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteFaissIndex"])(item.index_name);
                        if (faissResult.success) {
                            faissDirectoryDeleteSuccess = true;
                        } else {
                            faissDirectoryError = `FAISS deletion failed: ${faissResult.error}`;
                        }
                    } catch (err) {
                        faissDirectoryError = `FAISS deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;
                    }
                } else {
                    faissDirectoryDeleteSuccess = true;
                }
                // Update progress based on results
                const overallSuccess = pineDeleteSuccess && faissDirectoryDeleteSuccess;
                if (overallSuccess) {
                    setBulkDeleteProgress((prev)=>({
                            ...prev,
                            [item._id]: {
                                status: 'success',
                                message: 'Successfully deleted'
                            }
                        }));
                    deletedItems.push(item._id);
                } else {
                    let errorMessage = '';
                    if (!pineDeleteSuccess) errorMessage += pineError + ' ';
                    if (!faissDirectoryDeleteSuccess) errorMessage += faissDirectoryError;
                    setBulkDeleteProgress((prev)=>({
                            ...prev,
                            [item._id]: {
                                status: 'error',
                                message: errorMessage.trim()
                            }
                        }));
                }
            } catch (err) {
                setBulkDeleteProgress((prev)=>({
                        ...prev,
                        [item._id]: {
                            status: 'error',
                            message: err instanceof Error ? err.message : 'Unknown error'
                        }
                    }));
            }
        }
        // Remove successfully deleted items from the UI
        if (deletedItems.length > 0) {
            setIndexData((prevData)=>prevData.filter((item)=>!deletedItems.includes(item._id)));
            setSelectedItems(new Set());
            showNotification('success', `Successfully deleted ${deletedItems.length} of ${bulkDeleteModal.items.length} selected indexes`);
        }
        setIsBulkDeleting(false);
        // Show error notification if some items failed
        const failedCount = bulkDeleteModal.items.length - deletedItems.length;
        if (failedCount > 0) {
            showNotification('error', `${failedCount} items failed to delete. Check the details in the modal.`);
        }
        // Auto-close modal after a delay if all items were successful
        const allSuccessful = bulkDeleteModal.items.every((item)=>deletedItems.includes(item._id));
        if (allSuccessful) {
            setTimeout(()=>{
                closeBulkDeleteModal();
            }, 2000);
        }
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex h-screen bg-gray-50 dark:bg-gray-900",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$adminsidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    currentView: "show",
                    isOpen: sidebarOpen,
                    onToggle: toggleSidebar
                }, void 0, false, {
                    fileName: "[project]/components/showdeleteindex.tsx",
                    lineNumber: 789,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: toggleSidebar,
                                className: "text-gray-600 dark:text-gray-300 hover:text-primaryColor",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiList"], {
                                    className: "text-xl"
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 801,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/showdeleteindex.tsx",
                                lineNumber: 797,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 796,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "container mx-auto px-4 py-8",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "animate-spin rounded-full h-12 w-12 border-b-2 border-primaryColor mx-auto mb-4"
                                    }, void 0, false, {
                                        fileName: "[project]/components/showdeleteindex.tsx",
                                        lineNumber: 806,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-n600 dark:text-n30",
                                        children: "Loading index data..."
                                    }, void 0, false, {
                                        fileName: "[project]/components/showdeleteindex.tsx",
                                        lineNumber: 807,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/showdeleteindex.tsx",
                                lineNumber: 805,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 804,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/showdeleteindex.tsx",
                    lineNumber: 794,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/showdeleteindex.tsx",
            lineNumber: 788,
            columnNumber: 7
        }, this);
    }
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex h-screen bg-gray-50 dark:bg-gray-900",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$adminsidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    currentView: "show",
                    isOpen: sidebarOpen,
                    onToggle: toggleSidebar
                }, void 0, false, {
                    fileName: "[project]/components/showdeleteindex.tsx",
                    lineNumber: 818,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: toggleSidebar,
                                className: "text-gray-600 dark:text-gray-300 hover:text-primaryColor",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiList"], {
                                    className: "text-xl"
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 830,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/showdeleteindex.tsx",
                                lineNumber: 826,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 825,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "container mx-auto px-4 py-8",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiWarning"], {
                                            className: "text-red-500 text-3xl mx-auto mb-4"
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 836,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-lg font-semibold text-red-700 dark:text-red-400 mb-2",
                                            children: "Error Loading Data"
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 837,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-red-600 dark:text-red-300 mb-4",
                                            children: error
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 840,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: fetchIndexData,
                                            className: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",
                                            children: "Try Again"
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 841,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 835,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/showdeleteindex.tsx",
                                lineNumber: 834,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 833,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/showdeleteindex.tsx",
                    lineNumber: 823,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/showdeleteindex.tsx",
            lineNumber: 817,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex h-screen bg-gray-50 dark:bg-gray-900",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$adminsidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                currentView: "show",
                isOpen: sidebarOpen,
                onToggle: toggleSidebar
            }, void 0, false, {
                fileName: "[project]/components/showdeleteindex.tsx",
                lineNumber: 857,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: toggleSidebar,
                            className: "text-gray-600 dark:text-gray-300 hover:text-primaryColor",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiList"], {
                                className: "text-xl"
                            }, void 0, false, {
                                fileName: "[project]/components/showdeleteindex.tsx",
                                lineNumber: 869,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 865,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/showdeleteindex.tsx",
                        lineNumber: 864,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 overflow-auto",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "container mx-auto px-4 py-8",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-8",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-center lg:text-left mb-4 lg:mb-0",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                            className: "text-3xl font-bold text-n700 dark:text-n20 mb-2",
                                                            children: "Index Management"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 879,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-n600 dark:text-n30",
                                                            children: "Manage your FAISS indexes stored in PINE collection."
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 882,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                    lineNumber: 878,
                                                    columnNumber: 17
                                                }, this),
                                                currentUser && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center justify-center lg:justify-end space-x-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center space-x-2 text-sm text-n600 dark:text-n30",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiUserCircle"], {
                                                                    className: "text-lg"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 891,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-right",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "font-medium text-n700 dark:text-n20",
                                                                            children: currentUser.name
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                                            lineNumber: 893,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-xs text-n500 dark:text-n40",
                                                                            children: currentUser.email
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                                            lineNumber: 894,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 892,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 890,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: handleLogoutClick,
                                                            className: "flex items-center space-x-2 px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 bg-red-50 hover:bg-red-100 dark:bg-red-900/20 dark:hover:bg-red-900/30 rounded-lg transition-colors duration-200",
                                                            title: "Sign out",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSignOut"], {
                                                                    className: "text-lg"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 902,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "hidden sm:inline",
                                                                    children: "Sign Out"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 903,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 897,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                    lineNumber: 889,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 877,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-n600 dark:text-n30 max-w-2xl mx-auto text-sm",
                                                children: "Deleting an index will remove both the PINE collection entry and the entire FAISS directory with all associated files."
                                            }, void 0, false, {
                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                lineNumber: 911,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 910,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 875,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-6",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "max-w-md mx-auto",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: "email-filter",
                                                className: "block text-sm font-medium text-n700 dark:text-n20 mb-2",
                                                children: "Filter by Email:"
                                            }, void 0, false, {
                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                lineNumber: 920,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                id: "email-filter",
                                                value: selectedEmail,
                                                onChange: (e)=>setSelectedEmail(e.target.value),
                                                className: "w-full p-3 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: "",
                                                        children: "-- All Emails --"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 929,
                                                        columnNumber: 19
                                                    }, this),
                                                    emails.map((email, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                            value: email,
                                                            children: email
                                                        }, index, false, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 931,
                                                            columnNumber: 21
                                                        }, this))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                lineNumber: 923,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/showdeleteindex.tsx",
                                        lineNumber: 919,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 918,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-6",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "max-w-md mx-auto relative",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "relative",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiMagnifyingGlass"], {
                                                        className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-n400 dark:text-n500"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 943,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "text",
                                                        placeholder: "Search by email, index name, or embedding model...",
                                                        value: searchTerm,
                                                        onChange: (e)=>setSearchTerm(e.target.value),
                                                        className: "w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 placeholder-n400 dark:placeholder-n500"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 944,
                                                        columnNumber: 19
                                                    }, this),
                                                    searchTerm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: clearSearch,
                                                        className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-n400 dark:text-n500 hover:text-n600 dark:hover:text-n300 transition-colors",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {}, void 0, false, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 956,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 952,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                lineNumber: 942,
                                                columnNumber: 17
                                            }, this),
                                            searchTerm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-2 text-sm text-n500 dark:text-n40 text-center",
                                                children: [
                                                    "Found ",
                                                    filteredData.length,
                                                    " result",
                                                    filteredData.length !== 1 ? 's' : '',
                                                    ' for "',
                                                    searchTerm,
                                                    '"'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                lineNumber: 961,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/showdeleteindex.tsx",
                                        lineNumber: 941,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 940,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: toggleMultiSelectMode,
                                                    className: `px-4 py-2 rounded-md transition-all duration-200 flex items-center font-medium ${isMultiSelectMode ? 'bg-blue-500 text-white hover:bg-blue-600 shadow-md' : 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-800/30 dark:hover:to-indigo-800/30 hover:shadow-sm'}`,
                                                    children: isMultiSelectMode ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {
                                                                className: "mr-2 w-4 h-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 982,
                                                                columnNumber: 23
                                                            }, this),
                                                            "Exit Multi-Select"
                                                        ]
                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCursor"], {
                                                                className: "mr-2 w-4 h-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 987,
                                                                columnNumber: 23
                                                            }, this),
                                                            "Enable Multi-Select"
                                                        ]
                                                    }, void 0, true)
                                                }, void 0, false, {
                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                    lineNumber: 972,
                                                    columnNumber: 17
                                                }, this),
                                                isMultiSelectMode && filteredData.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center gap-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    onClick: selectedItems.size === filteredData.length ? deselectAllItems : selectAllItems,
                                                                    className: "text-sm px-3 py-1 rounded-md bg-primaryColor/10 text-primaryColor hover:bg-primaryColor/20 transition-colors flex items-center gap-1 font-medium",
                                                                    children: selectedItems.size === filteredData.length ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSquare"], {
                                                                                className: "w-3 h-3"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                                lineNumber: 1002,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            "Deselect All"
                                                                        ]
                                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiHandPointing"], {
                                                                                className: "w-3 h-3"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                                lineNumber: 1007,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            "Select All"
                                                                        ]
                                                                    }, void 0, true)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 996,
                                                                    columnNumber: 23
                                                                }, this),
                                                                selectedItems.size > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-sm text-n500 dark:text-n40",
                                                                    children: [
                                                                        "(",
                                                                        selectedItems.size,
                                                                        " selected)"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 1013,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 995,
                                                            columnNumber: 21
                                                        }, this),
                                                        selectedItems.size > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: openBulkDeleteModal,
                                                            className: "px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiTrash"], {
                                                                    className: "mr-2"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                                    lineNumber: 1024,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Delete Selected (",
                                                                selectedItems.size,
                                                                ")"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 1020,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 971,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex gap-2",
                                            children: [
                                                indexData.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>{
                                                        const dataToExport = filteredData.map((item)=>({
                                                                email: item.email,
                                                                index_name: item.index_name,
                                                                embed_model: item.embed_model || 'N/A',
                                                                source: item.source
                                                            }));
                                                        const csvContent = [
                                                            'Email,Index Name,Embedding Model,Source',
                                                            ...dataToExport.map((item)=>`"${item.email}","${item.index_name}","${item.embed_model}","${item.source}"`)
                                                        ].join('\n');
                                                        const blob = new Blob([
                                                            csvContent
                                                        ], {
                                                            type: 'text/csv'
                                                        });
                                                        const url = URL.createObjectURL(blob);
                                                        const link = document.createElement('a');
                                                        link.href = url;
                                                        link.download = `indexes_export_${new Date().toISOString().split('T')[0]}.csv`;
                                                        link.click();
                                                        URL.revokeObjectURL(url);
                                                        showNotification('success', `Exported ${dataToExport.length} indexes to CSV`);
                                                    },
                                                    className: "px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiDownloadSimple"], {
                                                            className: "mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                            lineNumber: 1063,
                                                            columnNumber: 21
                                                        }, this),
                                                        "Export CSV"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                    lineNumber: 1035,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: fetchIndexData,
                                                    disabled: loading,
                                                    className: "px-4 py-2 bg-primaryColor text-white rounded-md hover:bg-primaryColor/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center",
                                                    children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 1075,
                                                                columnNumber: 23
                                                            }, this),
                                                            "Loading..."
                                                        ]
                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiArrowsClockwise"], {
                                                                className: "mr-2"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 1080,
                                                                columnNumber: 23
                                                            }, this),
                                                            "Refresh Data"
                                                        ]
                                                    }, void 0, true)
                                                }, void 0, false, {
                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                    lineNumber: 1068,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 1033,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 969,
                                    columnNumber: 13
                                }, this),
                                indexData.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center py-12",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiDatabase"], {
                                            className: "text-6xl text-n300 dark:text-n600 mx-auto mb-4"
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 1091,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-xl font-semibold text-n600 dark:text-n30 mb-2",
                                            children: "No Data Found"
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 1092,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-n500 dark:text-n40",
                                            children: "No indexes found in the collections."
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 1095,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 1090,
                                    columnNumber: 15
                                }, this) : filteredData.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center py-12",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiMagnifyingGlass"], {
                                            className: "text-6xl text-n300 dark:text-n600 mx-auto mb-4"
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 1101,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-xl font-semibold text-n600 dark:text-n30 mb-2",
                                            children: "No Results Found"
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 1102,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-n500 dark:text-n40 mb-4",
                                            children: [
                                                'No entries match your search term "',
                                                searchTerm,
                                                '".'
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 1105,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: clearSearch,
                                            className: "px-4 py-2 bg-primaryColor text-white rounded-md hover:bg-primaryColor/90 transition-colors",
                                            children: "Clear Search"
                                        }, void 0, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 1108,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 1100,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
                                    children: filteredData.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `border rounded-lg overflow-hidden hover:shadow-md transition-all duration-300 bg-white dark:bg-n800 ${isMultiSelectMode && selectedItems.has(item._id) ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800' : 'border-primaryColor/20 hover:border-primaryColor/50'}`,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "p-6",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between mb-4",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center",
                                                                children: [
                                                                    isMultiSelectMode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                        onClick: ()=>toggleItemSelection(item._id),
                                                                        className: "mr-3 p-1 rounded hover:bg-gray-100 dark:hover:bg-n700 transition-colors",
                                                                        children: selectedItems.has(item._id) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCheckSquare"], {
                                                                            className: "text-blue-500 text-lg"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                                            lineNumber: 1136,
                                                                            columnNumber: 33
                                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiSquare"], {
                                                                            className: "text-n400 dark:text-n500 text-lg"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                                            lineNumber: 1138,
                                                                            columnNumber: 33
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1131,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiDatabase"], {
                                                                        className: "text-primaryColor text-xl mr-2"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1142,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                        className: "font-semibold text-n700 dark:text-n20",
                                                                        children: "Index Entry"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1143,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 1129,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex gap-2",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-xs px-2 py-1 rounded-full bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 flex items-center gap-1",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiShieldCheck"], {
                                                                                className: "w-3 h-3"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                                lineNumber: 1149,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            "PINE"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1148,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-xs bg-primaryColor/10 text-primaryColor px-2 py-1 rounded-full flex items-center gap-1",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCheckCircle"], {
                                                                                className: "w-3 h-3"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                                lineNumber: 1153,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            "Active"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1152,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 1147,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 1128,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-3 mb-6",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-start",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiEnvelope"], {
                                                                        className: "text-green-500 dark:text-green-400 mt-1 mr-3 flex-shrink-0"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1162,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "text-xs text-n500 dark:text-n40 uppercase tracking-wide",
                                                                                children: "Email"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                                lineNumber: 1164,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "text-sm font-medium text-n700 dark:text-n20 break-all",
                                                                                children: item.email
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                                lineNumber: 1167,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1163,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 1161,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-start",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiTag"], {
                                                                        className: "text-blue-500 dark:text-blue-400 mt-1 mr-3 flex-shrink-0"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1174,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "text-xs text-n500 dark:text-n40 uppercase tracking-wide",
                                                                                children: "Index Name"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                                lineNumber: 1176,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "text-sm font-medium text-n700 dark:text-n20 break-all",
                                                                                children: item.index_name
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                                lineNumber: 1179,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1175,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 1173,
                                                                columnNumber: 25
                                                            }, this),
                                                            item.embed_model && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-start",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiInfo"], {
                                                                        className: "text-purple-500 dark:text-purple-400 mt-1 mr-3 flex-shrink-0"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1187,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "text-xs text-n500 dark:text-n40 uppercase tracking-wide",
                                                                                children: "Embedding Model"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                                lineNumber: 1189,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "text-sm font-mono text-n700 dark:text-n20 break-all",
                                                                                children: item.embed_model
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                                lineNumber: 1192,
                                                                                columnNumber: 31
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1188,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 1186,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 1160,
                                                        columnNumber: 23
                                                    }, this),
                                                    !isMultiSelectMode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-3",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                onClick: ()=>openEditDataModal(item),
                                                                className: "w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors font-medium flex items-center justify-center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiTable"], {
                                                                        className: "mr-2"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                                        lineNumber: 1207,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    "Edit Data"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 1203,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                onClick: ()=>openDeleteModal(item),
                                                                disabled: deleteLoading === item._id,
                                                                className: "w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:bg-gray-300 dark:disabled:bg-gray-700 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center",
                                                                children: deleteLoading === item._id ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                                            lineNumber: 1217,
                                                                            columnNumber: 33
                                                                        }, this),
                                                                        "Deleting..."
                                                                    ]
                                                                }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiTrash"], {
                                                                            className: "mr-2"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/showdeleteindex.tsx",
                                                                            lineNumber: 1222,
                                                                            columnNumber: 33
                                                                        }, this),
                                                                        "Delete Index"
                                                                    ]
                                                                }, void 0, true)
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                                lineNumber: 1210,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 1202,
                                                        columnNumber: 25
                                                    }, this),
                                                    isMultiSelectMode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-center text-sm text-n500 dark:text-n40",
                                                        children: selectedItems.has(item._id) ? 'Selected for deletion' : 'Click checkbox to select'
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 1232,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                lineNumber: 1126,
                                                columnNumber: 21
                                            }, this)
                                        }, item._id, false, {
                                            fileName: "[project]/components/showdeleteindex.tsx",
                                            lineNumber: 1118,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 1116,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DeleteConfirmationModal, {
                                    isOpen: deleteModal.isOpen,
                                    onClose: closeDeleteModal,
                                    onConfirm: confirmDelete,
                                    itemData: deleteModal.item,
                                    isDeleting: deleteLoading !== null
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 1243,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DeleteConfirmationModal, {
                                    isOpen: bulkDeleteModal.isOpen,
                                    onClose: closeBulkDeleteModal,
                                    onConfirm: handleBulkDelete,
                                    itemData: null,
                                    selectedItems: bulkDeleteModal.items,
                                    isDeleting: isBulkDeleting,
                                    isBulkDelete: true,
                                    bulkProgress: bulkDeleteProgress
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 1252,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$modals$2f$EditDataModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    isOpen: editDataModal.isOpen,
                                    onClose: closeEditDataModal,
                                    indexData: editDataModal.indexData
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 1264,
                                    columnNumber: 13
                                }, this),
                                notification.isVisible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg transition-all duration-300 transform ${notification.isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'} ${notification.type === 'success' ? 'bg-green-500 text-white' : notification.type === 'error' ? 'bg-red-500 text-white' : 'bg-blue-500 text-white'}`,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: [
                                                    notification.type === 'success' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiCheckCircle"], {
                                                        className: "mr-2 text-lg"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 1283,
                                                        columnNumber: 57
                                                    }, this),
                                                    notification.type === 'error' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiWarning"], {
                                                        className: "mr-2 text-lg"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 1284,
                                                        columnNumber: 55
                                                    }, this),
                                                    notification.type === 'info' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiInfo"], {
                                                        className: "mr-2 text-lg"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 1285,
                                                        columnNumber: 54
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium",
                                                        children: notification.message
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/showdeleteindex.tsx",
                                                        lineNumber: 1286,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                lineNumber: 1282,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>setNotification((prev)=>({
                                                            ...prev,
                                                            isVisible: false
                                                        })),
                                                className: "ml-4 text-white hover:text-gray-200 transition-colors",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiX"], {
                                                    className: "text-lg"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/showdeleteindex.tsx",
                                                    lineNumber: 1292,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/components/showdeleteindex.tsx",
                                                lineNumber: 1288,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/showdeleteindex.tsx",
                                        lineNumber: 1281,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/showdeleteindex.tsx",
                                    lineNumber: 1272,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/showdeleteindex.tsx",
                            lineNumber: 873,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/showdeleteindex.tsx",
                        lineNumber: 872,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/showdeleteindex.tsx",
                lineNumber: 862,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LogoutConfirmationModal, {
                isOpen: showLogoutConfirm,
                onClose: ()=>setShowLogoutConfirm(false),
                onConfirm: handleLogout,
                isProcessing: isLogoutProcessing,
                userName: currentUser?.name || 'User'
            }, void 0, false, {
                fileName: "[project]/components/showdeleteindex.tsx",
                lineNumber: 1302,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/showdeleteindex.tsx",
        lineNumber: 856,
        columnNumber: 5
    }, this);
};
_s(ShowDeleteIndex, "sZP0B5Hdg6LbhhHeGRUl6xld5YQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c2 = ShowDeleteIndex;
const __TURBOPACK__default__export__ = ShowDeleteIndex;
var _c, _c1, _c2;
__turbopack_refresh__.register(_c, "DeleteConfirmationModal");
__turbopack_refresh__.register(_c1, "LogoutConfirmationModal");
__turbopack_refresh__.register(_c2, "ShowDeleteIndex");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/show-index/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$showdeleteindex$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/showdeleteindex.tsx [app-client] (ecmascript)");
"use client";
;
;
const ShowIndexPage = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$showdeleteindex$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/app/show-index/page.tsx",
        lineNumber: 6,
        columnNumber: 10
    }, this);
};
_c = ShowIndexPage;
const __TURBOPACK__default__export__ = ShowIndexPage;
var _c;
__turbopack_refresh__.register(_c, "ShowIndexPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/show-index/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=_3fb893._.js.map