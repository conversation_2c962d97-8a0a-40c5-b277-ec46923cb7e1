#!/usr/bin/env python3
"""
Test script to verify API fallback works when corrupted data is detected.
"""

import requests
import json

def test_api_fallback():
    """Test API fallback with Tamil query"""
    
    print("🧪 TESTING API FALLBACK FOR CORRUPTED DATA")
    print("=" * 50)
    
    # Test endpoint
    url = "http://localhost:5010/financial_query"
    
    # Test case: Tamil query that should trigger API fallback if corrupted data is detected
    tamil_test = {
        "query": "புதுக்கோட்டை மின்தடை போராட்டம் பற்றி கூறுங்கள்",
        "language": "Tamil",
        "index_name": "default"
    }
    
    try:
        print(f"📤 Sending Tamil query: {tamil_test['query']}")
        response = requests.post(url, json=tamil_test, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Response received successfully")
            
            # Check AI response for corruption
            ai_response = data.get('ai_response', '')
            print(f"🤖 AI Response length: {len(ai_response)} characters")
            print(f"🤖 AI Response preview: {ai_response[:200]}...")
            
            # Check for corruption patterns
            corruption_patterns = ["கொழுப்பு", "தகவல்தொடர்புகள்", "குறும்படம் கேலக்", "எக்ஸெல்:", "வெளியீடு:"]
            is_corrupted = any(pattern in ai_response for pattern in corruption_patterns)
            
            if is_corrupted:
                print("❌ CORRUPTION STILL DETECTED in AI response!")
                for pattern in corruption_patterns:
                    if pattern in ai_response:
                        print(f"   - Found corruption pattern: '{pattern}'")
            else:
                print("✅ NO CORRUPTION detected in AI response!")
                
            # Check retrieved documents
            retrieved_docs = data.get('retrieved_documents', [])
            print(f"📊 Retrieved documents: {len(retrieved_docs)}")
            
            for i, doc in enumerate(retrieved_docs[:3]):
                doc_text = doc.get('text', '')
                print(f"   Doc {i+1}: {doc_text[:100]}...")
                
                # Check if this document came from API
                if 'API தேடல்' in doc_text or 'wikipedia' in doc.get('category', '').lower():
                    print(f"   ✅ Document {i+1} came from API fallback")
                elif any(pattern in doc_text for pattern in corruption_patterns):
                    print(f"   ❌ Document {i+1} contains corruption")
                else:
                    print(f"   ✅ Document {i+1} appears clean")
            
            # Check language processing
            if "language_processing" in data:
                lang_proc = data["language_processing"]
                print(f"🌐 Language Processing:")
                print(f"   - Enabled: {lang_proc.get('enabled', False)}")
                print(f"   - Query Language: {lang_proc.get('query_language', 'Unknown')}")
                print(f"   - CSV Language: {lang_proc.get('csv_language', 'Unknown')}")
                print(f"   - Direct Processing: {lang_proc.get('direct_processing_used', False)}")
                
                if lang_proc.get('direct_processing_used'):
                    print("🎉 SUCCESS: Direct processing was used (no translation)!")
                else:
                    print("⚠️ Translation-based processing was used")
                    
        else:
            print(f"❌ ERROR: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 API FALLBACK TEST COMPLETED")

if __name__ == "__main__":
    test_api_fallback()
