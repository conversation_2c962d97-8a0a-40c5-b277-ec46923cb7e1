import os
import csv
import io
import re
import json
import pandas as pd
import numpy as np
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import faiss
from langchain_huggingface.embeddings import HuggingFaceEmbeddings
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import uuid
import time
import database  # Import our database module
from datetime import datetime
import requests
from openai import OpenAI
import openpyxl  # For Excel file processing
import hashlib  # For cache key generation

# Response formatting utilities
class ResponseFormatter:
    """
    Utility class for standardizing API response formats across the application.
    Provides consistent structure, error handling, and user-friendly messaging.
    """

    @staticmethod
    def success_response(data: Any = None, message: str = "Operation completed successfully",
                        metadata: Dict[str, Any] = None, status_code: int = 200) -> tuple:
        """
        Create a standardized success response.

        Args:
            data: The main response data
            message: Success message for the user
            metadata: Additional context information
            status_code: HTTP status code

        Returns:
            tuple: (response_dict, status_code)
        """
        response = {
            "success": True,
            "status": "success",
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }

        if metadata:
            response["metadata"] = metadata

        return response, status_code

    @staticmethod
    def error_response(error: str, error_type: str = "general_error",
                      details: Dict[str, Any] = None, status_code: int = 400) -> tuple:
        """
        Create a standardized error response with user-friendly messaging.

        Args:
            error: Error message
            error_type: Category of error for better handling
            details: Additional error context
            status_code: HTTP status code

        Returns:
            tuple: (response_dict, status_code)
        """
        # Map technical errors to user-friendly messages
        user_friendly_messages = {
            "csv_parse_error": "Unable to process the CSV file. Please check the file format and try again.",
            "index_creation_error": "Failed to create the search index. Please try again or contact support.",
            "faiss_access_error": "Unable to access the search database. Please try again later.",
            "upload_cancelled": "The upload process was cancelled by the user.",
            "network_error": "Network connection issue. Please check your connection and try again.",
            "validation_error": "Invalid input provided. Please check your data and try again.",
            "permission_error": "You don't have permission to perform this action.",
            "not_found_error": "The requested resource was not found.",
            "rate_limit_error": "Too many requests. Please wait a moment and try again."
        }

        user_message = user_friendly_messages.get(error_type, error)

        response = {
            "success": False,
            "status": "error",
            "error": {
                "message": user_message,
                "type": error_type,
                "technical_details": error if error_type in user_friendly_messages else None
            },
            "timestamp": datetime.now().isoformat()
        }

        if details:
            response["error"]["details"] = details

        return response, status_code

    @staticmethod
    def processing_response(message: str, progress: Dict[str, Any] = None,
                          process_id: str = None) -> Dict[str, Any]:
        """
        Create a response for ongoing processing operations.

        Args:
            message: Status message
            progress: Progress information
            process_id: Unique identifier for the process

        Returns:
            dict: Response dictionary
        """
        response = {
            "success": True,
            "status": "processing",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }

        if progress:
            response["progress"] = progress

        if process_id:
            response["process_id"] = process_id

        return response

    @staticmethod
    def data_response(data: Any, total_count: int = None, page_info: Dict[str, Any] = None,
                     message: str = "Data retrieved successfully") -> Dict[str, Any]:
        """
        Create a response for data retrieval operations.

        Args:
            data: The retrieved data
            total_count: Total number of items available
            page_info: Pagination information
            message: Success message

        Returns:
            dict: Response dictionary
        """
        response = {
            "success": True,
            "status": "success",
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }

        if total_count is not None:
            response["total_count"] = total_count

        if page_info:
            response["pagination"] = page_info

        return response
try:
    from pinecone import Pinecone
except ImportError:
    print("Warning: Pinecone not installed. Pinecone functionality will be disabled.")
    Pinecone = None
# Load environment variables
load_dotenv()
NEWS_API_KEY = os.getenv("NEWS_API_KEY")

# Configuration
# Get the directory where this script is located
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
FAISS_DATA_DIR = os.getenv("FAISS_DATA_DIR", os.path.join(SCRIPT_DIR, "faiss_data"))  # Directory to store FAISS files
CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", 500))
BATCH_SIZE = int(os.getenv("BATCH_SIZE", 10))
# Configuration for suggest.py
FINANCIAL_INDEX_NAME = os.getenv("FINANCIAL_INDEX_NAME", "financialnews")
FINANCIAL_PINECONE_API_KEY = os.getenv("FINANCIAL_PINECONE_API_KEY", "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua")
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
API_ENVIRONMENT = os.getenv("API_ENVIRONMENT", "development")  # Default to development if not set

# Backend Server Configuration
BACKEND_SERVER_URL = os.getenv("BACKEND_SERVER_URL", "http://localhost:5010")

# Ensure FAISS data directory exists
os.makedirs(FAISS_DATA_DIR, exist_ok=True)

# Available embedding models
EMBEDDING_MODELS = {
    "all-MiniLM-L6-v2": {
        "name": "all-MiniLM-L6-v2",
        "dimension": 384,
        "description": "Sentence Transformers model for general purpose embeddings"
    },
    "all-mpnet-base-v2": {
        "name": "all-mpnet-base-v2",
        "dimension": 768,
        "description": "Higher quality model with larger dimensions"
    },
    "paraphrase-multilingual-MiniLM-L12-v2": {
        "name": "paraphrase-multilingual-MiniLM-L12-v2",
        "dimension": 384,
        "description": "Multilingual model supporting 50+ languages"
    },
    "distiluse-base-multilingual-cased-v1": {
        "name": "distiluse-base-multilingual-cased-v1",
        "dimension": 512,
        "description": "Efficient multilingual model"
    }
}

# Default embedding model
DEFAULT_EMBED_MODEL = "all-MiniLM-L6-v2"

# Dictionary to track active uploads
active_uploads = {}
active_uploads_lock = threading.Lock()

# Dictionary to cache embedding models
embedding_models = {}

# Initialize Flask app
app = Flask(__name__)

# Configure CORS with more permissive settings for development
CORS(app,
     resources={
         r"/api/*": {
             "origins": [
                 "http://localhost:3000",
                 "http://localhost:3001",
                 "http://127.0.0.1:3000",
                 "http://127.0.0.1:3001",
                 "http://**************:3000",
                 "http://**************:3001",
                 BACKEND_SERVER_URL
             ],
             "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
             "allow_headers": ["Content-Type", "Authorization", "X-Requested-With", "Origin"],
             "supports_credentials": True
         },
         r"/financial_query": {
             "origins": [
                 "http://localhost:3000",
                 "http://localhost:3001",
                 "http://127.0.0.1:3000",
                 "http://127.0.0.1:3001",
                 "http://**************:3000",
                 "http://**************:3001",
                 BACKEND_SERVER_URL
             ],
             "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
             "allow_headers": ["Content-Type", "Authorization", "X-Requested-With", "Origin"],
             "supports_credentials": True
         }
     },
     # Additional CORS settings for development
     send_wildcard=False,
     vary_header=True
)

def get_embedder(model_name=DEFAULT_EMBED_MODEL):
    """
    Get or initialize an embedding model.

    Args:
        model_name: Name of the embedding model to use

    Returns:
        HuggingFaceEmbeddings: The embedding model
    """
    if model_name not in EMBEDDING_MODELS:
        print(f"Warning: Unknown model '{model_name}'. Using default model '{DEFAULT_EMBED_MODEL}'")
        model_name = DEFAULT_EMBED_MODEL

    if model_name not in embedding_models:
        print(f"Initializing embedding model: {model_name}")
        embedding_models[model_name] = HuggingFaceEmbeddings(model_name=model_name)

    return embedding_models[model_name]

# Initialize default embedding model
embedder = get_embedder(DEFAULT_EMBED_MODEL)

# Add a simple health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    """
    Enhanced health check endpoint to verify the server and upload services are running.
    """
    try:
        # Check upload service availability
        upload_services = {}

        try:
            from services.youtube_processor import process_youtube_url
            upload_services['youtube_processor'] = True
        except ImportError:
            upload_services['youtube_processor'] = False

        try:
            from services.article_processor import process_article_url
            upload_services['article_processor'] = True
        except ImportError:
            upload_services['article_processor'] = False

        try:
            from services.pdf_processor import process_pdf_file
            upload_services['pdf_processor'] = True
        except ImportError:
            upload_services['pdf_processor'] = False

        try:
            from services.document_processor import process_document_file
            upload_services['document_processor'] = True
        except ImportError:
            upload_services['document_processor'] = False

        try:
            from services.audio_proccessor import process_audio_file
            upload_services['audio_processor'] = True
        except ImportError:
            upload_services['audio_processor'] = False

        # Check DeepSeek API availability
        deepseek_status = {
            "api_key_configured": bool(DEEPSEEK_API_KEY),
            "client_initialized": deepseek_client is not None
        }

        # Prepare health data
        health_data = {
            "server_status": "running",
            "faiss_data_dir": FAISS_DATA_DIR,
            "default_embed_model": DEFAULT_EMBED_MODEL,
            "deepseek_status": deepseek_status,
            "upload_services": upload_services,
            "available_endpoints": [
                "/api/process_youtube",
                "/api/process_article",
                "/api/process_pdf",
                "/api/process_document",
                "/api/process_audio",
                "/api/upload-csv",
                "/api/query-faiss",
                "/financial_query"
            ]
        }

        # Calculate service availability percentage
        total_services = len(upload_services)
        active_services = sum(1 for status in upload_services.values() if status)
        service_availability = (active_services / total_services * 100) if total_services > 0 else 100

        metadata = {
            "service_availability_percentage": round(service_availability, 1),
            "total_services": total_services,
            "active_services": active_services,
            "server_uptime": "Available", # Could be enhanced with actual uptime tracking
            "api_version": "1.0.0"
        }

        response, status_code = ResponseFormatter.success_response(
            data=health_data,
            message="FAISS backend server is running and healthy",
            metadata=metadata
        )

        return jsonify(response), status_code

    except Exception as e:
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="health_check_error",
            status_code=500
        )
        return jsonify(response), status_code

@app.route('/api/translate', methods=['POST'])
def translate_text():
    """
    Translate text using the backend translation service.
    """
    try:
        data = request.get_json() or {}
        text = data.get('text', '').strip()
        source_lang = data.get('source_lang', 'auto')
        target_lang = data.get('target_lang', 'en')

        if not text:
            response, status_code = ResponseFormatter.error_response(
                error="Text is required for translation",
                error_type="validation_error",
                status_code=400
            )
            return jsonify(response), status_code

        # Import translation service
        try:
            from services.translation_service import translation_service
            
            # Perform translation
            translation_result = translation_service.translate_text(text, target_lang, source_lang)
            
            response_data = {
                "original_text": text,
                "translated_text": translation_result.get('translated_text', text),
                "source_language": translation_result.get('source_language', source_lang),
                "target_language": translation_result.get('target_language', target_lang),
                "translation_provider": translation_result.get('translation_provider', 'unknown'),
                "cached": translation_result.get('cached', False)
            }

            response, status_code = ResponseFormatter.success_response(
                data=response_data,
                message="Translation completed successfully"
            )
            return jsonify(response), status_code

        except ImportError:
            response, status_code = ResponseFormatter.error_response(
                error="Translation service not available",
                error_type="service_unavailable",
                status_code=503
            )
            return jsonify(response), status_code

    except Exception as e:
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="translation_error",
            status_code=500
        )
        return jsonify(response), status_code

@app.route('/api/test-deepseek', methods=['POST'])
def test_deepseek():
    """
    Test endpoint to verify DeepSeek API functionality.
    """
    try:
        data = request.get_json() or {}
        test_query = data.get('query', 'What is artificial intelligence?')
        test_answer = data.get('answer', 'Artificial intelligence is a field of computer science focused on creating systems that can perform tasks that typically require human intelligence.')

        print(f"🧪 Testing DeepSeek API with query: {test_query}")

        # Get language parameter for testing
        test_language = data.get('language', 'English')

        # Test the related questions generation
        related_questions = generate_related_questions(test_query, test_answer, test_language)

        return jsonify({
            "success": True,
            "message": "DeepSeek API test completed",
            "test_query": test_query,
            "test_answer": test_answer,
            "related_questions": related_questions,
            "api_key_configured": bool(DEEPSEEK_API_KEY),
            "questions_count": len(related_questions)
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "api_key_configured": bool(DEEPSEEK_API_KEY)
        }), 500

@app.route('/api/test-translation', methods=['POST'])
def test_translation():
    """
    Test endpoint to verify translation functionality.
    """
    try:
        data = request.get_json() or {}
        text = data.get('text', 'Hello, how are you?')
        target_language = data.get('target_language', 'ta')

        # Import translation service
        try:
            from services.translation_service import translation_service

            # Test language detection
            detected_language = translation_service.detect_language(text)

            # Test translation
            translation_result = translation_service.translate_text(text, target_language)

            # Test response translation
            test_response = {
                'ai_response': 'This is a test response for translation.',
                'related_questions': ['What is this?', 'How does it work?', 'Why is it important?']
            }

            translated_response = translation_service.translate_response_data(test_response, target_language)

            return jsonify({
                "success": True,
                "message": "Translation test completed",
                "original_text": text,
                "detected_language": detected_language,
                "target_language": target_language,
                "translation_result": translation_result,
                "test_response_translation": translated_response,
                "cache_stats": translation_service.get_cache_stats()
            })

        except ImportError as e:
            return jsonify({
                "success": False,
                "error": f"Translation service not available: {str(e)}"
            }), 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/test-language-aware', methods=['POST'])
def test_language_aware():
    """
    Test endpoint to verify language-aware processing functionality.
    """
    try:
        data = request.get_json() or {}
        query = data.get('query', 'What is the stock market performance?')
        csv_language = data.get('csv_language', 'English')

        # Import language-aware processor
        try:
            from services.language_aware_processor import language_aware_processor

            # Mock search function for testing
            def mock_search_function(search_query, **kwargs):
                return [
                    {
                        'text': f'Mock search result for: {search_query}',
                        'score': 0.95,
                        'metadata': {
                            'source': 'test_data',
                            'category': 'finance'
                        }
                    },
                    {
                        'text': f'Another result for: {search_query}',
                        'score': 0.87,
                        'metadata': {
                            'source': 'test_data_2',
                            'category': 'economics'
                        }
                    }
                ]

            # Create mock CSV data based on specified language
            if csv_language == 'Tamil':
                import pandas as pd
                mock_df = pd.DataFrame({
                    'தலைப்பு': ['பங்குச் சந்தை செய்தி', 'பொருளாதார அறிக்கை'],
                    'உள்ளடக்கம்': ['பங்குச் சந்தை இன்று நன்றாக செயல்பட்டது', 'பொருளாதார குறிகாட்டிகள் வளர்ச்சியைக் காட்டுகின்றன'],
                    'வகை': ['நிதி', 'பொருளாதாரம்']
                })
            else:
                import pandas as pd
                mock_df = pd.DataFrame({
                    'Title': ['Stock Market News', 'Economic Report'],
                    'Content': ['The stock market performed well today', 'Economic indicators show growth'],
                    'Category': ['Finance', 'Economics']
                })

            # Test language detection
            query_lang_info = language_aware_processor.detect_query_language(query)
            csv_lang_info = language_aware_processor.detect_csv_language(dataframe=mock_df)

            # Test processing strategy determination
            should_direct = language_aware_processor.should_use_direct_processing(
                query_lang_info['language'],
                csv_lang_info['language']
            )

            # Test full processing workflow
            processing_result = language_aware_processor.process_query_with_language_awareness(
                query=query,
                dataframe=mock_df,
                search_function=mock_search_function
            )

            # Generate processing summary
            summary = language_aware_processor.get_processing_summary(processing_result)

            return jsonify({
                "success": True,
                "message": "Language-aware processing test completed",
                "test_query": query,
                "csv_language": csv_language,
                "query_language_info": query_lang_info,
                "csv_language_info": csv_lang_info,
                "should_use_direct_processing": should_direct,
                "processing_result": {
                    "strategy": processing_result.get('processing_strategy'),
                    "success": processing_result.get('success'),
                    "query_translated": processing_result.get('query_translated', False),
                    "results_translated": processing_result.get('results_translated', False),
                    "translations_count": len(processing_result.get('translations_performed', [])),
                    "processing_duration_ms": processing_result.get('processing_duration_ms', 0),
                    "results_count": len(processing_result.get('search_results', []))
                },
                "processing_summary": summary,
                "cache_stats": language_aware_processor.get_cache_stats()
            })

        except ImportError as e:
            return jsonify({
                "success": False,
                "error": f"Language-aware processor not available: {str(e)}"
            }), 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500



# Add CORS preflight handler for all API routes
@app.before_request
def handle_preflight():
    if request.method == "OPTIONS":
        response = jsonify({})
        origin = request.headers.get('Origin')
        allowed_origins = [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001',
            'http://**************:3000',
            'http://**************:3001',
            BACKEND_SERVER_URL
        ]

        if origin in allowed_origins:
            response.headers.add("Access-Control-Allow-Origin", origin)
        else:
            response.headers.add("Access-Control-Allow-Origin", BACKEND_SERVER_URL)

        response.headers.add('Access-Control-Allow-Headers', "Content-Type,Authorization,X-Requested-With,Origin")
        response.headers.add('Access-Control-Allow-Methods', "GET,PUT,POST,DELETE,OPTIONS")
        response.headers.add('Access-Control-Allow-Credentials', 'true')
        return response

def create_faiss_index(index_name: str, dimension: int = None, embed_model: str = None) -> Dict[str, Any]:
    """
    Create a new FAISS index directory structure with the given name and dimension.

    Args:
        index_name: Name for the new index (will be used as folder name)
        dimension: Vector dimension (default: None, will use dimension from embedding model)
        embed_model: Name of the embedding model to use (default: None, will use DEFAULT_EMBED_MODEL)

    Returns:
        Dict with success status and error message if any
    """
    # Determine the dimension based on the embedding model
    if dimension is None:
        model_name = embed_model or DEFAULT_EMBED_MODEL
        if model_name in EMBEDDING_MODELS:
            dimension = EMBEDDING_MODELS[model_name]["dimension"]
        else:
            dimension = EMBEDDING_MODELS[DEFAULT_EMBED_MODEL]["dimension"]
        print(f"Using dimension {dimension} from model {model_name}")

    try:
        # Create index directory path
        index_dir = os.path.join(FAISS_DATA_DIR, index_name)
        faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
        metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

        # Check if index already exists
        if os.path.exists(faiss_file_path) and os.path.exists(metadata_file_path):
            print(f"FAISS index '{index_name}' already exists. Using existing index...")
            return {"success": True, "message": "Using existing index"}

        # Create directory if it doesn't exist
        os.makedirs(index_dir, exist_ok=True)

        # Create empty FAISS index
        # Using IndexFlatIP for inner product (cosine similarity after normalization)
        index = faiss.IndexFlatIP(dimension)

        # Save empty index
        faiss.write_index(index, faiss_file_path)

        # Create empty metadata file
        with open(metadata_file_path, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

        print(f"Successfully created FAISS index: {index_name}")
        return {"success": True}

    except Exception as e:
        error_message = str(e)
        print(f"Error creating FAISS index: {error_message}")
        return {"success": False, "error": error_message, "error_type": "create_index_error"}


def load_faiss_index(index_name: str) -> tuple:
    """
    Load FAISS index and metadata from files.
    Special handling for 'default' index which may use different file names.

    Args:
        index_name: Name of the index to load

    Returns:
        tuple: (faiss_index, metadata_store, success)
    """
    try:
        index_dir = os.path.join(FAISS_DATA_DIR, index_name)

        # For default index, try both naming conventions
        if index_name == "default":
            # Try standard naming first
            faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
            metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

            # If standard naming doesn't exist, try legacy naming
            if not (os.path.exists(faiss_file_path) and os.path.exists(metadata_file_path)):
                faiss_file_path = os.path.join(index_dir, "news_index.faiss")
                metadata_file_path = os.path.join(index_dir, "news_metadata.json")
                print(f"Using legacy file names for default index: {faiss_file_path}, {metadata_file_path}")
        else:
            # For other indexes, use standard naming
            faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
            metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

        if not os.path.exists(faiss_file_path) or not os.path.exists(metadata_file_path):
            print(f"FAISS index files not found for '{index_name}': {faiss_file_path}, {metadata_file_path}")
            return None, [], False

        # Load FAISS index
        faiss_index = faiss.read_index(faiss_file_path)

        # Load metadata
        with open(metadata_file_path, 'r', encoding='utf-8') as f:
            metadata_store = json.load(f)

        print(f"✅ Successfully loaded FAISS index '{index_name}' with {faiss_index.ntotal} vectors")
        return faiss_index, metadata_store, True

    except Exception as e:
        print(f"Error loading FAISS index {index_name}: {e}")
        return None, [], False

def save_faiss_index(index_name: str, faiss_index, metadata_store: list) -> bool:
    """
    Save FAISS index and metadata to files.

    Args:
        index_name: Name of the index
        faiss_index: FAISS index object
        metadata_store: List of metadata dictionaries

    Returns:
        bool: Success status
    """
    try:
        index_dir = os.path.join(FAISS_DATA_DIR, index_name)
        faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
        metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

        # Ensure directory exists
        os.makedirs(index_dir, exist_ok=True)

        # Save FAISS index
        faiss.write_index(faiss_index, faiss_file_path)

        # Save metadata
        with open(metadata_file_path, 'w', encoding='utf-8') as f:
            json.dump(metadata_store, f, ensure_ascii=False, indent=2)

        return True

    except Exception as e:
        print(f"Error saving FAISS index {index_name}: {e}")
        return False



def chunk_text(text: str, size: int = 500) -> List[str]:
    """
    Split text into chunks of approximately equal size.

    Args:
        text: Text to split
        size: Maximum chunk size

    Returns:
        List of text chunks
    """
    chunks, start = [], 0
    if not text or not isinstance(text, str):
        return []

    while start < len(text):
        end = min(start + size, len(text))
        while end > start and end < len(text) and not text[end - 1].isspace():
            end -= 1
        if end == start:
            end = min(start + size, len(text))
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        start = end
    return chunks

def process_csv_data(csv_data: str, index_name: str, update_mode: str = None,
                upload_id: str = None, client_email: str = None, embed_model: str = None) -> Dict[str, Any]:
    """
    Process CSV data and upload to FAISS and database.

    Args:
        csv_data: CSV data as string
        index_name: Name of the FAISS index to use
        update_mode: Mode for handling existing indexes ('update' to keep existing data, 'new' to delete and start fresh)
        upload_id: Unique identifier for this upload process (for cancellation)
        client_email: Client email for tracking (default: None)
        embed_model: Name of the embedding model to use (default: None, will use DEFAULT_EMBED_MODEL)

    Returns:
        Dict with processing results
    """
    # Use the specified embedding model or default
    model_name = embed_model or DEFAULT_EMBED_MODEL
    if model_name not in EMBEDDING_MODELS:
        print(f"Warning: Unknown model '{model_name}'. Using default model '{DEFAULT_EMBED_MODEL}'")
        model_name = DEFAULT_EMBED_MODEL

    # Get the embedder for this model
    embedder = get_embedder(model_name)
    print(f"Using embedding model: {model_name} with dimension {EMBEDDING_MODELS[model_name]['dimension']}")
    try:

        # Parse CSV data
        try:
            # Try different parsing strategies with improved handling of empty rows and column names
            parsing_strategies = [
                # Strategy 1: Default parsing with keep_default_na=True to properly detect empty cells
                lambda: pd.read_csv(io.StringIO(csv_data), keep_default_na=True),

                # Strategy 2: With index_col=0 for unnamed first column
                lambda: pd.read_csv(io.StringIO(csv_data), index_col=0, keep_default_na=True),

                # Strategy 3: With escape characters
                lambda: pd.read_csv(io.StringIO(csv_data), escapechar='\\', quotechar='"', doublequote=True, keep_default_na=True),

                # Strategy 4: With different separator
                lambda: pd.read_csv(io.StringIO(csv_data), sep=',', engine='python', keep_default_na=True),

                # Strategy 5: With error handling
                lambda: pd.read_csv(io.StringIO(csv_data), on_bad_lines='skip', keep_default_na=True)
            ]

            df = None
            for i, strategy in enumerate(parsing_strategies):
                try:
                    df = strategy()
                    print(f"CSV parsed successfully with strategy {i+1}. Shape: {df.shape}")
                    if not df.empty:
                        # Clean column names - remove whitespace and ensure they're strings
                        df.columns = [str(col).strip() for col in df.columns]

                        # Print column information
                        print(f"Columns: {df.columns.tolist()}")

                        # Count non-empty rows
                        non_empty_rows = 0
                        for _, row in df.iterrows():
                            if any(pd.notna(val) and str(val).strip() != '' for val in row.values):
                                non_empty_rows += 1

                        print(f"Total rows: {len(df)}, Non-empty rows: {non_empty_rows}")

                        if non_empty_rows > 0:
                            # Find first non-empty row to show as sample
                            for idx, row in df.iterrows():
                                if any(pd.notna(val) and str(val).strip() != '' for val in row.values):
                                    print(f"First non-empty row sample (index {idx}): {row.to_dict()}")
                                    break
                            break
                        else:
                            print("Warning: All rows appear to be empty. Will try next parsing strategy.")
                            df = None
                except Exception as e:
                    print(f"Parsing strategy {i+1} failed: {e}")

            if df is None or df.empty:
                raise ValueError("Could not parse CSV data with any strategy or all rows are empty")

        except Exception as e:
            error_message = str(e)
            print(f"Error parsing CSV data: {error_message}")
            return {
                "success": False,
                "error": f"Failed to parse CSV data: {error_message}",
                "error_type": "csv_parse_error"
            }

        # Detect CSV language for better processing
        csv_language_info = None
        try:
            from services.language_aware_processor import language_aware_processor
            csv_language_info = language_aware_processor.detect_csv_language(
                dataframe=df,
                index_name=index_name
            )
            print(f"📊 CSV Language Detection: {csv_language_info.get('language', 'Unknown')} "
                  f"(confidence: {csv_language_info.get('confidence', 0):.3f})")
        except Exception as e:
            print(f"⚠️ CSV language detection failed: {e}")

        # Store CSV data in database with embedding model and language information
        try:
            # Get the dimension for the embedding model
            dimension = EMBEDDING_MODELS[model_name]["dimension"] if model_name in EMBEDDING_MODELS else None

            db_success, db_message, db_info = database.create_table_from_csv(
                csv_data,
                index_name,
                client_email,
                embedding_model=model_name,
                embedding_dimension=dimension
            )

            if not db_success:
                print(f"Warning: Failed to store CSV data in database: {db_message}")
                # Continue with FAISS processing even if database storage fails
            else:
                print(f"Successfully stored CSV data in database: {db_message}")

            # Store language information in cache if detection was successful
            if csv_language_info and csv_language_info.get('language') != 'English':
                try:
                    language_aware_processor.cache[f"csv_lang_{index_name}"] = csv_language_info
                    print(f"✅ Cached CSV language info for index '{index_name}'")
                except:
                    pass  # Non-critical operation

        except Exception as e:
            print(f"Error storing CSV data in database: {e}")
            # Continue with FAISS processing even if database storage fails

        # Initialize FAISS index
        try:
            # Get the dimension for the embedding model
            dimension = EMBEDDING_MODELS[model_name]["dimension"]

            # If update_mode is 'new', delete the existing index and create a new one
            if update_mode == 'new':
                try:
                    index_dir = os.path.join(FAISS_DATA_DIR, index_name)
                    if os.path.exists(index_dir):
                        print(f"Deleting existing index '{index_name}' as per 'new' update mode...")
                        import shutil
                        shutil.rmtree(index_dir)
                        print(f"Successfully deleted index '{index_name}'")

                    # Create a new index
                    create_result = create_faiss_index(index_name, dimension, model_name)
                    if not create_result["success"]:
                        return {
                            "success": False,
                            "error": f"Failed to create new index: {create_result['error']}",
                            "error_type": "index_creation_error"
                        }
                    print(f"Successfully created new index '{index_name}'")
                except Exception as e:
                    error_message = str(e)
                    print(f"Error handling 'new' update mode: {error_message}")
                    return {
                        "success": False,
                        "error": f"Failed to recreate index: {error_message}",
                        "error_type": "index_recreation_error"
                    }

            # Load or create FAISS index
            faiss_index, metadata_store, success = load_faiss_index(index_name)
            if not success:
                # Create new index if it doesn't exist
                create_result = create_faiss_index(index_name, dimension, model_name)
                if not create_result["success"]:
                    return {
                        "success": False,
                        "error": f"Failed to create FAISS index: {create_result['error']}",
                        "error_type": "faiss_access_error"
                    }
                faiss_index, metadata_store, success = load_faiss_index(index_name)
                if not success:
                    return {
                        "success": False,
                        "error": "Failed to load newly created FAISS index",
                        "error_type": "faiss_access_error"
                    }
        except Exception as e:
            error_message = str(e)
            print(f"Error initializing FAISS index: {error_message}")
            return {
                "success": False,
                "error": f"Failed to access FAISS index: {error_message}",
                "error_type": "faiss_access_error"
            }

        # Process data in batches
        vectors = []
        new_metadata = []
        total_vectors = 0

        # Register this upload in active_uploads if upload_id is provided
        if upload_id:
            with active_uploads_lock:
                active_uploads[upload_id] = {
                    "status": "processing",
                    "total_rows": len(df),
                    "processed_rows": 0,
                    "total_vectors": 0,
                    "index_name": index_name,
                    "start_time": time.time(),
                    "cancelled": False
                }

        # Function to prepare a vector from a row and chunk
        def prepare_vector(row_idx, chunk_idx, chunk, metadata):
            try:
                # Check if upload has been cancelled
                if upload_id and upload_id in active_uploads:
                    with active_uploads_lock:
                        if active_uploads[upload_id]["cancelled"]:
                            return None

                # Generate embedding for the chunk using the selected model
                vector = embedder.embed_documents([chunk])[0]
                vector_array = np.array(vector, dtype='float32')

                # Create a truly unique ID for this vector by adding a UUID component
                # This ensures that even if the same CSV is uploaded multiple times in "update" mode,
                # we won't overwrite existing vectors but will add new ones
                unique_suffix = str(uuid.uuid4())[:8]  # Use first 8 chars of UUID for brevity
                vector_id = f"{index_name}-{row_idx}-chunk-{chunk_idx}-{unique_suffix}"

                # Log the vector ID creation for debugging
                if row_idx % 50 == 0 and chunk_idx == 0:  # Log only occasionally to avoid excessive output
                    print(f"Created unique vector ID: {vector_id} for row {row_idx}, chunk {chunk_idx}")

                # Add embedding model info to metadata
                metadata_with_model = {
                    **metadata,
                    "embedding_model": model_name,
                    "embedding_dimension": EMBEDDING_MODELS[model_name]["dimension"],
                    "upload_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),  # Add timestamp to track when this vector was created
                    "row_idx": row_idx,
                    "chunk_idx": chunk_idx,
                    "chunk_text": chunk,
                    "vector_id": vector_id
                }

                # Return the vector data (vector array and metadata separately for FAISS)
                return {
                    "vector": vector_array,
                    "metadata": metadata_with_model
                }
            except Exception as e:
                print(f"Error preparing vector for row {row_idx}, chunk {chunk_idx}: {e}")
                return None

        # Function to process a single row
        def process_row(row_idx, row):
            try:
                # Check if upload has been cancelled
                if upload_id and upload_id in active_uploads:
                    with active_uploads_lock:
                        if active_uploads[upload_id]["cancelled"]:
                            return None

                # Check if row is empty (all values are NaN or empty strings)
                is_empty = True
                for val in row.values:
                    if pd.notna(val) and str(val).strip() != '':
                        is_empty = False
                        break

                # Skip empty rows
                if is_empty:
                    print(f"Skipping empty row at index {row_idx}")
                    return []

                # Extract content and metadata from the row
                content = ""
                metadata = {}

                # Extract specific fields if they exist (similar to api-pinecone-app.py)
                if 'Content' in row and pd.notna(row['Content']):
                    content = str(row['Content'])
                if 'Summary' in row and pd.notna(row['Summary']):
                    metadata["summary"] = str(row['Summary'])[:500]
                if 'URL' in row and pd.notna(row['URL']):
                    metadata["url"] = str(row['URL'])[:500]
                if 'Sentiment' in row and pd.notna(row['Sentiment']):
                    metadata["sentiment"] = str(row['Sentiment'])

                # Store all column values as metadata using column names as keys
                # This ensures we preserve the original column structure
                for col, val in row.items():
                    if pd.notna(val) and str(val).strip() != '':
                        # Limit metadata value length to 500 chars to avoid Pinecone limits
                        metadata[str(col)] = str(val)[:500]

                # If content is empty, try to build it from all columns
                if not content:
                    content_parts = []
                    for col, val in row.items():
                        if pd.notna(val) and str(col).strip() != '' and str(val).strip() != '':
                            content_parts.append(f"{col}: {val}")
                    content = " ".join(content_parts)

                # If still empty after trying to build from columns, use a default message
                if not content.strip():
                    content = f"Row {row_idx} from {index_name} dataset"
                    print(f"Warning: Empty content for row {row_idx}")

                # Add row_idx to metadata
                metadata["row_idx"] = row_idx

                # Split content into chunks (like in api-pinecone-app.py)
                chunks = chunk_text(content)
                if not chunks:
                    # If no chunks, create one with the content
                    chunks = [content]

                # Create vectors for each chunk
                vectors = []
                for chunk_idx, chunk in enumerate(chunks):
                    vector_data = prepare_vector(row_idx, chunk_idx, chunk, metadata)
                    if vector_data:
                        vectors.append(vector_data)

                return vectors
            except Exception as e:
                print(f"Error processing row {row_idx}: {e}")
                print(f"Row data: {row}")
                return []

        # Process rows in parallel
        print(f"Starting parallel processing of {len(df)} rows...")
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=5) as executor:
            # Submit all row processing tasks
            futures = [executor.submit(process_row, idx, row) for idx, row in df.iterrows()]
            total_futures = len(futures)
            completed_futures = 0

            # Process completed futures
            for future in as_completed(futures):
                completed_futures += 1
                if completed_futures % 100 == 0 or completed_futures == total_futures:
                    print(f"Processed {completed_futures}/{total_futures} rows ({(completed_futures/total_futures)*100:.1f}%)")
                # Check if upload has been cancelled
                if upload_id and upload_id in active_uploads:
                    with active_uploads_lock:
                        if active_uploads[upload_id]["cancelled"]:
                            print(f"Upload {upload_id} was cancelled. Stopping processing.")
                            # Clean up futures to prevent further processing
                            for f in futures:
                                f.cancel()
                            # Update status
                            active_uploads[upload_id]["status"] = "cancelled"
                            return {
                                "success": False,
                                "error": "Upload was cancelled by user",
                                "error_type": "upload_cancelled",
                                "total_vectors": total_vectors,
                                "upload_id": upload_id
                            }

                # Get the result from the future (now returns a list of vectors)
                vector_data_list = future.result()

                # Add all vectors to the batch
                if vector_data_list:
                    for vector_data in vector_data_list:
                        if vector_data:
                            vectors.append(vector_data["vector"])
                            new_metadata.append(vector_data["metadata"])

                # Process in batches when we reach the batch size
                while len(vectors) >= BATCH_SIZE:

                    # Check again if upload has been cancelled before database operation
                    if upload_id and upload_id in active_uploads:
                        with active_uploads_lock:
                            if active_uploads[upload_id]["cancelled"]:
                                print(f"Upload {upload_id} was cancelled before batch processing. Stopping processing.")
                                # Clean up futures to prevent further processing
                                for f in futures:
                                    if not f.done():
                                        f.cancel()
                                # Update status
                                active_uploads[upload_id]["status"] = "cancelled"
                                return {
                                    "success": False,
                                    "error": "Upload was cancelled by user",
                                    "error_type": "upload_cancelled",
                                    "total_vectors": total_vectors,
                                    "upload_id": upload_id
                                }

                        # Proceed with batch processing if not cancelled
                        batch_vectors = vectors[:BATCH_SIZE]
                        batch_metadata = new_metadata[:BATCH_SIZE]
                        vectors = vectors[BATCH_SIZE:]
                        new_metadata = new_metadata[BATCH_SIZE:]

                        try:
                            # Log the update mode for clarity
                            mode_msg = "adding new vectors" if update_mode == 'update' or update_mode is None else "replacing existing vectors"
                            print(f"Processing batch in {update_mode or 'update'} mode ({mode_msg})...")

                            # Convert to numpy array and normalize for cosine similarity
                            batch_array = np.vstack(batch_vectors)
                            faiss.normalize_L2(batch_array)

                            # Add vectors to FAISS index
                            faiss_index.add(batch_array)

                            # Add metadata to metadata store
                            metadata_store.extend(batch_metadata)

                            total_vectors += len(batch_vectors)
                            print(f"Successfully processed batch of {len(batch_vectors)} vectors. Total: {total_vectors}")

                            # Update progress in active_uploads
                            if upload_id and upload_id in active_uploads:
                                with active_uploads_lock:
                                    active_uploads[upload_id]["total_vectors"] = total_vectors
                                    active_uploads[upload_id]["processed_rows"] += len(batch_vectors)
                        except Exception as e:
                            print(f"Error processing batch: {e}")
                            # Continue processing despite errors

        # Process any remaining vectors
        if vectors:
            # Check again if upload has been cancelled before final processing
            if upload_id and upload_id in active_uploads:
                with active_uploads_lock:
                    if active_uploads[upload_id]["cancelled"]:
                        print(f"Upload {upload_id} was cancelled before final processing. Stopping processing.")
                        # Update status
                        active_uploads[upload_id]["status"] = "cancelled"
                        return {
                            "success": False,
                            "error": "Upload was cancelled by user",
                            "error_type": "upload_cancelled",
                            "total_vectors": total_vectors,
                            "upload_id": upload_id
                        }

            # Proceed with final processing if not cancelled
            try:
                # Log the update mode for clarity
                mode_msg = "adding new vectors" if update_mode == 'update' or update_mode is None else "replacing existing vectors"
                print(f"Processing final batch in {update_mode or 'update'} mode ({mode_msg})...")

                # Convert to numpy array and normalize for cosine similarity
                final_array = np.vstack(vectors)
                faiss.normalize_L2(final_array)

                # Add vectors to FAISS index
                faiss_index.add(final_array)

                # Add metadata to metadata store
                metadata_store.extend(new_metadata)

                total_vectors += len(vectors)
                print(f"Successfully processed final batch of {len(vectors)} vectors. Total: {total_vectors}")

                # Update progress in active_uploads
                if upload_id and upload_id in active_uploads:
                    with active_uploads_lock:
                        active_uploads[upload_id]["total_vectors"] = total_vectors
                        active_uploads[upload_id]["processed_rows"] += len(vectors)
            except Exception as e:
                print(f"Error processing final batch: {e}")
                # Continue despite errors

        # Save the updated FAISS index and metadata
        try:
            save_success = save_faiss_index(index_name, faiss_index, metadata_store)
            if save_success:
                print(f"Successfully saved FAISS index with {total_vectors} vectors")
            else:
                print(f"Warning: Failed to save FAISS index")
        except Exception as e:
            print(f"Error saving FAISS index: {e}")
            # Continue despite save errors

        # Calculate processing time and log completion
        end_time = time.time()
        processing_time = end_time - start_time
        print(f"Processing completed in {processing_time:.2f} seconds. Total vectors: {total_vectors}, Total rows: {len(df)}")

        # Update status to complete
        if upload_id and upload_id in active_uploads:
            with active_uploads_lock:
                active_uploads[upload_id]["status"] = "complete"
                active_uploads[upload_id]["end_time"] = end_time
                active_uploads[upload_id]["processing_time"] = processing_time
                print(f"Upload {upload_id} marked as complete.")

        return {
            "success": True,
            "index_name": index_name,
            "total_vectors": total_vectors,
            "total_rows": len(df),
            "processing_time_seconds": processing_time
        }
    except Exception as e:
        print(f"Error processing CSV data: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@app.route('/api/upload-csv', methods=['POST'])
def upload_csv():
    """
    Endpoint to handle CSV file uploads and store in FAISS with professional response formatting.
    """
    try:
        # Validate file upload
        if 'file' not in request.files:
            response, status_code = ResponseFormatter.error_response(
                error="No file provided in the request",
                error_type="validation_error"
            )
            return jsonify(response), status_code

        file = request.files['file']

        # Validate file selection
        if file.filename == '':
            response, status_code = ResponseFormatter.error_response(
                error="No file selected for upload",
                error_type="validation_error"
            )
            return jsonify(response), status_code

        # Validate file type
        if not file.filename.lower().endswith('.csv'):
            response, status_code = ResponseFormatter.error_response(
                error="Invalid file type. Only CSV files are supported",
                error_type="validation_error",
                details={"supported_formats": [".csv"], "received_file": file.filename}
            )
            return jsonify(response), status_code

        # Extract and validate form parameters
        client = request.form.get('client')
        index_name = request.form.get('index_name')
        update_mode = request.form.get('update_mode')  # 'update' or 'new'
        embed_model = request.form.get('embed_model', DEFAULT_EMBED_MODEL)

        # Validate required parameters
        if not index_name:
            response, status_code = ResponseFormatter.error_response(
                error="Index name is required for CSV upload",
                error_type="validation_error",
                details={"required_fields": ["index_name"]}
            )
            return jsonify(response), status_code

        # Validate update mode if provided
        if update_mode and update_mode not in ['update', 'new']:
            response, status_code = ResponseFormatter.error_response(
                error="Invalid update mode specified",
                error_type="validation_error",
                details={"valid_modes": ["update", "new"], "received": update_mode}
            )
            return jsonify(response), status_code

        # Log processing information
        print(f"📁 Processing CSV upload for client: {client or 'anonymous'}")
        print(f"🏷️  Index name: {index_name}")
        print(f"🔄 Update mode: {update_mode or 'update (default)'}")
        print(f"🧠 Embedding model: {embed_model}")

        # Read file content
        file_content = file.read().decode('utf-8')

        # Generate a unique upload ID
        upload_id = str(uuid.uuid4())

        # Create or access FAISS index
        index_result = create_faiss_index(index_name, embed_model=embed_model)
        if not index_result["success"]:
            response, status_code = ResponseFormatter.error_response(
                error=index_result.get("error", "Failed to create FAISS index"),
                error_type=index_result.get("error_type", "index_creation_error"),
                details={"index_name": index_name, "embedding_model": embed_model},
                status_code=500
            )
            return jsonify(response), status_code

        # Log processing mode details
        mode_descriptions = {
            'update': f"Adding new vectors to existing index '{index_name}' (preserving existing data)",
            'new': f"Recreating index '{index_name}' from scratch (existing data will be deleted)",
            None: f"Adding new vectors to existing index '{index_name}' (default update mode)"
        }
        print(f"🔧 Processing mode: {mode_descriptions.get(update_mode, mode_descriptions[None])}")

        # Process CSV data and upload to FAISS and database with the update mode, client email, and embedding model
        result = process_csv_data(file_content, index_name, update_mode=update_mode,
                                 upload_id=upload_id, client_email=client, embed_model=embed_model)

        if result["success"]:
            # Prepare success response data
            upload_data = {
                "index_name": index_name,
                "vector_count": result["total_vectors"],
                "total_rows_processed": result["total_rows"],
                "processing_time_seconds": result.get("processing_time_seconds", 0),
                "upload_id": upload_id,
                "embedding_model": embed_model,
                "embedding_dimension": EMBEDDING_MODELS[embed_model]["dimension"] if embed_model in EMBEDDING_MODELS else EMBEDDING_MODELS[DEFAULT_EMBED_MODEL]["dimension"],
                "update_mode": update_mode or "update",
                "database_storage_enabled": True
            }

            # Add client information if provided
            if client:
                upload_data["client"] = client

            # Create mode-specific success message
            mode_messages = {
                'update': f"Successfully added {result['total_vectors']} new vectors to existing index '{index_name}'",
                'new': f"Successfully created new index '{index_name}' with {result['total_vectors']} vectors",
                None: f"Successfully added {result['total_vectors']} vectors to index '{index_name}'"
            }

            success_message = mode_messages.get(update_mode, mode_messages[None])

            # Prepare metadata
            metadata = {
                "processing_stats": {
                    "vectors_per_second": round(result["total_vectors"] / max(result.get("processing_time_seconds", 1), 1), 2),
                    "rows_to_vectors_ratio": round(result["total_vectors"] / max(result["total_rows"], 1), 2)
                },
                "storage_info": {
                    "faiss_index_location": f"{FAISS_DATA_DIR}/{index_name}",
                    "database_table": index_name,
                    "retrieval_endpoint": "/api/get-csv-data"
                },
                "next_steps": [
                    f"Query your data using /api/query-faiss with index_name='{index_name}'",
                    f"Retrieve raw CSV data using /api/get-csv-data with index_name='{index_name}'",
                    f"Use /financial_query endpoint for AI-powered responses"
                ]
            }

            response, status_code = ResponseFormatter.success_response(
                data=upload_data,
                message=success_message,
                metadata=metadata
            )

            return jsonify(response), status_code
        else:
            # Handle processing errors
            response, status_code = ResponseFormatter.error_response(
                error=result.get("error", "Unknown error occurred during CSV processing"),
                error_type=result.get("error_type", "processing_error"),
                details={
                    "upload_id": upload_id,
                    "index_name": index_name,
                    "total_vectors_processed": result.get("total_vectors", 0)
                },
                status_code=500
            )
            return jsonify(response), status_code

    except Exception as e:
        # Handle unexpected errors with professional formatting
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="unexpected_error",
            details={
                "endpoint": "/api/upload-csv",
                "file_name": request.files.get('file', {}).filename if 'file' in request.files else None
            },
            status_code=500
        )
        return jsonify(response), status_code

@app.route('/api/upload-excel', methods=['POST'])
def upload_excel():
    """
    Endpoint to handle Excel file uploads and store in FAISS with Tamil language support.
    """
    try:
        # Import Excel processor
        from services.excel_processor import (
            validate_excel_file,
            process_excel_data,
            check_duplicate_excel_upload,
            detect_language_from_excel_data
        )

        # Validate file upload
        if 'file' not in request.files:
            response, status_code = ResponseFormatter.error_response(
                error="No file provided in the request",
                error_type="validation_error"
            )
            return jsonify(response), status_code

        file = request.files['file']

        # Validate file selection
        if file.filename == '':
            response, status_code = ResponseFormatter.error_response(
                error="No file selected for upload",
                error_type="validation_error"
            )
            return jsonify(response), status_code

        # Validate file type
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            response, status_code = ResponseFormatter.error_response(
                error="Invalid file type. Only Excel files (.xlsx, .xls) are supported",
                error_type="validation_error",
                details={"supported_formats": [".xlsx", ".xls"], "received_file": file.filename}
            )
            return jsonify(response), status_code

        # Extract and validate form parameters
        client_id = request.form.get('client_id')
        index_name = request.form.get('index_name', 'default')
        update_mode = request.form.get('update_mode', 'update')  # 'update' or 'new'
        embed_model = request.form.get('embed_model', DEFAULT_EMBED_MODEL)

        # Validate required parameters
        if not client_id:
            response, status_code = ResponseFormatter.error_response(
                error="Client ID is required for Excel upload",
                error_type="validation_error",
                details={"required_fields": ["client_id"]}
            )
            return jsonify(response), status_code

        # Validate update mode
        if update_mode not in ['update', 'new']:
            response, status_code = ResponseFormatter.error_response(
                error="Invalid update mode specified",
                error_type="validation_error",
                details={"valid_modes": ["update", "new"], "received": update_mode}
            )
            return jsonify(response), status_code

        # Log processing information
        print(f"📊 Processing Excel upload for client: {client_id}")
        print(f"📁 File: {file.filename}")
        print(f"🏷️  Index name: {index_name}")
        print(f"🔄 Update mode: {update_mode}")
        print(f"🧠 Embedding model: {embed_model}")

        # Save uploaded file temporarily
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            file.save(temp_file.name)
            temp_file_path = temp_file.name

        try:
            # Validate Excel file
            is_valid, validation_message, excel_df = validate_excel_file(temp_file_path)
            if not is_valid:
                response, status_code = ResponseFormatter.error_response(
                    error=validation_message,
                    error_type="validation_error",
                    details={"filename": file.filename}
                )
                return jsonify(response), status_code

            # Detect language for index selection
            detected_language = detect_language_from_excel_data(excel_df)

            # Use Tamil-specific index if Tamil content is detected
            if detected_language == 'Tamil' and index_name == 'default':
                index_name = 'default'
                print(f"🌏 Tamil content detected, using Tamil index: {index_name}")

            # Create or access FAISS index
            index_result = create_faiss_index(index_name, embed_model=embed_model)
            if not index_result["success"]:
                response, status_code = ResponseFormatter.error_response(
                    error=index_result.get("error", "Failed to create FAISS index"),
                    error_type=index_result.get("error_type", "index_creation_error"),
                    details={"index_name": index_name, "embedding_model": embed_model},
                    status_code=500
                )
                return jsonify(response), status_code

            # Load existing FAISS index and metadata
            faiss_index, existing_metadata, load_success = load_faiss_index(index_name)
            if not load_success:
                response, status_code = ResponseFormatter.error_response(
                    error="Failed to load FAISS index",
                    error_type="faiss_access_error",
                    status_code=500
                )
                return jsonify(response), status_code

            # Check for duplicates if in update mode
            if update_mode == 'update':
                is_duplicate = check_duplicate_excel_upload(
                    client_id, file.filename, index_name, existing_metadata
                )
                if is_duplicate:
                    response, status_code = ResponseFormatter.error_response(
                        error=f"Excel file '{file.filename}' has already been uploaded by client '{client_id}'",
                        error_type="duplicate_upload",
                        details={
                            "filename": file.filename,
                            "client_id": client_id,
                            "suggestion": "Use update_mode='new' to force re-upload"
                        }
                    )
                    return jsonify(response), status_code

            # Get embedder
            embedder = get_embedder(embed_model)

            # Process Excel data
            vectors, metadata = process_excel_data(
                excel_df, client_id, file.filename, index_name, embedder
            )

            if not vectors:
                response, status_code = ResponseFormatter.error_response(
                    error="No valid data found in Excel file to process",
                    error_type="processing_error"
                )
                return jsonify(response), status_code

            # Prepare vectors for FAISS
            vectors_array = np.vstack(vectors).astype('float32')
            faiss.normalize_L2(vectors_array)

            # Handle update modes
            if update_mode == 'new':
                # Reset index and metadata
                dimension = vectors_array.shape[1]
                faiss_index = faiss.IndexFlatIP(dimension)
                combined_metadata = metadata
                print(f"🔄 Created new index with {len(vectors)} vectors")
            else:
                # Add to existing index
                combined_metadata = existing_metadata + metadata
                print(f"🔄 Adding {len(vectors)} vectors to existing index")

            # Add vectors to index
            faiss_index.add(vectors_array)

            # Save updated index and metadata
            index_dir = os.path.join(FAISS_DATA_DIR, index_name)
            os.makedirs(index_dir, exist_ok=True)

            faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
            metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

            # Save FAISS index
            faiss.write_index(faiss_index, faiss_file_path)

            # Save metadata
            with open(metadata_file_path, 'w', encoding='utf-8') as f:
                json.dump(combined_metadata, f, ensure_ascii=False, indent=2)

            print(f"✅ Successfully saved Excel data to FAISS index: {index_name}")

            # Record Excel upload in database
            try:
                from database import record_excel_upload
                columns_info = json.dumps(list(excel_df.columns))
                embedding_dimension = len(vectors[0]) if vectors else 0

                record_success, record_message = record_excel_upload(
                    file.filename,
                    index_name,
                    client_id,
                    columns_info,
                    len(excel_df),
                    embed_model,
                    embedding_dimension,
                    detected_language,
                    len(metadata)
                )

                if record_success:
                    print(f"📝 Database record created: {record_message}")
                else:
                    print(f"⚠️ Database record failed: {record_message}")

            except Exception as db_error:
                print(f"⚠️ Database recording error: {db_error}")

            # Prepare success response
            upload_data = {
                "index_name": index_name,
                "filename": file.filename,
                "client_id": client_id,
                "vector_count": len(vectors),
                "total_rows_processed": len(excel_df),
                "embedding_model": embed_model,
                "detected_language": detected_language,
                "update_mode": update_mode,
                "chunks_created": len(metadata)
            }

            success_message = f"Successfully processed Excel file '{file.filename}' with {len(vectors)} vectors"

            metadata_info = {
                "processing_stats": {
                    "rows_processed": len(excel_df),
                    "chunks_created": len(metadata),
                    "vectors_generated": len(vectors)
                },
                "file_info": {
                    "original_filename": file.filename,
                    "detected_language": detected_language,
                    "columns": list(excel_df.columns)
                },
                "storage_info": {
                    "faiss_index_location": f"{FAISS_DATA_DIR}/{index_name}",
                    "index_type": "Tamil-specific" if index_name.endswith('-tamil') else "Default"
                }
            }

            response, status_code = ResponseFormatter.success_response(
                data=upload_data,
                message=success_message,
                metadata=metadata_info
            )

            return jsonify(response), status_code

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except Exception as e:
        # Handle unexpected errors
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="unexpected_error",
            details={
                "endpoint": "/api/upload-excel",
                "file_name": request.files.get('file', {}).filename if 'file' in request.files else None
            },
            status_code=500
        )
        return jsonify(response), status_code

@app.route('/api/list-excel-files', methods=['GET', 'POST'])
def list_excel_files():
    """
    Endpoint to list uploaded Excel files.
    """
    try:
        from database import list_excel_files as db_list_excel_files

        # Get client_id from request (GET params or POST body)
        client_id = None
        if request.method == 'GET':
            client_id = request.args.get('client_id')
        else:  # POST
            data = request.get_json() or {}
            client_id = data.get('client_id')

        print(f"📋 Listing Excel files for client: {client_id or 'ALL'}")

        # Get Excel files from database
        success, message, excel_files = db_list_excel_files(client_id)

        if not success:
            response, status_code = ResponseFormatter.error_response(
                error=message,
                error_type="database_error"
            )
            return jsonify(response), status_code

        # Prepare response data
        response_data = {
            "excel_files": excel_files,
            "total_count": len(excel_files),
            "client_id": client_id
        }

        metadata_info = {
            "query_info": {
                "client_filter": client_id or "None (showing all)",
                "total_files": len(excel_files)
            }
        }

        response, status_code = ResponseFormatter.success_response(
            data=response_data,
            message=f"Retrieved {len(excel_files)} Excel files",
            metadata=metadata_info
        )

        return jsonify(response), status_code

    except Exception as e:
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="unexpected_error",
            details={"endpoint": "/api/list-excel-files"},
            status_code=500
        )
        return jsonify(response), status_code

@app.route('/api/check-index', methods=['POST'])
def check_index():
    """
    Endpoint to check if a FAISS index exists.
    """
    try:
        # Get request data
        data = request.get_json()

        # Validate required parameters
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        index_name = data.get('index_name')
        client = data.get('client')
        embed_model = data.get('embed_model', DEFAULT_EMBED_MODEL)

        if not index_name:
            return jsonify({"success": False, "error": "Index name is required"}), 400

        if client:
            print(f"Processing request for client: {client}")
        print(f"Using embedding model: {embed_model}")

        # Check if FAISS index exists
        try:
            index_dir = os.path.join(FAISS_DATA_DIR, index_name)
            faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
            metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

            exists = os.path.exists(faiss_file_path) and os.path.exists(metadata_file_path)

            if exists:
                print(f"FAISS index '{index_name}' already exists. Using existing index...")
            else:
                print(f"FAISS index '{index_name}' does not exist. Will need to create it.")

            return jsonify({
                "success": True,
                "exists": exists,
                "index_name": index_name,
                "embedding_model": embed_model
            })

        except Exception as e:
            error_message = str(e)
            print(f"Error checking if FAISS index exists: {error_message}")
            return jsonify({
                "success": False,
                "error": f"Failed to check if index exists: {error_message}"
            }), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/list-indexes', methods=['POST'])
def list_indexes():
    """
    Endpoint to list all Pinecone indexes in the user's account.
    This endpoint is disabled as per user request.
    """
    return jsonify({
        "success": False,
        "error": "This functionality has been disabled."
    }), 404

@app.route('/api/delete-index', methods=['POST'])
def delete_index():
    """
    Endpoint to delete a Pinecone index.
    This endpoint is disabled as per user request.
    """
    return jsonify({
        "success": False,
        "error": "This functionality has been disabled."
    }), 404

@app.route('/api/delete-faiss-index', methods=['POST'])
def delete_faiss_index():
    """
    Endpoint to delete a FAISS index directory and all its files.
    This will permanently remove the index folder and all associated files.
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        index_name = data.get('index_name')

        if not index_name:
            return jsonify({
                "success": False,
                "error": "index_name is required"
            }), 400

        # Validate index name to prevent directory traversal attacks
        if not re.match(r'^[a-zA-Z0-9_-]+$', index_name):
            return jsonify({
                "success": False,
                "error": "Invalid index name. Only alphanumeric characters, underscores, and hyphens are allowed."
            }), 400

        # Prevent deletion of default index for safety
        if index_name.lower() == 'default':
            return jsonify({
                "success": False,
                "error": "Cannot delete the default index for safety reasons."
            }), 403

        # Construct the index directory path
        index_dir = os.path.join(FAISS_DATA_DIR, index_name)

        # Check if the index directory exists
        if not os.path.exists(index_dir):
            return jsonify({
                "success": False,
                "error": f"FAISS index '{index_name}' does not exist."
            }), 404

        # Check if it's actually a directory
        if not os.path.isdir(index_dir):
            return jsonify({
                "success": False,
                "error": f"'{index_name}' is not a valid index directory."
            }), 400

        # List files that will be deleted (for logging)
        files_to_delete = []
        for root, dirs, files in os.walk(index_dir):
            for file in files:
                files_to_delete.append(os.path.join(root, file))

        print(f"Attempting to delete FAISS index '{index_name}' with {len(files_to_delete)} files...")

        # Delete the entire directory and its contents
        import shutil
        shutil.rmtree(index_dir)

        print(f"Successfully deleted FAISS index directory: {index_dir}")

        return jsonify({
            "success": True,
            "message": f"Successfully deleted FAISS index '{index_name}' and all associated files.",
            "deleted_files_count": len(files_to_delete),
            "index_name": index_name
        })

    except PermissionError as e:
        error_message = f"Permission denied while deleting FAISS index: {str(e)}"
        print(error_message)
        return jsonify({
            "success": False,
            "error": error_message
        }), 403

    except FileNotFoundError as e:
        error_message = f"FAISS index files not found: {str(e)}"
        print(error_message)
        return jsonify({
            "success": False,
            "error": error_message
        }), 404

    except Exception as e:
        error_message = f"Error deleting FAISS index: {str(e)}"
        print(error_message)
        return jsonify({
            "success": False,
            "error": error_message
        }), 500

@app.route('/api/list-embedding-models', methods=['GET'])
def list_embedding_models():
    """
    Endpoint to list all available embedding models.
    """
    try:
        return jsonify({
            "success": True,
            "models": EMBEDDING_MODELS,
            "default_model": DEFAULT_EMBED_MODEL
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/list-csv-files', methods=['POST'])
def list_csv_files():
    """
    Endpoint to list all CSV files stored in the database.
    """
    try:
        # Get request data
        data = request.get_json()

        # Get client email filter if provided
        client_email = None
        if data and 'client_email' in data:
            client_email = data.get('client_email')

        # Query the database
        success, message, result = database.list_csv_files(client_email)

        if success:
            return jsonify({
                "success": True,
                "message": message,
                "files": result
            })
        else:
            return jsonify({
                "success": False,
                "error": message
            }), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/get-csv-data', methods=['POST'])
def get_csv_data():
    """
    Endpoint to retrieve CSV data from the database.
    """
    try:
        # Get request data
        data = request.get_json()

        # Validate required parameters
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        index_name = data.get('index_name')
        limit = data.get('limit', 100)
        offset = data.get('offset', 0)

        if not index_name:
            return jsonify({"success": False, "error": "Index name is required"}), 400

        # Query the database
        success, message, result = database.query_csv_data(index_name, limit, offset)

        if success:
            return jsonify({
                "success": True,
                "message": message,
                "data": result["data"],
                "columns": result["columns"],
                "total_count": result["total_count"],
                "limit": result["limit"],
                "offset": result["offset"]
            })
        else:
            return jsonify({
                "success": False,
                "error": message
            }), 404

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/upload-status', methods=['POST'])
def upload_status():
    """
    Endpoint to check the status of an ongoing upload.
    """
    try:
        # Get request data
        data = request.get_json()

        # Validate required parameters
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        upload_id = data.get('upload_id')

        if not upload_id:
            return jsonify({"success": False, "error": "Upload ID is required"}), 400

        # Check if upload exists
        with active_uploads_lock:
            if upload_id not in active_uploads:
                return jsonify({
                    "success": False,
                    "error": "Upload not found or already completed",
                    "upload_id": upload_id
                }), 404

            # Return the status
            status_data = {
                "success": True,
                "upload_id": upload_id,
                "status": active_uploads[upload_id]["status"],
                "total_rows": active_uploads[upload_id]["total_rows"],
                "processed_rows": active_uploads[upload_id]["processed_rows"],
                "total_vectors": active_uploads[upload_id]["total_vectors"],
                "index_name": active_uploads[upload_id]["index_name"],
                "cancelled": active_uploads[upload_id]["cancelled"]
            }

            # Include processing time if available
            if "processing_time" in active_uploads[upload_id]:
                status_data["processing_time"] = active_uploads[upload_id]["processing_time"]

            return jsonify(status_data)

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/cancel-upload', methods=['POST'])
def cancel_upload():
    """
    Endpoint to cancel an ongoing upload with enhanced error handling
    """
    try:
        # Get request data
        data = request.get_json()

        # Validate required parameters
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        upload_id = data.get('upload_id')

        if not upload_id:
            return jsonify({"success": False, "error": "Upload ID is required"}), 400

        with active_uploads_lock:
            # Validate upload exists and isn't already completed
            if upload_id not in active_uploads:
                return jsonify({
                    "success": False,
                    "error": "Upload not found or already completed",
                    "upload_id": upload_id
                }), 404

            # Prevent cancelling already completed/cancelled uploads
            if active_uploads[upload_id]["status"] in ["complete", "cancelled"]:
                return jsonify({
                    "success": False,
                    "error": "Upload has already completed",
                    "upload_id": upload_id
                }), 409

            # Mark as cancelled and record cancellation time
            active_uploads[upload_id]["cancelled"] = True
            active_uploads[upload_id]["cancel_time"] = time.time()
            active_uploads[upload_id]["status"] = "cancelling"

            # Calculate time since start for diagnostics
            time_since_start = time.time() - active_uploads[upload_id]["start_time"]
            print(f"Cancellation requested for upload {upload_id} after {time_since_start:.2f} seconds")

            return jsonify({
                "success": True,
                "message": "Upload cancellation initiated",
                "upload_id": upload_id,
                "processed_vectors": active_uploads[upload_id]["total_vectors"],
                "processed_rows": active_uploads[upload_id]["processed_rows"]
            })

    except Exception as e:
        error_message = f"Failed to cancel upload: {str(e)}"
        print(error_message)
        return jsonify({
            "success": False,
            "error": error_message,
            "upload_id": upload_id
        }), 500



# app.py explore backend
@app.route('/api/news')
def get_financial_news():
    # Get category from query parameters, default to 'all'
    category = request.args.get('category', 'all').lower()

    # Define category-specific search queries
    category_queries = {
        'business': 'business OR corporate OR company OR companies OR profit OR revenue OR CEO',
        'markets': 'stock OR market OR nifty OR sensex OR BSE OR NSE OR trading OR shares OR equity',
        'economy': 'economy OR GDP OR inflation OR RBI OR "reserve bank" OR fiscal OR monetary OR budget',
        'technology': 'technology OR tech OR digital OR AI OR artificial intelligence OR startup OR innovation',
        'banking': 'banking OR bank OR loan OR credit OR deposit OR finance OR NBFC OR fintech',
        'policy': 'policy OR regulation OR government OR tax OR GST OR compliance OR legal',
        'all': 'finance OR stock OR market OR economy OR investment OR banking OR nifty OR sensex OR rbi OR "reserve bank" OR rupee'
    }

    # Define category-specific keywords for better categorization
    category_keywords = {
        'business': ['business', 'corporate', 'company', 'companies', 'profit', 'revenue', 'ceo', 'earnings', 'quarterly', 'annual report'],
        'markets': ['stock', 'market', 'nifty', 'sensex', 'bse', 'nse', 'trading', 'shares', 'equity', 'bull', 'bear', 'rally', 'correction'],
        'economy': ['economy', 'gdp', 'inflation', 'rbi', 'reserve bank', 'fiscal', 'monetary', 'budget', 'growth', 'recession', 'economic'],
        'technology': ['tech', 'technology', 'digital', 'ai', 'artificial intelligence', 'startup', 'innovation', 'software', 'hardware', 'app'],
        'banking': ['bank', 'banking', 'loan', 'credit', 'deposit', 'finance', 'nbfc', 'fintech', 'interest rate', 'mortgage', 'lending'],
        'policy': ['policy', 'regulation', 'government', 'tax', 'gst', 'compliance', 'legal', 'law', 'bill', 'parliament', 'ministry']
    }

    # If category is not 'all', use a more specific query to get better results
    if category != 'all':
        # Use the category-specific query
        query = category_queries[category]

        # Add more specific keywords to improve relevance
        params = {
            'apiKey': NEWS_API_KEY,
            'q': query,
            'pageSize': 100,
            'language': 'en',
            'sortBy': 'publishedAt',
            'domains': 'economictimes.indiatimes.com,moneycontrol.com,livemint.com,business-standard.com,financialexpress.com,ndtv.com/business,cnbctv18.com'
        }
    else:
        # For 'all' category, use the general query
        params = {
            'apiKey': NEWS_API_KEY,
            'q': category_queries['all'],
            'pageSize': 100,
            'language': 'en',
            'sortBy': 'publishedAt',
            'domains': 'economictimes.indiatimes.com,moneycontrol.com,livemint.com,business-standard.com,financialexpress.com,ndtv.com/business,cnbctv18.com'
        }

    print(f"Fetching news for category: {category} with query: {params['q']}")
    response = requests.get("https://newsapi.org/v2/everything", params=params)
    data = response.json()
    financial_news = []

    # Track how many articles we've assigned to each category
    category_counts = {cat: 0 for cat in category_queries.keys()}

    if data.get('status') == 'ok':
        print(f"Total Indian financial articles received: {len(data.get('articles', []))}")

        for article in data.get('articles', []):
            # Only include articles with images
            if not article.get('urlToImage'):
                continue

            # Get article content
            title = article.get('title', '').lower()
            description = article.get('description', '').lower()
            content = title + ' ' + description

            # Determine the article category based on keyword matching
            article_category = 'all'  # Default category
            max_matches = 0

            # For each category, count how many keywords match
            for cat, keywords in category_keywords.items():
                matches = sum(1 for keyword in keywords if keyword in content)
                if matches > max_matches:
                    max_matches = matches
                    article_category = cat

            # If we're fetching a specific category, ensure articles match that category
            if category != 'all':
                # If we're fetching a specific category, force that category
                article_category = category

            # Increment the count for this category
            category_counts[article_category] += 1

            item = {
                'title': article.get('title'),
                'summary': article.get('description'),
                'url': article.get('url'),
                'image_url': article.get('urlToImage'),
                'published': article.get('publishedAt'),
                'source': article.get('source', {}).get('name'),
                'category': article_category
            }

            # Format date if it exists
            if item['published']:
                try:
                    dt = datetime.fromisoformat(item['published'].replace('Z', '+00:00'))
                    item['formatted_date'] = dt.strftime('%B %d, %Y')
                except:
                    item['formatted_date'] = item['published']

            financial_news.append(item)

    # Print category distribution
    print(f"Category distribution: {category_counts}")

    # If we're fetching 'all' categories, ensure we have a good mix
    if category == 'all':
        # Sort by category to group articles
        financial_news.sort(key=lambda x: x['category'])

        # Take up to 2 articles from each category to ensure diversity
        balanced_news = []
        for cat in category_queries.keys():
            if cat == 'all':
                continue  # Skip the 'all' category itself

            cat_news = [news for news in financial_news if news['category'] == cat]
            balanced_news.extend(cat_news[:2])

        # If we still need more articles to reach 10, add more from any category
        if len(balanced_news) < 10:
            remaining_news = [news for news in financial_news if news not in balanced_news]
            balanced_news.extend(remaining_news[:10 - len(balanced_news)])

        financial_news = balanced_news[:10]
    else:
        # For specific categories, just take the top 10
        financial_news = financial_news[:10]

    # Return the list of categories along with the news
    categories = list(category_queries.keys())
    return jsonify({
        'news': financial_news,
        'categories': categories,
        'current_category': category
    })


#suggest.py /financial_querry endpoint

# Initialize Pinecone client
try:
    if Pinecone is not None:
        financial_pc = Pinecone(api_key=FINANCIAL_PINECONE_API_KEY)
        financial_index = financial_pc.Index(FINANCIAL_INDEX_NAME)
        print(f"Successfully initialized Pinecone with index: {FINANCIAL_INDEX_NAME}")
    else:
        print("Pinecone not available - using FAISS only")
        financial_pc = None
        financial_index = None
except Exception as e:
    print(f"Error initializing Pinecone: {e}")
    financial_index = None
    financial_pc = None
embedder = HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")
deepseek_client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url="https://api.deepseek.com")

# Dictionary to store client-specific Pinecone clients and indexes
client_pinecone_instances = {}

def retrieve_from_pinecone(query, k=5, api_key=None, index_name=None):
    """
    Retrieve documents from Pinecone based on a query.
    Can use either the default financial index or a client-specific index.

    Args:
        query (str): The query text
        k (int): Number of results to return
        api_key (str, optional): Client-specific Pinecone API key
        index_name (str, optional): Client-specific Pinecone index name

    Returns:
        list: Matching documents
    """
    print(f"🔍 RETRIEVE_FROM_PINECONE CALLED:")
    print(f"   - Query: '{query[:50]}...'")
    print(f"   - API Key provided: {'Yes' if api_key else 'No'}")
    print(f"   - API Key (first 10): {api_key[:10] if api_key else 'None'}...")
    print(f"   - Index Name provided: {'Yes' if index_name else 'No'}")
    print(f"   - Index Name: {index_name}")

    # Check if Pinecone is available
    if Pinecone is None:
        print("❌ Pinecone not available - cannot perform Pinecone query")
        return []

    query_vector = embedder.embed_documents([query])[0]

    # Use client-specific configuration if provided
    if api_key and index_name:
        print(f"✅ USING CLIENT-SPECIFIC CONFIGURATION with index: '{index_name}'")

        # Create a unique key for this client configuration
        client_key = f"{api_key}:{index_name}"

        # Check if we already have an instance for this client
        if client_key not in client_pinecone_instances:
            print(f"No cached instance found for {client_key}, creating new instance")
            try:
                # Validate API key format and create new Pinecone client
                api_key = api_key.strip()  # Remove any whitespace

                # Create new Pinecone client and index for this client
                print(f"Initializing Pinecone client with API key: {api_key[:5]}...{api_key[-5:]}")
                client_pc = Pinecone(api_key=api_key)

                # Check if index exists
                try:
                    existing_indexes = client_pc.list_indexes().names()
                    print(f"Available indexes for this API key: {existing_indexes}")

                    if index_name not in existing_indexes:
                        print(f"Warning: Index '{index_name}' does not exist for this API key. Will attempt to use it anyway.")
                except Exception as e:
                    print(f"Warning: Could not list indexes: {e}")

                # Create index instance
                print(f"Creating index instance for: {index_name}")
                client_index = client_pc.Index(index_name)

                # Store in cache
                client_pinecone_instances[client_key] = {
                    "pc": client_pc,
                    "index": client_index
                }
                print(f"Successfully created new Pinecone instance for index: {index_name}")
            except Exception as e:
                print(f"Error creating Pinecone instance: {e}")
                print(f"Error details: {type(e).__name__}")

                # DO NOT fall back to default index when specific index is requested
                print(f"❌ FAILED TO CREATE CLIENT-SPECIFIC INDEX: {index_name}")
                print(f"   - Error: {e}")
                print(f"   - Will NOT fall back to default index as specific index was requested")
                return []

        # Use the client-specific index
        try:
            print(f"Using cached Pinecone instance for index: {index_name}")
            client_data = client_pinecone_instances[client_key]
            client_index = client_data["index"]

            # Perform query
            print(f"Querying index '{index_name}' with vector of dimension {len(query_vector)}")
            results = client_index.query(
                vector=query_vector,
                top_k=k,
                namespace=NAMESPACE,
                include_metadata=True
            )
            print(f"Query successful, found {len(results.matches)} matches")
            return results.matches
        except Exception as e:
            print(f"Error querying client-specific index: {e}")
            print(f"Error type: {type(e).__name__}")

            # Try to recreate the client instance in case it expired
            try:
                print(f"Attempting to recreate Pinecone client instance for index: {index_name}")
                client_pc = Pinecone(api_key=api_key)

                # Check if index exists before trying to use it
                try:
                    existing_indexes = client_pc.list_indexes().names()
                    print(f"Available indexes: {existing_indexes}")
                    if index_name not in existing_indexes:
                        print(f"Warning: Index '{index_name}' does not exist. Will attempt to use it anyway.")
                except Exception as list_error:
                    print(f"Warning: Could not list indexes during retry: {list_error}")

                client_index = client_pc.Index(index_name)
                client_pinecone_instances[client_key] = {
                    "pc": client_pc,
                    "index": client_index
                }

                # Try query again
                print(f"Retrying query to index '{index_name}'")
                results = client_index.query(
                    vector=query_vector,
                    top_k=k,
                    namespace=NAMESPACE,
                    include_metadata=True
                )
                print(f"Retry successful, found {len(results.matches)} matches")
                return results.matches
            except Exception as retry_error:
                print(f"Error on retry: {retry_error}")
                print(f"Error type: {type(retry_error).__name__}")

                # DO NOT fall back to default index when specific index is requested
                print(f"❌ RETRY FAILED FOR CLIENT-SPECIFIC INDEX: {index_name}")
                print(f"   - Retry Error: {retry_error}")
                print(f"   - Will NOT fall back to default index as specific index was requested")
                return []

    # Use default financial index if available
    print("❌ FALLBACK TO DEFAULT: No client-specific configuration provided, using default financial index")
    print(f"   - This should NOT happen if API key and index were passed correctly!")
    print(f"   - API Key check: api_key={api_key is not None}, value={api_key[:10] if api_key else 'None'}...")
    print(f"   - Index Name check: index_name={index_name is not None}, value={index_name}")

    if financial_index:
        try:
            print(f"Querying default financial index with vector of dimension {len(query_vector)}")
            results = financial_index.query(
                vector=query_vector,
                top_k=k,
                namespace=NAMESPACE,
                include_metadata=True
            )
            print(f"Default index query successful, found {len(results.matches)} matches")
            return results.matches
        except Exception as e:
            print(f"Error querying default financial index: {e}")
            print(f"Error type: {type(e).__name__}")
            return []
    else:
        print("Default financial index not available")
        return []

def generate_response(query, context_docs, selected_language="English"):
    # Handle both FAISS (dict) and Pinecone (object) formats
    context_texts = []
    source_info = []

    for doc in context_docs:
        if isinstance(doc, dict):
            # FAISS format
            metadata = doc.get('metadata', {})
            text = metadata.get('chunk_text', '')
            source_type = metadata.get('source_type', 'unknown')
            url = metadata.get('url', '')
            title = metadata.get('title', 'Unknown')
        else:
            # Pinecone format
            text = doc.metadata.get('chunk_text', '')
            source_type = doc.metadata.get('source_type', 'unknown')
            url = doc.metadata.get('url', '')
            title = doc.metadata.get('title', 'Unknown')

        context_texts.append(text)

        # Collect unique source information
        source_entry = {
            'type': source_type,
            'url': url,
            'title': title
        }
        if source_entry not in source_info:
            source_info.append(source_entry)

    context = "\n\n".join(context_texts)

    # Create source attribution text
    source_attribution = ""
    if source_info:
        source_attribution = "\n\nSOURCES USED:\n"
        for i, source in enumerate(source_info, 1):
            source_type_display = {
                'article': 'Article',
                'youtube': 'YouTube Video',
                'pdf': 'PDF Document',
                'document': 'Document',
                'audio': 'Audio File'
            }.get(source['type'], source['type'].title())

            source_attribution += f"{i}. {source_type_display}: {source['title']}\n"
            if source['url'] and not source['url'].startswith('file://'):
                source_attribution += f"   URL: {source['url']}\n"

    # Create language-specific system prompt and user prompt
    if selected_language == "Tamil":
        system_prompt = """நீங்கள் ஒரு உதவிகரமான உதவியாளர். கேள்விகளுக்கு பதிலளிக்கும் போது, உங்கள் பதிலில் நீங்கள் பயன்படுத்தும் ஆதாரங்களை குறிப்பிடுங்கள் (எ.கா., 'கட்டுரையின் படி...', 'யூடியூப் வீடியோவின் அடிப்படையில்...', 'ஆவணத்தில் குறிப்பிட்டுள்ளபடி...').

முக்கியம்: உங்கள் பதில் முழுவதும் தமிழில் மட்டுமே இருக்க வேண்டும். ஆங்கிலத்தில் பதில் கொடுக்க வேண்டாம்."""

        user_prompt = f"""கொடுக்கப்பட்ட தகவலின் அடிப்படையில் பின்வரும் கேள்விக்கு பதிலளியுங்கள். உங்கள் பதிலில் ஆதாரங்களை குறிப்பிடுவதை உறுதி செய்யுங்கள்.

சூழல்:
{context}
{source_attribution}

கேள்வி: {query}

குறிப்பு: உங்கள் பதில் முழுவதும் தமிழில் மட்டுமே இருக்க வேண்டும்."""
    else:
        system_prompt = "You are a helpful assistant. When answering questions, reference the sources you're using by mentioning them in your response (e.g., 'According to the article...', 'Based on the YouTube video...', 'As mentioned in the document...')."

        user_prompt = f"""Answer the following question based on this information. Make sure to reference the sources in your response when appropriate.

CONTEXT:
{context}
{source_attribution}

QUESTION: {query}
"""

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    try:
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            stream=False
        )
        return response.choices[0].message.content
    except Exception as e:
        # Handle DeepSeek/OpenAI errors gracefully, especially insufficient balance (402)
        error_msg = str(e)
        if '402' in error_msg or 'Insufficient Balance' in error_msg or 'Payment Required' in error_msg:
            return "[Error: The AI service is currently unavailable due to insufficient balance. Please contact the administrator or try again later.]"
        return f"[Error: Could not generate AI response. Details: {error_msg}]"

def generate_related_questions(query, answer, selected_language="English"):
    """
    Generate related questions using DeepSeek API.

    Args:
        query: The original question
        answer: The AI-generated answer
        selected_language: The language for generating questions (English/Tamil)

    Returns:
        List of related questions (up to 7 questions)
    """
    # Check if DeepSeek API key is available
    if not DEEPSEEK_API_KEY:
        print("⚠️ DEEPSEEK_API_KEY not found in environment variables")
        if selected_language == "Tamil":
            return ["இந்த தகவலின் முக்கிய அம்சங்கள் என்ன?",
                    "இந்த தலைப்பு பற்றி மேலும் விவரங்கள் தர முடியுமா?",
                    "இந்த தகவலின் தாக்கங்கள் என்ன?",
                    "இது தற்போதைய போக்குகளுடன் எவ்வாறு தொடர்புடையது?",
                    "இதன் சாத்தியமான நன்மைகள் மற்றும் தீமைகள் என்ன?",
                    "என்ன கூடுதல் ஆராய்ச்சி உதவியாக இருக்கும்?",
                    "இந்த தகவலை நடைமுறையில் எவ்வாறு பயன்படுத்தலாம்?"]
        else:
            return ["What are the key points from this information?",
                    "Can you provide more details about this topic?",
                    "What are the implications of this information?",
                    "How does this relate to current trends?",
                    "What are the potential benefits and drawbacks?",
                    "What additional research would be helpful?",
                    "How can this information be applied practically?"]

    # Create language-specific prompts
    if selected_language == "Tamil":
        prompt = f"""
கொடுக்கப்பட்ட கேள்வி மற்றும் பதிலின் அடிப்படையில், 7 சூழல் சார்ந்த தொடர்ச்சி கேள்விகளை உருவாக்கவும். கேள்விகள் தமிழில் மட்டுமே இருக்க வேண்டும்.

கேள்வி: {query}
பதில்: {answer}

தொடர்புடைய கேள்விகள்:
1.
"""
    else:
        prompt = f"""
Based on the following question and answer, generate 7 contextually relevant follow-up questions.

QUESTION: {query}
ANSWER: {answer}

RELATED QUESTIONS:
1.
"""

    messages = [{"role": "user", "content": prompt}]
    try:
        print(f"🤖 Generating related questions using DeepSeek API in {selected_language}...")
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            max_tokens=300,
            temperature=0.7
        )
        raw_text = response.choices[0].message.content
        print(f"✅ DeepSeek API response received: {len(raw_text)} characters")

        lines = raw_text.strip().split('\n')
        questions = [
            re.sub(r"^\d+\.\s*", "", line).strip()
            for line in lines
            if re.search(r'\?', line) and line.strip()
        ]

        # Filter out empty questions and ensure we have good questions
        filtered_questions = [q for q in questions if len(q) > 10 and q.endswith('?')]

        if filtered_questions:
            print(f"✅ Generated {len(filtered_questions)} related questions in {selected_language}")
            return filtered_questions[:7]
        else:
            print(f"⚠️ No valid questions found in DeepSeek response, using {selected_language} fallback")
            if selected_language == "Tamil":
                return ["இந்த தகவலின் முக்கிய அம்சங்கள் என்ன?",
                        "இந்த தலைப்பு பற்றி மேலும் விவரங்கள் தர முடியுமா?",
                        "இந்த தகவலின் தாக்கங்கள் என்ன?",
                        "இது தற்போதைய போக்குகளுடன் எவ்வாறு தொடர்புடையது?",
                        "இதன் சாத்தியமான நன்மைகள் மற்றும் தீமைகள் என்ன?",
                        "என்ன கூடுதல் ஆராய்ச்சி உதவியாக இருக்கும்?",
                        "இந்த தகவலை நடைமுறையில் எவ்வாறு பயன்படுத்தலாம்?"]
            else:
                return ["What are the key points from this information?",
                        "Can you provide more details about this topic?",
                        "What are the implications of this information?",
                        "How does this relate to current trends?",
                        "What are the potential benefits and drawbacks?",
                        "What additional research would be helpful?",
                        "How can this information be applied practically?"]

    except Exception as e:
        error_msg = str(e)
        print(f"❌ Error generating related questions: {error_msg}")

        # Check for specific error types
        if '401' in error_msg or 'Unauthorized' in error_msg:
            print("🔑 API key appears to be invalid")
            return ["Invalid API key - please check configuration"]
        elif '402' in error_msg or 'Insufficient Balance' in error_msg or 'Payment Required' in error_msg:
            print("💳 Insufficient balance in DeepSeek account")
            return ["API service unavailable - insufficient balance"]
        elif 'timeout' in error_msg.lower() or 'connection' in error_msg.lower():
            print("🌐 Network connectivity issue")
            return ["Network error - please try again"]
        else:
            # Provide fallback questions for any other errors
            print(f"🔄 Using fallback questions due to error: {error_msg}")
            if selected_language == "Tamil":
                return ["இந்த தகவலின் முக்கிய அம்சங்கள் என்ன?",
                        "இந்த தலைப்பு பற்றி மேலும் விவரங்கள் தர முடியுமா?",
                        "இந்த தகவலின் தாக்கங்கள் என்ன?",
                        "இது தற்போதைய போக்குகளுடன் எவ்வாறு தொடர்புடையது?",
                        "இதன் சாத்தியமான நன்மைகள் மற்றும் தீமைகள் என்ன?",
                        "என்ன கூடுதல் ஆராய்ச்சி உதவியாக இருக்கும்?",
                        "இந்த தகவலை நடைமுறையில் எவ்வாறு பயன்படுத்தலாம்?"]
            else:
                return ["What are the key points from this information?",
                        "Can you provide more details about this topic?",
                        "What are the implications of this information?",
                        "How does this relate to current trends?",
                        "What are the potential benefits and drawbacks?",
                        "What additional research would be helpful?",
                        "How can this information be applied practically?"]

def extract_sentences(text):
    return re.split(r'(?<=[.!?])\s+', text.strip())

def enrich_ai_response_with_urls(ai_response, api_key=None, index_name=None, use_faiss=True):
    sentences = extract_sentences(ai_response)
    enriched = []

    print(f"🔍 ENRICHMENT DEBUG: Starting URL enrichment with {len(sentences)} sentences")
    print(f"   - API Key provided: {'Yes' if api_key else 'No'}")
    print(f"   - Index Name provided: {'Yes' if index_name else 'No'}")
    print(f"   - Index Name: {index_name}")
    print(f"   - Use FAISS: {use_faiss}")

    for i, sentence in enumerate(sentences):
        if not sentence.strip():
            continue

        print(f"🔍 ENRICHMENT SENTENCE {i+1}: Processing sentence: '{sentence[:50]}...'")

        # Use the same search method as the main query
        if use_faiss:
            print(f"   - Calling retrieve_from_faiss_query with index_name={index_name}")
            matches = retrieve_from_faiss_query(sentence, index_name or "default", k=1)
        else:
            print(f"   - Calling retrieve_from_pinecone with api_key={api_key[:10] if api_key else None}..., index_name={index_name}")
            matches = retrieve_from_pinecone(sentence, k=1, api_key=api_key, index_name=index_name)

        if matches:
            # Handle both FAISS (dict) and Pinecone (object) formats
            if isinstance(matches[0], dict):
                # FAISS format
                top_match = matches[0].get('metadata', {})
            else:
                # Pinecone format
                top_match = matches[0].metadata

            # Get source type and create appropriate display
            source_type = top_match.get("source_type", "unknown")
            source_title = top_match.get("title", "Unknown")
            source_url = top_match.get("url", "N/A")

            # Create source-specific summary
            source_type_display = {
                'article': 'Article',
                'youtube': 'YouTube Video',
                'pdf': 'PDF Document',
                'document': 'Document',
                'audio': 'Audio File'
            }.get(source_type, source_type.title())

            # Create enhanced summary with source information
            enhanced_summary = f"Source: {source_type_display}"
            if source_title and source_title != "Unknown":
                enhanced_summary += f" - {source_title}"

            # Add original summary if available
            original_summary = top_match.get("summary", "")
            if original_summary and original_summary != "N/A":
                enhanced_summary += f" | {original_summary}"

            enriched.append({
                "sentence": sentence,
                "url": source_url,
                "summary": enhanced_summary,
                "source_type": source_type,
                "source_title": source_title
            })
            print(f"   ✅ Found match for sentence {i+1} - Source: {source_type_display}")
        else:
            enriched.append({
                "sentence": sentence,
                "url": "Not found",
                "summary": "No summary found",
                "source_type": "unknown",
                "source_title": "Unknown"
            })
            print(f"   ❌ No match found for sentence {i+1}")

    print(f"🔍 ENRICHMENT COMPLETE: Processed {len(enriched)} sentences")
    return enriched

@app.route('/financial_query', methods=['POST'])
def handle_query():
    data = request.get_json()
    query = data.get("query", "").strip()

    # Get client-specific configuration if provided in the request
    api_key = data.get("api_key")
    requested_index_name = data.get("index_name")  # Store the originally requested index
    client_email = data.get("client_email")

    # Get language preference and translation settings
    selected_language = data.get("language", "English")
    target_language = data.get("target_language")  # Language for response translation
    enable_translation = data.get("enable_translation", False)

    # Import translation service
    try:
        from services.translation_service import translation_service
        translation_available = True
    except ImportError:
        print("⚠️ Translation service not available")
        translation_available = False

    # Import language-aware processor
    try:
        from services.language_aware_processor import language_aware_processor
        language_aware_available = True
        print("✅ Language-aware processor available")
    except ImportError:
        print("⚠️ Language-aware processor not available")
        language_aware_available = False

    # Initialize language-aware processing variables
    original_query = query
    language_processing_result = None

    # Use language-aware processor if available
    if language_aware_available:
        print("🚀 USING LANGUAGE-AWARE PROCESSOR")

        # Create a search function wrapper for the language-aware processor
        def search_wrapper(search_query, **kwargs):
            """Wrapper function for search operations with API fallback for corrupted data."""
            # Use translated query for search if available
            search_index = requested_index_name or "default"

            # Try FAISS first, prioritizing uploaded content if recent uploads exist
            matches = retrieve_from_faiss_query(search_query, search_index, k=5, prioritize_uploads=has_recent_uploads)

            # Check if FAISS results contain corrupted data
            if matches:
                corrupted_patterns = ["கொழுப்பு", "தகவல்தொடர்புகள்", "குறும்படம் கேலக்", "எக்ஸெல்:", "வெளியீடு:"]
                for match in matches:
                    metadata = match.get('metadata', {})
                    chunk_text = metadata.get('chunk_text', '')
                    if any(pattern in chunk_text for pattern in corrupted_patterns):
                        print(f"🚨 CORRUPTED DATA DETECTED in FAISS results, switching to API fallback...")
                        matches = []  # Clear corrupted results
                        break

            # If no clean FAISS results, try API-based search
            if not matches:
                print(f"🌐 Using API-based search for clean Tamil content...")
                api_matches = search_tamil_content_via_api(search_query)
                if api_matches:
                    matches = api_matches
                elif api_key:
                    print(f"❌ No API results, trying Pinecone fallback...")
                    matches = retrieve_from_pinecone(search_query, api_key=api_key, index_name=search_index)

            return matches or []

        # Process query with language awareness
        try:
            language_processing_result = language_aware_processor.process_query_with_language_awareness(
                query=query,
                index_name=requested_index_name,
                search_function=search_wrapper
            )

            # Extract results from language processing
            if language_processing_result.get('success'):
                matches = language_processing_result.get('search_results', [])
                query_language_info = language_processing_result.get('query_language_info', {})
                selected_language = query_language_info.get('language', 'English')

                # Update query variables based on language processing
                if language_processing_result.get('query_translated'):
                    translated_query = language_processing_result.get('translated_query', query)
                    query_translated = True
                else:
                    translated_query = query
                    query_translated = False

                print(f"✅ Language-aware processing completed successfully")
                print(f"   - Detected Language: {selected_language}")
                print(f"   - Processing Strategy: {language_processing_result.get('processing_strategy', 'unknown')}")
                print(f"   - Results Found: {len(matches) if matches else 0}")

            else:
                print("⚠️ Language-aware processing failed, falling back to legacy method")
                language_processing_result = None

        except Exception as e:
            print(f"❌ Error in language-aware processing: {e}")
            language_processing_result = None

    # Fallback to legacy language detection if language-aware processor is not available or failed
    if not language_aware_available or language_processing_result is None:
        print("🔄 USING LEGACY LANGUAGE PROCESSING")

        # Auto-detect language from query text if not explicitly set
        def is_tamil_text(text):
            """Check if text contains Tamil characters"""
            import re
            # Tamil Unicode range: \u0B80-\u0BFF
            tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
            return bool(tamil_pattern.search(text))

        def is_telugu_text(text):
            """Check if text contains Telugu characters"""
            import re
            # Telugu Unicode range: \u0C00-\u0C7F
            telugu_pattern = re.compile(r'[\u0C00-\u0C7F]')
            return bool(telugu_pattern.search(text))

        def is_kannada_text(text):
            """Check if text contains Kannada characters"""
            import re
            # Kannada Unicode range: \u0C80-\u0CFF
            kannada_pattern = re.compile(r'[\u0C80-\u0CFF]')
            return bool(kannada_pattern.search(text))

        # Auto-detect language if not explicitly set
        if selected_language not in ["Tamil", "Telugu", "Kannada"]:
            if is_tamil_text(query):
                selected_language = "Tamil"
                print(f"🔍 AUTO-DETECTED TAMIL TEXT in query: '{query[:50]}...'")
                print(f"🌏 LANGUAGE AUTO-DETECTION: Switching to Tamil language processing")
            elif is_telugu_text(query):
                selected_language = "Telugu"
                print(f"🔍 AUTO-DETECTED TELUGU TEXT in query: '{query[:50]}...'")
                print(f"🌏 LANGUAGE AUTO-DETECTION: Switching to Telugu language processing")
            elif is_kannada_text(query):
                selected_language = "Kannada"
                print(f"🔍 AUTO-DETECTED KANNADA TEXT in query: '{query[:50]}...'")
                print(f"🌏 LANGUAGE AUTO-DETECTION: Switching to Kannada language processing")

        # Store original query for translation
        translated_query = query
        query_translated = False

        # Translate query to English for better search results
        if selected_language in ["Tamil", "Telugu", "Kannada"] and translation_available:
            try:
                # Determine source language code
                source_lang = "ta" if selected_language == "Tamil" else "te" if selected_language == "Telugu" else "kn"
                print(f"🌐 Translating {selected_language} query to English: '{query[:50]}...'")

                # Extract continuous capital English words that should not be translated
                import re
                capital_words_matches = re.findall(r'\b[A-Z]{2,}\b', query)
                capital_words = []

                # Create placeholders for capital words
                for i, word in enumerate(capital_words_matches):
                    placeholder = f"__CAPITAL_WORD_{i}__"
                    capital_words.append((word, placeholder))
                    query = query.replace(word, placeholder)

                # Translate the query
                translation_result = translation_service.translate_text(query, "en", source_lang)
                if translation_result and translation_result.get('translated_text'):
                    translated_query = translation_result['translated_text']
                    query_translated = True

                    # Restore capital words
                    for word, placeholder in capital_words:
                        translated_query = translated_query.replace(placeholder, word)

                    print(f"✅ Query translated to English: '{translated_query[:50]}...'")
                else:
                    print("⚠️ Query translation failed, using original query")
                    translated_query = original_query

            except Exception as e:
                print(f"❌ Error translating query: {str(e)}")
                translated_query = original_query

    # Get upload context information
    upload_context = data.get("upload_context", "")
    has_recent_uploads = data.get("has_recent_uploads", False)

    # Enhanced logging for request analysis
    print(f"🔍 FINANCIAL QUERY REQUEST ANALYSIS:")
    print(f"   - Original Query: {original_query[:50]}...")
    print(f"   - Translated Query: {translated_query[:50]}...")
    print(f"   - Language: {selected_language}")
    print(f"   - Query Translated: {query_translated}")
    print(f"   - API Key provided: {'Yes' if api_key else 'No'}")
    print(f"   - API Key (first 10 chars): {api_key[:10] if api_key else 'None'}...")
    print(f"   - Index Name provided: {'Yes' if requested_index_name else 'No'}")
    print(f"   - Requested Index Name: {requested_index_name}")
    print(f"   - Client Email: {client_email if client_email else 'Not provided'}")

    # Initialize index_name with the requested value
    index_name = requested_index_name

    # Handle Tamil language selection - always route to Tamil FAISS index for Tamil queries
    if selected_language == "Tamil":
        # For Tamil language, always use the Tamil index unless explicitly overridden
        if requested_index_name and requested_index_name != "default":
            # If user specifically requested a non-default index, respect that choice
            print(f"🌏 TAMIL LANGUAGE with specific non-default index: Using requested index '{requested_index_name}'")
        else:
            # For Tamil language, default to Tamil index
            index_name = "default"
            print(f"🌏 TAMIL LANGUAGE DETECTED: Routing to Tamil FAISS index '{index_name}'")

    # Log the request details
    if api_key and requested_index_name:
        print(f"✅ USING CLIENT-SPECIFIC CONFIGURATION:")
        print(f"   - Target Index: {requested_index_name}")
        print(f"   - API Key: {api_key[:10]}...")
    elif client_email and not requested_index_name:
        # Only fetch from PINE collection if no specific index was requested
        print(f"🔄 ATTEMPTING TO FETCH CONFIGURATION FROM PINE COLLECTION:")
        print(f"   - Client Email: {client_email}")
        # Fetch configuration from PINE collection based on client email
        try:
            # Make a request to the PINE collection API
            response = requests.get(
                "https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE",
                headers={
                    "Content-Type": "application/json",
                    "xxxid": "PINE"
                }
            )

            if response.ok:
                pine_data = response.json()
                if pine_data.get("statusCode") == 200 and pine_data.get("source"):
                    # Parse each item in the source array (they are JSON strings)
                    pine_entries = [json.loads(item) for item in pine_data["source"]]

                    # Find the entry where client matches the user's email
                    user_entry = next((item for item in pine_entries if
                                      item.get("client") and
                                      item.get("client").strip().lower() == client_email.strip().lower()), None)

                    if user_entry and user_entry.get("api_key") and user_entry.get("index_name"):
                        if not api_key:  # Only use stored API key if none provided
                            api_key = user_entry["api_key"]
                        if not index_name:  # Only use stored index if none requested
                            index_name = user_entry["index_name"]
                        print(f"Found matching PINE data for user: {client_email}")
                    else:
                        print(f"No matching PINE data found for user: {client_email}")
        except Exception as e:
            print(f"Error fetching PINE collection data: {e}")
    elif requested_index_name:
        print(f"🎯 USING EXPLICITLY REQUESTED INDEX: {requested_index_name}")
        print("   - Skipping PINE collection lookup as specific index was requested")
    else:
        print("Received query with default configuration")

    # Final logging of what will be used
    print(f"🔧 FINAL CONFIGURATION:")
    print(f"   - Index to use: {index_name}")
    print(f"   - API Key available: {'Yes' if api_key else 'No'}")

    if not query:
        return jsonify({"error": "Query is required."}), 400

    # Use translated query for search if available
    search_query = translated_query if query_translated else query

    # Determine whether to use FAISS or Pinecone based on index_name
    matches = []
    use_faiss = True  # Default to FAISS for new system

    # Check if we already have results from language-aware processing
    if language_processing_result and language_processing_result.get('success') and language_processing_result.get('search_results'):
        matches = language_processing_result['search_results']
        print(f"✅ Using results from language-aware processing: {len(matches)} matches found")
    else:
        # Use default index if no index is specified, considering language preference
        if not index_name:
            if selected_language == "Tamil":
                index_name = "default"
                print("🎯 No specific index provided, using DEFAULT TAMIL FAISS index")
            else:
                index_name = "default"
                print("🎯 No specific index provided, using DEFAULT FAISS index")

        if index_name:
            print(f"🎯 USING FAISS INDEX: {index_name}")

            # Validate user access to the index if client email is provided
            # Default index is accessible to all users, only validate access for other indexes
            if client_email and index_name != 'default':
                try:
                    # Import the filter service
                    from pine_filter_service import PineFilterService

                    # Check if user has access to this index
                    has_access, access_message = PineFilterService.validate_user_access(client_email, index_name)

                    if not has_access:
                        error_msg = f"Access denied to index '{index_name}'. {access_message}"
                        print(f"❌ {error_msg}")
                        return jsonify({
                            "error": error_msg,
                            "error_type": "access_denied",
                            "user_email": client_email,
                            "index_name": index_name
                        }), 403

                    print(f"✅ User {client_email} has access to index '{index_name}'")

                except Exception as e:
                    print(f"⚠️ Error validating user access: {e}")
                    # Continue without access validation if there's an error
            elif index_name == 'default':
                print(f"✅ User {client_email or 'anonymous'} accessing default index (allowed for all users)")
            else:
                print(f"⚠️ No client email provided for access validation")

            # Try FAISS first, prioritizing uploaded content if recent uploads exist
            matches = retrieve_from_faiss_query(search_query, index_name, k=5, prioritize_uploads=has_recent_uploads)

            if not matches:
                print(f"❌ No results from FAISS index '{index_name}', trying Pinecone fallback...")
                # Fallback to Pinecone if FAISS doesn't work
                matches = retrieve_from_pinecone(search_query, api_key=api_key, index_name=index_name)
                use_faiss = False

    if not matches:
        error_msg = f"No matching documents found in {'FAISS' if use_faiss else 'Pinecone'} index"
        if index_name:
            error_msg += f" '{index_name}'"
        error_msg += ". Please try a different query or select another index."

        return jsonify({
            "error": error_msg
        }), 404

    retrieved_docs = []
    for i, match in enumerate(matches):
        # Handle both FAISS (dict) and Pinecone (object) match formats
        if isinstance(match, dict):
            # FAISS format
            metadata = match.get('metadata', {})
            score = round(match.get('score', 0) * 100, 2)
        else:
            # Pinecone format
            metadata = match.metadata
            score = round(match.score * 100, 2)

        retrieved_docs.append({
            "rank": i + 1,
            "score": f"{score}%",
            "date": metadata.get('record_date', metadata.get('upload_timestamp', 'Unknown')),
            "category": metadata.get('category', 'N/A'),
            "text": metadata.get('chunk_text', 'No text')
        })

    # Generate AI response using the search query (translated if Tamil)
    ai_response = generate_response(search_query, matches, selected_language)
    enriched_sentences = enrich_ai_response_with_urls(ai_response, api_key=api_key, index_name=index_name, use_faiss=use_faiss)
    related_questions = generate_related_questions(search_query, ai_response, selected_language)

    # Analyze data sources in retrieved documents
    upload_sources = []
    has_uploaded_content = False

    for doc in retrieved_docs:
        # Check if this document came from uploaded content
        if any(match.get('metadata', {}).get('upload_source') for match in matches
               if match.get('metadata', {}).get('chunk_text') == doc.get('text')):
            has_uploaded_content = True
            # Extract source information
            for match in matches:
                meta = match.get('metadata', {})
                if meta.get('chunk_text') == doc.get('text') and meta.get('upload_source'):
                    source_info = meta.get('url', meta.get('title', 'Unknown source'))
                    if source_info not in upload_sources:
                        upload_sources.append(source_info)

    # Prepare the response
    response_data = {
        "query": original_query,  # Always return the original query
        "translated_query": translated_query if query_translated else None,  # Include translated query if applicable
        "retrieved_documents": retrieved_docs,
        "ai_response": ai_response,
        "sentence_analysis": enriched_sentences,
        "related_questions": related_questions,
        "api_environment": API_ENVIRONMENT,
        "index_used": index_name if index_name else "default",
        "search_engine": "FAISS" if use_faiss else "Pinecone",
        "has_uploaded_content": has_uploaded_content,
        "upload_sources": upload_sources,
        "prioritized_uploads": has_recent_uploads,
        "query_translated": query_translated
    }

    # Add language-aware processing metadata if available
    if language_processing_result:
        response_data["language_processing"] = {
            "enabled": True,
            "query_language": language_processing_result.get('query_language_info', {}).get('language', 'Unknown'),
            "csv_language": language_processing_result.get('csv_language_info', {}).get('language', 'Unknown'),
            "processing_strategy": language_processing_result.get('processing_strategy', 'unknown'),
            "direct_processing_used": language_processing_result.get('use_direct_processing', False),
            "translations_performed": language_processing_result.get('translations_performed', []),
            "processing_duration_ms": language_processing_result.get('processing_duration_ms', 0),
            "success": language_processing_result.get('success', False)
        }

        # Add language confidence scores if available
        if language_processing_result.get('query_language_info'):
            response_data["language_processing"]["query_language_confidence"] = language_processing_result['query_language_info'].get('confidence', 0)

        if language_processing_result.get('csv_language_info'):
            response_data["language_processing"]["csv_language_confidence"] = language_processing_result['csv_language_info'].get('confidence', 0)
    else:
        response_data["language_processing"] = {
            "enabled": False,
            "fallback_method": "legacy_detection"
        }

    # Apply translation if requested and available
    if translation_available and target_language and enable_translation:
        try:
            print(f"🌐 Translating response to {target_language}")

            # Detect query language for better translation context
            query_language = translation_service.detect_language(original_query)

            # Translate the response data
            translated_response = translation_service.translate_response_data(
                response_data,
                target_language
            )

            # Update response data with translated content
            response_data.update(translated_response)

            # Add translation metadata
            response_data["translation_applied"] = True
            response_data["query_language"] = query_language
            response_data["target_language"] = target_language

            print(f"✅ Translation completed: {query_language} -> {target_language}")

        except Exception as e:
            print(f"❌ Translation failed: {str(e)}")
            response_data["translation_error"] = str(e)
            response_data["translation_applied"] = False
    else:
        response_data["translation_applied"] = False

    # Add available FAISS indexes to response
    try:
        available_indexes = []
        default_index_exists = False

        if os.path.exists(FAISS_DATA_DIR):
            for item in os.listdir(FAISS_DATA_DIR):
                item_path = os.path.join(FAISS_DATA_DIR, item)
                if os.path.isdir(item_path):
                    faiss_file = os.path.join(item_path, f"{item}.faiss")
                    json_file = os.path.join(item_path, f"{item}.json")

                    # Check standard naming
                    if os.path.exists(faiss_file) and os.path.exists(json_file):
                        if item == "default":
                            default_index_exists = True
                        else:
                            available_indexes.append(item)
                    # Check legacy naming for default and Tamil indexes
                    elif item in ["default", "default"]:
                        legacy_faiss_file = os.path.join(item_path, "news_index.faiss")
                        legacy_json_file = os.path.join(item_path, "news_metadata.json")
                        if os.path.exists(legacy_faiss_file) and os.path.exists(legacy_json_file):
                            if item == "default":
                                default_index_exists = True
                            else:
                                available_indexes.append(item)

        # Always put 'default' first if it exists
        if default_index_exists:
            available_indexes.insert(0, "default")
        elif not available_indexes:
            available_indexes = ["default"]  # Fallback to default

        if available_indexes:
            response_data["faiss_categories"] = available_indexes
            response_data["default_index"] = "default"
    except Exception as e:
        print(f"Error listing FAISS indexes: {e}")

    # If in development mode and API key is provided, try to get the list of Pinecone indexes
    if API_ENVIRONMENT == "development" and api_key:
        try:
            pc = Pinecone(api_key=api_key)
            indexes = pc.list_indexes().names()
            response_data["pinecone_indexes"] = indexes
        except Exception as e:
            print(f"Error listing Pinecone indexes: {e}")
            # Don't fail the whole request if we can't get indexes
            response_data["pinecone_indexes_error"] = str(e)

    return jsonify(response_data)

@app.route('/index', methods=['POST'])
def index():
    """
    Endpoint to list all Pinecone indexes for a given API key.
    Only available in development mode.
    """
    indexes = []
    error = None

    if API_ENVIRONMENT != "development":
        return jsonify({
            "error": "This endpoint is only available in development mode."
        }), 403

    if request.method == 'POST':
        data = request.get_json() or {}
        api_key = data.get('api_key') or request.form.get('api_key')

        if not api_key:
            return jsonify({
                "error": "API key is required."
            }), 400

        try:
            pc = Pinecone(api_key=api_key)
            indexes = pc.list_indexes().names()
        except Exception as e:
            error = str(e)
            return jsonify({
                "error": f"Failed to list indexes: {error}"
            }), 500

    return jsonify({
        "api_environment": API_ENVIRONMENT,
        "indexes": indexes
    })

@app.route('/api/list-categories', methods=['POST'])
def list_categories():
    """
    Endpoint to list all categories (FAISS indexes) for UI display with enhanced email filtering.
    """
    try:
        # Get request data
        data = request.get_json()

        # Get email filter if provided
        email = None
        if data and 'email' in data:
            email = data.get('email')

        # Query the database with enhanced filtering
        success, message, result = database.list_pine_categories(email)

        if success:
            return jsonify({
                "success": True,
                "message": message,
                "categories": result,
                "filtered_by_email": email is not None,
                "user_email": email if email else None
            })
        else:
            return jsonify({
                "success": False,
                "error": message
            }), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/user-indices', methods=['POST'])
def get_user_indices():
    """
    Endpoint to get all PINE collection indices for a specific user.
    Ensures data isolation by returning only indices belonging to the authenticated user.
    """
    try:
        # Get request data
        data = request.get_json()

        if not data or 'email' not in data:
            return jsonify({
                "success": False,
                "error": "Email is required"
            }), 400

        email = data.get('email')
        print(f"🔍 Getting indices for user: {email}")

        # Import the filter service
        from pine_filter_service import PineFilterService

        # Get user's indices with enhanced filtering
        success, message, indices = PineFilterService.get_user_indices(email)

        print(f"📊 Filter service result: success={success}, indices={indices}")

        if success:
            return jsonify({
                "success": True,
                "message": message,
                "indices": indices,
                "count": len(indices),
                "user_email": email
            })
        else:
            return jsonify({
                "success": False,
                "error": message,
                "indices": [],
                "count": 0,
                "user_email": email
            }), 200  # Return 200 instead of 400 for better debugging

    except Exception as e:
        print(f"❌ Error in get_user_indices: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/validate-user-access', methods=['POST'])
def validate_user_access():
    """
    Endpoint to validate if a user has access to a specific index.
    """
    try:
        # Get request data
        data = request.get_json()

        if not data or 'email' not in data or 'index_name' not in data:
            return jsonify({
                "success": False,
                "error": "Email and index_name are required"
            }), 400

        email = data.get('email')
        index_name = data.get('index_name')

        # Import the filter service
        from pine_filter_service import PineFilterService

        # Validate user access
        has_access, message = PineFilterService.validate_user_access(email, index_name)

        return jsonify({
            "success": True,
            "has_access": has_access,
            "message": message,
            "user_email": email,
            "index_name": index_name
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/debug-pine-collection', methods=['GET'])
def debug_pine_collection():
    """
    Debug endpoint to see what's in the PINE collection database.
    """
    try:
        conn = database.get_connection()
        cursor = conn.cursor()

        # Get all records from pine_collection
        cursor.execute("SELECT id, index_name, email, upload_date FROM pine_collection ORDER BY upload_date DESC")
        rows = cursor.fetchall()

        records = []
        for row in rows:
            records.append({
                "id": row[0],
                "index_name": row[1],
                "email": row[2],
                "upload_date": row[3]
            })

        conn.close()

        return jsonify({
            "success": True,
            "total_records": len(records),
            "records": records
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/migrate-user-indexes', methods=['POST'])
def migrate_user_indexes():
    """
    Migration endpoint to associate existing FAISS indexes with a user's email.
    This helps populate the PINE collection for users who had indexes before the filtering system.
    """
    try:
        data = request.get_json()

        if not data or 'email' not in data:
            return jsonify({
                "success": False,
                "error": "Email is required"
            }), 400

        email = data.get('email')
        selected_indexes = data.get('indexes', [])  # Optional: specific indexes to migrate

        # Validate email
        if not email or not email.strip():
            return jsonify({
                "success": False,
                "error": "Valid email is required"
            }), 400

        # Get all available FAISS indexes
        available_indexes = []
        if os.path.exists(FAISS_DATA_DIR):
            for item in os.listdir(FAISS_DATA_DIR):
                item_path = os.path.join(FAISS_DATA_DIR, item)
                if os.path.isdir(item_path):
                    # Check if both .faiss and .json files exist
                    faiss_file = os.path.join(item_path, f"{item}.faiss")
                    json_file = os.path.join(item_path, f"{item}.json")

                    if item == "default":
                        # Check legacy naming for default
                        legacy_faiss_file = os.path.join(item_path, "news_index.faiss")
                        legacy_json_file = os.path.join(item_path, "news_metadata.json")
                        if os.path.exists(faiss_file) and os.path.exists(json_file):
                            available_indexes.append(item)
                        elif os.path.exists(legacy_faiss_file) and os.path.exists(legacy_json_file):
                            available_indexes.append(item)
                    elif item == "default":
                        # Check legacy naming for Tamil index
                        legacy_faiss_file = os.path.join(item_path, "news_index.faiss")
                        legacy_json_file = os.path.join(item_path, "news_metadata.json")
                        if os.path.exists(legacy_faiss_file) and os.path.exists(legacy_json_file):
                            available_indexes.append(item)
                    else:
                        if os.path.exists(faiss_file) and os.path.exists(json_file):
                            available_indexes.append(item)

        # If specific indexes were provided, filter to only those
        if selected_indexes:
            indexes_to_migrate = [idx for idx in selected_indexes if idx in available_indexes]
        else:
            indexes_to_migrate = available_indexes

        # Migrate indexes to PINE collection
        migrated_count = 0
        errors = []

        for index_name in indexes_to_migrate:
            try:
                success, message = database.add_pine_category(index_name, email)
                if success:
                    migrated_count += 1
                    print(f"✅ Migrated index '{index_name}' for user {email}")
                else:
                    errors.append(f"Failed to migrate {index_name}: {message}")
            except Exception as e:
                errors.append(f"Error migrating {index_name}: {str(e)}")

        return jsonify({
            "success": True,
            "message": f"Migration completed for user {email}",
            "migrated_count": migrated_count,
            "total_available": len(available_indexes),
            "migrated_indexes": indexes_to_migrate[:migrated_count],
            "errors": errors if errors else None
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

def search_tamil_content_via_api(query: str, k: int = 5) -> List[Dict]:
    """
    Search for Tamil content using external APIs when local data is corrupted.

    Args:
        query: Search query in Tamil
        k: Number of results to return

    Returns:
        List of match dictionaries in FAISS format
    """
    try:
        import requests
        import json
        from datetime import datetime

        # Try Wikipedia API for Tamil content
        print(f"🔍 Searching Tamil Wikipedia for: {query[:50]}...")

        # Search Tamil Wikipedia
        search_url = "https://ta.wikipedia.org/api/rest_v1/page/search"
        params = {
            'q': query,
            'limit': k
        }

        response = requests.get(search_url, params=params, timeout=10)

        if response.status_code == 200:
            data = response.json()
            results = []

            for i, page in enumerate(data.get('pages', [])[:k]):
                # Get page content
                page_title = page.get('title', 'Unknown')
                page_url = f"https://ta.wikipedia.org/wiki/{page_title.replace(' ', '_')}"

                # Get page summary
                summary_url = f"https://ta.wikipedia.org/api/rest_v1/page/summary/{page_title}"
                try:
                    summary_response = requests.get(summary_url, timeout=5)
                    if summary_response.status_code == 200:
                        summary_data = summary_response.json()
                        content = summary_data.get('extract', page.get('snippet', ''))
                    else:
                        content = page.get('snippet', '')
                except:
                    content = page.get('snippet', '')

                # Create FAISS-compatible result
                match = {
                    'score': 0.9 - (i * 0.1),  # Decreasing relevance score
                    'metadata': {
                        'chunk_text': content,
                        'source_type': 'wikipedia',
                        'url': page_url,
                        'title': page_title,
                        'record_date': datetime.now().strftime('%Y-%m-%d'),
                        'category': 'Tamil Wikipedia',
                        'upload_source': 'api_search'
                    }
                }
                results.append(match)

            print(f"✅ Found {len(results)} Tamil Wikipedia results")
            return results

    except Exception as e:
        print(f"❌ Error in API search: {e}")

    # Fallback: Create a helpful response indicating API search was attempted
    fallback_result = {
        'score': 0.5,
        'metadata': {
            'chunk_text': f'API தேடல் முயற்சி செய்யப்பட்டது: "{query}". தற்போது உள்ளூர் தரவுத்தளத்தில் சிக்கல் உள்ளது.',
            'source_type': 'system',
            'url': 'N/A',
            'title': 'API தேடல் முயற்சி',
            'record_date': datetime.now().strftime('%Y-%m-%d'),
            'category': 'System Message',
            'upload_source': 'api_fallback'
        }
    }

    return [fallback_result]

def retrieve_from_faiss_query(query: str, index_name: str, k: int = 5, prioritize_uploads: bool = False) -> List[Dict]:
    """
    Search the FAISS index with a query and return top-k matches.
    Optionally prioritize recently uploaded content.

    Args:
        query: Search query
        index_name: Name of the FAISS index
        k: Number of results to return
        prioritize_uploads: Whether to boost scores for recently uploaded content

    Returns:
        List of match dictionaries
    """
    try:
        # Load FAISS index and metadata
        faiss_index, metadata_store, success = load_faiss_index(index_name)
        if not success or faiss_index is None or not metadata_store:
            return []

        # Get embedder (use default model for now)
        embedder = get_embedder(DEFAULT_EMBED_MODEL)

        # Embed the query
        query_vector = embedder.embed_documents([query])[0]
        query_embedding = np.array([query_vector]).astype("float32")

        # Normalize for cosine similarity
        faiss.normalize_L2(query_embedding)

        # Search the index
        distances, indices = faiss_index.search(query_embedding, k)

        results = []
        for rank, idx in enumerate(indices[0]):
            if idx < len(metadata_store) and idx >= 0:
                meta = metadata_store[idx]
                base_score = float(distances[0][rank])

                # Boost score for recently uploaded content if prioritization is enabled
                if prioritize_uploads and meta.get("upload_source"):
                    # Check if this is from a recent upload (has upload_source field)
                    upload_sources = ["youtube_upload", "article_upload", "pdf_upload", "document_upload", "audio_upload"]
                    if meta.get("upload_source") in upload_sources:
                        # Boost the score by 20% for uploaded content
                        base_score = base_score * 1.2
                        print(f"📈 Boosted score for uploaded content: {meta.get('source_type', 'unknown')} - {base_score:.4f}")

                # Create a match object similar to Pinecone's structure
                match = {
                    'score': base_score,
                    'metadata': {
                        'chunk_text': meta.get("chunk_text", ""),
                        'upload_timestamp': meta.get("upload_timestamp", "Unknown"),
                        'row_idx': meta.get("row_idx", 0),
                        'chunk_idx': meta.get("chunk_idx", 0),
                        'embedding_model': meta.get("embedding_model", "Unknown")
                    }
                }
                # Add any additional metadata fields
                for key, value in meta.items():
                    if key not in match['metadata']:
                        match['metadata'][key] = value

                results.append(match)

        # Sort results by score (descending) if prioritization was applied
        if prioritize_uploads:
            results.sort(key=lambda x: x['score'], reverse=True)

        return results

    except Exception as e:
        print(f"Error retrieving from FAISS index {index_name}: {e}")
        return []

@app.route('/api/list-faiss-indexes', methods=['GET', 'POST'])
def list_faiss_indexes():
    """
    Endpoint to list available FAISS indexes, optionally filtered by user email.
    Supports both GET (all indexes) and POST (user-filtered indexes).
    Always includes 'default' as the first option if it exists and user has access.
    """
    try:
        user_email = None

        # Check if this is a POST request with user email for filtering
        if request.method == 'POST':
            data = request.get_json()
            if data and 'email' in data:
                user_email = data.get('email')
                print(f"🔍 Filtering FAISS indexes for user: {user_email}")

        # Get all available indexes from filesystem
        all_available_indexes = []
        default_index_exists = False

        # Check if FAISS data directory exists
        if os.path.exists(FAISS_DATA_DIR):
            # List all subdirectories in FAISS_DATA_DIR
            for item in os.listdir(FAISS_DATA_DIR):
                item_path = os.path.join(FAISS_DATA_DIR, item)
                if os.path.isdir(item_path):
                    # Check if both .faiss and .json files exist
                    faiss_file = os.path.join(item_path, f"{item}.faiss")
                    json_file = os.path.join(item_path, f"{item}.json")

                    # Special handling for default and Tamil indexes with legacy file names
                    if item == "default":
                        # Check standard naming first
                        if os.path.exists(faiss_file) and os.path.exists(json_file):
                            default_index_exists = True
                            all_available_indexes.append(item)
                        else:
                            # Check legacy naming
                            legacy_faiss_file = os.path.join(item_path, "news_index.faiss")
                            legacy_json_file = os.path.join(item_path, "news_metadata.json")
                            if os.path.exists(legacy_faiss_file) and os.path.exists(legacy_json_file):
                                default_index_exists = True
                                all_available_indexes.append(item)
                                print(f"Found default index with legacy file names")
                    elif item == "default":
                        # Tamil index uses legacy naming convention
                        legacy_faiss_file = os.path.join(item_path, "news_index.faiss")
                        legacy_json_file = os.path.join(item_path, "news_metadata.json")
                        if os.path.exists(legacy_faiss_file) and os.path.exists(legacy_json_file):
                            all_available_indexes.append(item)
                            print(f"Found Tamil index with legacy file names")
                    else:
                        # For other indexes, use standard naming
                        if os.path.exists(faiss_file) and os.path.exists(json_file):
                            all_available_indexes.append(item)

        # If user email is provided, filter indexes by user access
        if user_email:
            try:
                # Import the filter service
                from pine_filter_service import PineFilterService

                # Get user's accessible indexes
                success, message, user_indexes = PineFilterService.get_user_indices(user_email)

                if success:
                    # Filter filesystem indexes to only include user's accessible ones
                    user_specific_indexes = [idx for idx in all_available_indexes if idx in user_indexes]
                    print(f"📊 User {user_email} has access to {len(user_specific_indexes)} user-specific indexes: {user_specific_indexes}")

                    # Always include 'default' index for all users (even new accounts)
                    available_indexes = user_specific_indexes.copy()
                    if default_index_exists and 'default' not in available_indexes:
                        available_indexes.append('default')
                        print(f"✅ Added default index for user {user_email}")

                else:
                    print(f"❌ Failed to get user indexes: {message}")
                    # For new users or on error, provide at least the default index
                    available_indexes = ['default'] if default_index_exists else []

            except Exception as e:
                print(f"❌ Error filtering indexes by user: {e}")
                # For new users or on error, provide at least the default index
                available_indexes = ['default'] if default_index_exists else []
        else:
            # No user filtering - return all available indexes
            available_indexes = all_available_indexes

        # Always put 'default' first if it exists
        if default_index_exists and 'default' in available_indexes:
            available_indexes.remove('default')
            available_indexes.insert(0, "default")

        # Ensure we always have at least the default index if it exists
        if not available_indexes and default_index_exists:
            available_indexes = ["default"]

        print(f"📋 Final available FAISS indexes: {available_indexes}")
        print(f"🎯 Default index exists: {default_index_exists}")
        print(f"👤 User filtering: {'Yes' if user_email else 'No'}")

        return jsonify({
            "success": True,
            "indexes": available_indexes,
            "total_count": len(available_indexes),
            "default_index": "default",
            "filtered_by_user": user_email is not None,
            "user_email": user_email
        })

    except Exception as e:
        print(f"Error in list_faiss_indexes endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/query-faiss', methods=['POST'])
def query_faiss():
    """
    Endpoint to query FAISS index with a search query using professional response formatting.
    """
    try:
        # Get and validate request data
        data = request.get_json()

        if not data:
            response, status_code = ResponseFormatter.error_response(
                error="Request body is required",
                error_type="validation_error"
            )
            return jsonify(response), status_code

        # Extract and validate parameters
        query = data.get('query', '').strip()
        index_name = data.get('index_name', '').strip()
        k = data.get('k', 5)

        # Validate query
        if not query:
            response, status_code = ResponseFormatter.error_response(
                error="Search query is required",
                error_type="validation_error",
                details={"required_fields": ["query"]}
            )
            return jsonify(response), status_code

        # Validate index name
        if not index_name:
            response, status_code = ResponseFormatter.error_response(
                error="Index name is required",
                error_type="validation_error",
                details={"required_fields": ["index_name"]}
            )
            return jsonify(response), status_code

        # Validate user access to the index
        user_email = data.get('user_email', '').strip()
        if user_email and index_name != 'default':
            # Default index is accessible to all users, only validate access for other indexes
            try:
                # Import the filter service
                from pine_filter_service import PineFilterService

                # Check if user has access to this index
                has_access, access_message = PineFilterService.validate_user_access(user_email, index_name)

                if not has_access:
                    response, status_code = ResponseFormatter.error_response(
                        error=f"Access denied to index '{index_name}'. {access_message}",
                        error_type="access_denied",
                        details={
                            "user_email": user_email,
                            "index_name": index_name,
                            "suggestion": "Please use an index you have access to or contact an administrator"
                        }
                    )
                    return jsonify(response), status_code

                print(f"✅ User {user_email} has access to index '{index_name}'")

            except Exception as e:
                print(f"⚠️ Error validating user access: {e}")
                # Continue without access validation if there's an error
        elif index_name == 'default':
            print(f"✅ User {user_email or 'anonymous'} accessing default index (allowed for all users)")
        else:
            print(f"⚠️ No user email provided for access validation")

        # Validate k parameter
        try:
            k = int(k)
            if k <= 0 or k > 100:
                raise ValueError("k must be between 1 and 100")
        except (ValueError, TypeError):
            response, status_code = ResponseFormatter.error_response(
                error="Invalid value for 'k' parameter",
                error_type="validation_error",
                details={"valid_range": "1-100", "received": k}
            )
            return jsonify(response), status_code

        print(f"🔍 Searching FAISS index '{index_name}' for query: '{query}' (top {k} results)")

        # Search FAISS index
        results = retrieve_from_faiss_query(query, index_name, k)

        # Prepare search results data
        search_data = {
            "query": query,
            "index_name": index_name,
            "results": results,
            "total_results": len(results),
            "search_parameters": {
                "top_k": k,
                "search_type": "semantic_similarity"
            }
        }

        # Prepare metadata
        metadata = {
            "search_stats": {
                "results_found": len(results),
                "search_time": "< 1 second",  # Could be enhanced with actual timing
                "index_type": "FAISS"
            },
            "result_quality": {
                "highest_score": max([r.get('score', 0) for r in results]) if results else 0,
                "lowest_score": min([r.get('score', 0) for r in results]) if results else 0,
                "average_score": sum([r.get('score', 0) for r in results]) / len(results) if results else 0
            }
        }

        message = f"Found {len(results)} relevant results for your search query"
        if len(results) == 0:
            message = "No results found for your search query. Try using different keywords or check the index name."

        response, status_code = ResponseFormatter.success_response(
            data=search_data,
            message=message,
            metadata=metadata
        )

        return jsonify(response), status_code

    except Exception as e:
        # Handle search errors professionally
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="search_error",
            details={
                "endpoint": "/api/query-faiss",
                "query": data.get('query', 'N/A') if 'data' in locals() else 'N/A',
                "index_name": data.get('index_name', 'N/A') if 'data' in locals() else 'N/A'
            },
            status_code=500
        )
        return jsonify(response), status_code

# Enhanced upload endpoints for ChatInputUpload integration
@app.route('/api/process_youtube', methods=['POST'])
def process_youtube_enhanced():
    """Enhanced YouTube URL processing endpoint with PINE collection integration"""
    try:
        data = request.get_json()
        url = data.get('url', '').strip()
        index_name = data.get('index_name', 'default')
        client_email = data.get('client_email', '')

        if not url:
            return jsonify({'success': False, 'error': 'URL is required'})

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Import and use YouTube processor
        try:
            from services.youtube_processor import process_youtube_url
            success = process_youtube_url(url, index_name=index_name)
        except ImportError:
            return jsonify({'success': False, 'error': 'YouTube processor not available'})

        if success:
            # Store in PINE collection if client email provided
            if client_email:
                try:
                    db_success, db_message = database.add_pine_category(
                        index_name=index_name,
                        email=client_email
                    )
                    if db_success:
                        print(f"✅ Stored YouTube processing in PINE collection: {db_message}")
                except Exception as e:
                    print(f"⚠️ Failed to store in PINE collection: {e}")

            return jsonify({
                'success': True,
                'message': 'YouTube video processed successfully',
                'upload_id': upload_id,
                'index_name': index_name
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to process YouTube video'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_article', methods=['POST'])
def process_article_enhanced():
    """Enhanced Article URL processing endpoint with PINE collection integration"""
    try:
        data = request.get_json()
        url = data.get('url', '').strip()
        index_name = data.get('index_name', 'default')
        client_email = data.get('client_email', '')

        if not url:
            return jsonify({'success': False, 'error': 'URL is required'})

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Import and use Article processor
        try:
            from services.article_processor import process_article_url
            success = process_article_url(url, index_name=index_name)
        except ImportError:
            return jsonify({'success': False, 'error': 'Article processor not available'})

        if success:
            # Store in PINE collection if client email provided
            if client_email:
                try:
                    db_success, db_message = database.add_pine_category(
                        index_name=index_name,
                        email=client_email
                    )
                    if db_success:
                        print(f"✅ Stored Article processing in PINE collection: {db_message}")
                except Exception as e:
                    print(f"⚠️ Failed to store in PINE collection: {e}")

            return jsonify({
                'success': True,
                'message': 'Article processed successfully',
                'upload_id': upload_id,
                'index_name': index_name
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to process article'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_pdf', methods=['POST'])
def process_pdf_enhanced():
    """Enhanced PDF file processing endpoint with PINE collection integration - No temporary files"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No PDF file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No PDF file selected'})

        # Get additional parameters
        index_name = request.form.get('index_name', 'default')
        client_email = request.form.get('client_email', '')

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext != '.pdf':
            return jsonify({
                'success': False,
                'error': f'Only PDF files are supported. Got: {file_ext}'
            })

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Read file content directly into memory (no temporary file)
        print(f"📄 Reading PDF file content: {file.filename}")
        file_content = file.read()

        if not file_content:
            return jsonify({'success': False, 'error': 'Empty PDF file'})

        print(f"📊 PDF file size: {len(file_content)} bytes")

        try:
            # Import and use PDF processor with file content
            try:
                from services.pdf_processor import process_pdf_file
                success = process_pdf_file(
                    file_content=file_content,
                    original_filename=file.filename,
                    index_name=index_name
                )
            except ImportError:
                return jsonify({'success': False, 'error': 'PDF processor not available'})

            if success:
                # Store in PINE collection if client email provided
                if client_email:
                    try:
                        db_success, db_message = database.add_pine_category(
                            index_name=index_name,
                            email=client_email
                        )
                        if db_success:
                            print(f"✅ Stored PDF processing in PINE collection: {db_message}")
                    except Exception as e:
                        print(f"⚠️ Failed to store in PINE collection: {e}")

                return jsonify({
                    'success': True,
                    'message': 'PDF processed and indexed successfully (no temp files)',
                    'upload_id': upload_id,
                    'index_name': index_name,
                    'filename': file.filename
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to process PDF'})

        except Exception as processing_error:
            print(f"❌ Error during PDF processing: {processing_error}")
            return jsonify({'success': False, 'error': f'PDF processing failed: {str(processing_error)}'})

    except Exception as e:
        print(f"❌ General error in PDF endpoint: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_audio', methods=['POST'])
def process_audio_enhanced():
    """Enhanced Audio file processing endpoint with PINE collection integration"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No audio file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No audio file selected'})

        # Get additional parameters
        index_name = request.form.get('index_name', 'default')
        client_email = request.form.get('client_email', '')

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        supported_audio_formats = {'.mp3', '.wav', '.m4a', '.flac', '.ogg'}
        if file_ext not in supported_audio_formats:
            return jsonify({
                'success': False,
                'error': f'Unsupported audio format: {file_ext}. Supported: {list(supported_audio_formats)}'
            })

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Save uploaded file temporarily
        temp_filename = f"temp_{upload_id}_{file.filename}"
        temp_dir = os.path.join(FAISS_DATA_DIR, "temp")
        os.makedirs(temp_dir, exist_ok=True)
        temp_path = os.path.join(temp_dir, temp_filename)

        # Save the file
        file.save(temp_path)

        try:
            # Import and use Audio processor
            try:
                from services.audio_proccessor import process_audio_file
                success = process_audio_file(temp_path, original_filename=file.filename, index_name=index_name)
            except ImportError:
                return jsonify({'success': False, 'error': 'Audio processor not available'})

            if success:
                # Store in PINE collection if client email provided
                if client_email:
                    try:
                        db_success, db_message = database.add_pine_category(
                            index_name=index_name,
                            email=client_email
                        )
                        if db_success:
                            print(f"✅ Stored Audio processing in PINE collection: {db_message}")
                    except Exception as e:
                        print(f"⚠️ Failed to store in PINE collection: {e}")

                return jsonify({
                    'success': True,
                    'message': 'Audio file processed and indexed successfully',
                    'upload_id': upload_id,
                    'index_name': index_name,
                    'filename': file.filename
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to process audio file'})

        finally:
            # Clean up temporary file
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except Exception as e:
                print(f"Warning: Could not remove temporary file {temp_path}: {e}")

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_document', methods=['POST'])
def process_document_enhanced():
    """Enhanced Document processing endpoint with PINE collection integration"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'})

        # Get additional parameters
        index_name = request.form.get('index_name', 'default')
        client_email = request.form.get('client_email', '')

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        supported_extensions = {'.doc', '.docx', '.txt', '.rtf'}
        if file_ext not in supported_extensions:
            return jsonify({
                'success': False,
                'error': f'Unsupported file type: {file_ext}. Supported: {list(supported_extensions)}'
            })

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Save uploaded file temporarily
        temp_filename = f"temp_{upload_id}_{file.filename}"
        temp_dir = os.path.join(FAISS_DATA_DIR, "temp")
        os.makedirs(temp_dir, exist_ok=True)
        temp_path = os.path.join(temp_dir, temp_filename)

        # Save the file
        file.save(temp_path)

        try:
            # Import and use Document processor
            try:
                from services.document_processor import process_document_file
                success = process_document_file(temp_path, original_filename=file.filename, index_name=index_name)
            except ImportError:
                return jsonify({'success': False, 'error': 'Document processor not available'})

            if success:
                # Store in PINE collection if client email provided
                if client_email:
                    try:
                        db_success, db_message = database.add_pine_category(
                            index_name=index_name,
                            email=client_email
                        )
                        if db_success:
                            print(f"✅ Stored Document processing in PINE collection: {db_message}")
                    except Exception as e:
                        print(f"⚠️ Failed to store in PINE collection: {e}")

                return jsonify({
                    'success': True,
                    'message': 'Document processed and indexed successfully',
                    'upload_id': upload_id,
                    'index_name': index_name,
                    'filename': file.filename
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to process document'})

        finally:
            # Clean up temporary file
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except Exception as e:
                print(f"Warning: Could not remove temporary file {temp_path}: {e}")

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Data Management API Endpoints
@app.route('/api/get-faiss-metadata/<index_name>', methods=['GET'])
def get_faiss_metadata(index_name):
    """
    Endpoint to get metadata (JSON data) for a specific FAISS index.
    """
    try:
        print(f"📊 Fetching metadata for index: {index_name}")
        print(f"📁 FAISS_DATA_DIR: {FAISS_DATA_DIR}")

        # Load the metadata for the specified index
        index_dir = os.path.join(FAISS_DATA_DIR, index_name)
        print(f"📂 Index directory: {index_dir}")
        print(f"📂 Directory exists: {os.path.exists(index_dir)}")

        metadata_file_path = os.path.join(index_dir, f"{index_name}.json")
        print(f"📄 Metadata file path: {metadata_file_path}")
        print(f"📄 File exists: {os.path.exists(metadata_file_path)}")

        # Special handling for default index with legacy file names
        if index_name == "default" and not os.path.exists(metadata_file_path):
            legacy_metadata_file = os.path.join(index_dir, "news_metadata.json")
            print(f"📄 Legacy metadata file path: {legacy_metadata_file}")
            print(f"📄 Legacy file exists: {os.path.exists(legacy_metadata_file)}")
            if os.path.exists(legacy_metadata_file):
                metadata_file_path = legacy_metadata_file
                print(f"Using legacy metadata file for default index")

        if not os.path.exists(metadata_file_path):
            # List directory contents for debugging
            if os.path.exists(index_dir):
                files_in_dir = os.listdir(index_dir)
                print(f"📋 Files in directory: {files_in_dir}")
            return jsonify({
                "success": False,
                "error": f"Metadata file not found for index '{index_name}'. Checked: {metadata_file_path}"
            }), 404

        # Load and return the metadata
        with open(metadata_file_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)

        # Transform metadata to include row IDs
        transformed_data = []
        for i, item in enumerate(metadata):
            row_data = {
                "id": f"row_{i}",
                **item
            }
            transformed_data.append(row_data)

        return jsonify({
            "success": True,
            "data": transformed_data,
            "total": len(transformed_data),
            "index_name": index_name
        })

    except Exception as e:
        print(f"Error fetching metadata for index {index_name}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/delete-faiss-rows/<index_name>', methods=['DELETE'])
def delete_faiss_rows(index_name):
    """
    Endpoint to delete specific rows from a FAISS index.
    """
    try:
        data = request.get_json()
        if not data or 'row_ids' not in data:
            return jsonify({
                "success": False,
                "error": "row_ids parameter is required"
            }), 400

        row_ids = data['row_ids']
        if not isinstance(row_ids, list):
            return jsonify({
                "success": False,
                "error": "row_ids must be a list"
            }), 400

        print(f"🗑️ Deleting {len(row_ids)} rows from index: {index_name}")

        # Load the current index and metadata
        faiss_index, metadata_store, success = load_faiss_index(index_name)
        if not success:
            return jsonify({
                "success": False,
                "error": f"Failed to load index '{index_name}'"
            }), 404

        # Extract row indices from row_ids (assuming format "row_X")
        indices_to_delete = []
        for row_id in row_ids:
            if isinstance(row_id, str) and row_id.startswith("row_"):
                try:
                    index_num = int(row_id.split("_")[1])
                    if 0 <= index_num < len(metadata_store):
                        indices_to_delete.append(index_num)
                except (ValueError, IndexError):
                    continue

        if not indices_to_delete:
            return jsonify({
                "success": False,
                "error": "No valid row indices found"
            }), 400

        # Sort indices in descending order to avoid index shifting issues
        indices_to_delete.sort(reverse=True)

        # Remove metadata entries
        original_count = len(metadata_store)
        for index in indices_to_delete:
            if 0 <= index < len(metadata_store):
                del metadata_store[index]

        deleted_count = original_count - len(metadata_store)

        # Save the updated metadata (simplified approach)
        save_success = save_faiss_index(index_name, faiss_index, metadata_store)
        if not save_success:
            return jsonify({
                "success": False,
                "error": "Failed to save updated index"
            }), 500

        return jsonify({
            "success": True,
            "deleted_count": deleted_count,
            "remaining_count": len(metadata_store)
        })

    except Exception as e:
        print(f"Error deleting rows from index {index_name}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/multilingual_financial_query', methods=['POST'])
def multilingual_financial_query():
    """
    Enhanced endpoint for Telugu/Kannada financial queries with translation flow:
    1. Detect language of incoming query
    2. Translate Telugu/Kannada query to English
    3. Send English query to financial_query endpoint
    4. Translate English response back to original language
    5. Return translated response
    """
    try:
        data = request.get_json() or {}
        original_query = data.get('query', '').strip()
        
        if not original_query:
            return jsonify({
                "success": False,
                "error": "Query is required"
            }), 400

        # Import translation service
        try:
            from services.translation_service import TranslationService
            translation_service = TranslationService()
            translation_available = True
        except ImportError:
            return jsonify({
                "success": False,
                "error": "Translation service not available"
            }), 500

        # Detect the language of the query
        detected_language = translation_service.detect_language(original_query)
        print(f"🔍 Detected language: {detected_language} for query: '{original_query[:50]}...'")
        
        # Language mapping for response
        language_names = {
            'te': 'Telugu',
            'kn': 'Kannada', 
            'ta': 'Tamil',
            'en': 'English'
        }
        
        detected_language_name = language_names.get(detected_language, 'English')
        
        # Check if query is in Telugu or Kannada
        if detected_language not in ['te', 'kn']:
            return jsonify({
                "success": False,
                "error": f"This endpoint is specifically for Telugu and Kannada queries. Detected language: {detected_language_name}",
                "detected_language": detected_language_name,
                "original_query": original_query
            }), 400

        # Step 1: Translate query to English
        english_query = original_query
        translation_successful = False
        
        if detected_language in ['te', 'kn']:
            print(f"🌐 Translating {detected_language_name} query to English...")
            translation_result = translation_service.translate_text(
                original_query, 
                target_lang='en', 
                source_lang=detected_language
            )
            
            if translation_result and translation_result.get('translated_text'):
                english_query = translation_result['translated_text']
                translation_successful = True
                print(f"✅ Query translated to English: '{english_query[:50]}...'")
            else:
                print("⚠️ Query translation failed, using original query")

        # Step 2: Prepare data for financial_query endpoint
        financial_query_data = {
            "query": english_query,
            "language": "English",  # We're sending English query
            "enable_translation": False,  # We'll handle translation ourselves
            **{k: v for k, v in data.items() if k not in ['query', 'language', 'enable_translation']}
        }

        # Step 3: Call financial_query endpoint internally
        print(f"📊 Sending English query to financial_query endpoint...")
        
        # Create a new request context for the internal call
        with app.test_request_context('/financial_query', 
                                    method='POST', 
                                    json=financial_query_data,
                                    headers={'Content-Type': 'application/json'}):
            try:
                # Call the financial_query function directly
                financial_response = handle_query()
                
                # Extract the response data
                if hasattr(financial_response, 'get_json'):
                    financial_data = financial_response.get_json()
                else:
                    financial_data = financial_response
                    
            except Exception as e:
                print(f"❌ Error calling financial_query: {str(e)}")
                return jsonify({
                    "success": False,
                    "error": f"Failed to process financial query: {str(e)}",
                    "original_query": original_query,
                    "english_query": english_query,
                    "detected_language": detected_language_name
                }), 500

        # Step 4: Translate the response back to original language
        if financial_data and detected_language in ['te', 'kn']:
            print(f"🔄 Translating response back to {detected_language_name}...")
            
            # Translate the main AI response
            if 'ai_response' in financial_data and financial_data['ai_response']:
                response_translation = translation_service.translate_text(
                    financial_data['ai_response'],
                    target_lang=detected_language,
                    source_lang='en'
                )
                
                if response_translation and response_translation.get('translated_text'):
                    financial_data['ai_response'] = response_translation['translated_text']
                    print(f"✅ AI response translated to {detected_language_name}")

            # Translate related questions if present
            if 'related_questions' in financial_data and financial_data['related_questions']:
                translated_questions = []
                for question in financial_data['related_questions']:
                    if question:
                        question_translation = translation_service.translate_text(
                            question,
                            target_lang=detected_language,
                            source_lang='en'
                        )
                        if question_translation and question_translation.get('translated_text'):
                            translated_questions.append(question_translation['translated_text'])
                        else:
                            translated_questions.append(question)
                    else:
                        translated_questions.append(question)
                
                financial_data['related_questions'] = translated_questions
                print(f"✅ Related questions translated to {detected_language_name}")

            # Translate any other text fields that might be present
            text_fields_to_translate = ['summary', 'context', 'explanation']
            for field in text_fields_to_translate:
                if field in financial_data and financial_data[field]:
                    field_translation = translation_service.translate_text(
                        financial_data[field],
                        target_lang=detected_language,
                        source_lang='en'
                    )
                    if field_translation and field_translation.get('translated_text'):
                        financial_data[field] = field_translation['translated_text']

        # Step 5: Add translation metadata to response
        financial_data.update({
            "translation_metadata": {
                "original_query": original_query,
                "english_query": english_query,
                "detected_language": detected_language_name,
                "query_translation_successful": translation_successful,
                "response_translated": detected_language in ['te', 'kn'],
                "translation_provider": translation_service.translator_available
            }
        })

        print(f"🎉 Multilingual financial query completed successfully for {detected_language_name}")
        return jsonify(financial_data)

    except Exception as e:
        print(f"❌ Error in multilingual_financial_query: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}",
            "original_query": original_query if 'original_query' in locals() else None
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5010)
