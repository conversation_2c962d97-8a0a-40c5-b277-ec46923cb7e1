{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/public/images/favicon.ico.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 58, height: 54, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAYAAAA1WQxeAAAA8klEQVR42gHnABj/AAEBAgEUJTAqN2uRkkWOxM8/hre8IUpiWwUKDQkAAAAAABIpMy04kL3HPqnj+j2l2+48qeD1Oqnd7yJfdm8DBgcEACqAnJg0uOf8MI2vqhQ0QTceVGteMLbh6i2v1NwPKzMpACyryMMqx+36IGBtWwABAQAKGx4TKLLPyyfK6vMVSVNJACGVpJke1/L9Ia6+tRdUWkklcoBzMLrh7iPF3+QONjovAA44OiwZws3HE+f2/BXm9fUc3fL6Kcjt/h/Q6e8RXGFRAAECAgEMNjcrE6CklhHQ1McSwcW2GZCbjxnE0ssPZmpRi6piXKCTQHwAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 7 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,wHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAA0a,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/chatComponents/UploadDropdown.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { PiFilePdf, PiYoutubeLogo, PiGlobe, PiMusicNote } from 'react-icons/pi';\r\nimport { UploadType } from './ChatInputUpload';\r\n\r\ninterface UploadDropdownProps {\r\n  onSelect: (type: UploadType) => void;\r\n  selectedLanguage: string;\r\n}\r\n\r\ninterface UploadOption {\r\n  type: UploadType;\r\n  icon: React.ReactNode;\r\n  label: {\r\n    English: string;\r\n    Tamil: string;\r\n    Telugu: string;\r\n    Kannada: string;\r\n  };\r\n  description: {\r\n    English: string;\r\n    Tamil: string;\r\n    Telugu: string;\r\n    Kannada: string;\r\n  };\r\n}\r\n\r\nconst uploadOptions: UploadOption[] = [\r\n  {\r\n    type: 'pdf',\r\n    icon: <PiFilePdf className=\"w-5 h-5\" />,\r\n    label: {\r\n      English: 'PDF/Document',\r\n      Tamil: 'PDF/ஆவணம்',\r\n      Telugu: 'PDF/పత్రం',\r\n      Kannada: 'PDF/ದಾಖಲೆ'\r\n    },\r\n    description: {\r\n      English: 'Upload PDF or document files',\r\n      Tamil: 'PDF அல்லது ஆவண கோப்புகளை பதிவேற்றவும்',\r\n      Telugu: 'PDF లేదా పత్రం ఫైల్‌లను అప్‌లోడ్ చేయండి',\r\n      Kannada: 'PDF ಅಥವಾ ದಾಖಲೆ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ'\r\n    }\r\n  },\r\n  {\r\n    type: 'youtube',\r\n    icon: <PiYoutubeLogo className=\"w-5 h-5\" />,\r\n    label: {\r\n      English: 'YouTube URL',\r\n      Tamil: 'YouTube URL',\r\n      Telugu: 'YouTube URL',\r\n      Kannada: 'YouTube URL'\r\n    },\r\n    description: {\r\n      English: 'Add YouTube video link',\r\n      Tamil: 'YouTube வீடியோ இணைப்பைச் சேர்க்கவும்',\r\n      Telugu: 'YouTube వీడియో లింక్ జోడించండి',\r\n      Kannada: 'YouTube ವೀಡಿಯೊ ಲಿಂಕ್ ಸೇರಿಸಿ'\r\n    }\r\n  },\r\n  {\r\n    type: 'article',\r\n    icon: <PiGlobe className=\"w-5 h-5\" />,\r\n    label: {\r\n      English: 'Article URL',\r\n      Tamil: 'கட்டுரை URL',\r\n      Telugu: 'వ్యాసం URL',\r\n      Kannada: 'ಲೇಖನ URL'\r\n    },\r\n    description: {\r\n      English: 'Add article or webpage link',\r\n      Tamil: 'கட்டுரை அல்லது வலைப்பக்க இணைப்பைச் சேர்க்கவும்',\r\n      Telugu: 'వ్యాసం లేదా వెబ్‌పేజీ లింక్ జోడించండి',\r\n      Kannada: 'ಲೇಖನ ಅಥವಾ ವೆಬ್‌ಪುಟದ ಲಿಂಕ್ ಸೇರಿಸಿ'\r\n    }\r\n  },\r\n  {\r\n    type: 'mp3',\r\n    icon: <PiMusicNote className=\"w-5 h-5\" />,\r\n    label: {\r\n      English: 'MP3 Audio',\r\n      Tamil: 'MP3 ஆடியோ',\r\n      Telugu: 'MP3 ఆడియో',\r\n      Kannada: 'MP3 ಆಡಿಯೊ'\r\n    },\r\n    description: {\r\n      English: 'Upload audio files',\r\n      Tamil: 'ஆடியோ கோப்புகளை பதிவேற்றவும்',\r\n      Telugu: 'ఆడియో ఫైల్‌లను అప్‌లోడ్ చేయండి',\r\n      Kannada: 'ಆಡಿಯೊ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ'\r\n    }\r\n  }\r\n];\r\n\r\nconst UploadDropdown: React.FC<UploadDropdownProps> = ({ onSelect, selectedLanguage }) => {\r\n  const getLanguageKey = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return 'Tamil';\r\n      case 'Telugu':\r\n        return 'Telugu';\r\n      case 'Kannada':\r\n        return 'Kannada';\r\n      default:\r\n        return 'English';\r\n    }\r\n  };\r\n\r\n  const getHoverColor = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return 'hover:bg-purple-50 hover:border-purple-200';\r\n      case 'Telugu':\r\n        return 'hover:bg-green-50 hover:border-green-200';\r\n      case 'Kannada':\r\n        return 'hover:bg-orange-50 hover:border-orange-200';\r\n      default:\r\n        return 'hover:bg-blue-50 hover:border-blue-200';\r\n    }\r\n  };\r\n\r\n  const getIconColor = (index: number) => {\r\n    const colors = {\r\n      Tamil: ['text-purple-600', 'text-purple-500', 'text-purple-700', 'text-purple-400'],\r\n      Telugu: ['text-green-600', 'text-green-500', 'text-green-700', 'text-green-400'],\r\n      Kannada: ['text-orange-600', 'text-orange-500', 'text-orange-700', 'text-orange-400'],\r\n      English: ['text-blue-600', 'text-blue-500', 'text-blue-700', 'text-blue-400']\r\n    };\r\n\r\n    const languageColors = colors[selectedLanguage as keyof typeof colors] || colors.English;\r\n    return languageColors[index % languageColors.length];\r\n  };\r\n\r\n  const languageKey = getLanguageKey();\r\n\r\n  return (\r\n    <div className=\"absolute bottom-full right-0 mb-2 w-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 shadow-lg z-50 overflow-hidden animate-fadeIn\">\r\n      <div className=\"p-2\">\r\n        {uploadOptions.map((option, index) => (\r\n          <button\r\n            key={option.type}\r\n            onClick={() => onSelect(option.type)}\r\n            className={`w-full flex items-start gap-3 p-3 rounded-lg border border-transparent transition-all ${getHoverColor()}`}\r\n          >\r\n            <div className={`flex-shrink-0 ${getIconColor(index)}`}>\r\n              {option.icon}\r\n            </div>\r\n            <div className=\"flex-grow text-left\">\r\n              <div className=\"font-medium text-sm text-gray-900 dark:text-gray-100\">\r\n                {option.label[languageKey as keyof typeof option.label]}\r\n              </div>\r\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-0.5\">\r\n                {option.description[languageKey as keyof typeof option.description]}\r\n              </div>\r\n            </div>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UploadDropdown;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAyBA,MAAM,gBAAgC;IACpC;QACE,MAAM;QACN,oBAAM,8OAAC,8IAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA,aAAa;YACX,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;IACF;IACA;QACE,MAAM;QACN,oBAAM,8OAAC,8IAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAC/B,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA,aAAa;YACX,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;IACF;IACA;QACE,MAAM;QACN,oBAAM,8OAAC,8IAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA,aAAa;YACX,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;IACF;IACA;QACE,MAAM;QACN,oBAAM,8OAAC,8IAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC7B,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA,aAAa;YACX,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;IACF;CACD;AAED,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE;IACnF,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,OAAO;gBAAC;gBAAmB;gBAAmB;gBAAmB;aAAkB;YACnF,QAAQ;gBAAC;gBAAkB;gBAAkB;gBAAkB;aAAiB;YAChF,SAAS;gBAAC;gBAAmB;gBAAmB;gBAAmB;aAAkB;YACrF,SAAS;gBAAC;gBAAiB;gBAAiB;gBAAiB;aAAgB;QAC/E;QAEA,MAAM,iBAAiB,MAAM,CAAC,iBAAwC,IAAI,OAAO,OAAO;QACxF,OAAO,cAAc,CAAC,QAAQ,eAAe,MAAM,CAAC;IACtD;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,8OAAC;oBAEC,SAAS,IAAM,SAAS,OAAO,IAAI;oBACnC,WAAW,CAAC,sFAAsF,EAAE,iBAAiB;;sCAErH,8OAAC;4BAAI,WAAW,CAAC,cAAc,EAAE,aAAa,QAAQ;sCACnD,OAAO,IAAI;;;;;;sCAEd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,OAAO,KAAK,CAAC,YAAyC;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;8CACZ,OAAO,WAAW,CAAC,YAA+C;;;;;;;;;;;;;mBAZlE,OAAO,IAAI;;;;;;;;;;;;;;;AAoB5B;uCAEe"}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/chatComponents/FileDropZone.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\r\nimport { PiCloudArrowUp, PiFile, PiX, PiCheck } from 'react-icons/pi';\r\n\r\ninterface FileDropZoneProps {\r\n  acceptedTypes: string;\r\n  onFilesSelected: (files: File[]) => void;\r\n  maxFiles?: number;\r\n  maxFileSize?: number; // in MB\r\n  selectedLanguage: string;\r\n}\r\n\r\ninterface FileWithStatus {\r\n  file: File;\r\n  status: 'selected' | 'uploading' | 'success' | 'error';\r\n  error?: string | null;\r\n}\r\n\r\nconst FileDropZone: React.FC<FileDropZoneProps> = ({\r\n  acceptedTypes,\r\n  onFilesSelected,\r\n  maxFiles = 1,\r\n  maxFileSize = 50,\r\n  selectedLanguage\r\n}) => {\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [files, setFiles] = useState<FileWithStatus[]>([]);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const getLanguageText = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return {\r\n          dragText: 'கோப்புகளை இங்கே இழுத்து விடவும் அல்லது உலாவ கிளிக் செய்யவும்',\r\n          browseText: 'உலாவு',\r\n          maxSizeText: `அதிகபட்சம் ${maxFileSize}MB`,\r\n          removeText: 'அகற்று',\r\n          selectedText: 'தேர்ந்தெடுக்கப்பட்டது'\r\n        };\r\n      case 'Telugu':\r\n        return {\r\n          dragText: 'ఫైల్‌లను ఇక్కడ లాగండి లేదా బ్రౌజ్ చేయడానికి క్లిక్ చేయండి',\r\n          browseText: 'బ్రౌజ్',\r\n          maxSizeText: `గరిష్టంగా ${maxFileSize}MB`,\r\n          removeText: 'తొలగించు',\r\n          selectedText: 'ఎంచుకోబడింది'\r\n        };\r\n      case 'Kannada':\r\n        return {\r\n          dragText: 'ಫೈಲ್‌ಗಳನ್ನು ಇಲ್ಲಿ ಎಳೆಯಿರಿ ಅಥವಾ ಬ್ರೌಸ್ ಮಾಡಲು ಕ್ಲಿಕ್ ಮಾಡಿ',\r\n          browseText: 'ಬ್ರೌಸ್',\r\n          maxSizeText: `ಗರಿಷ್ಠ ${maxFileSize}MB`,\r\n          removeText: 'ತೆಗೆದುಹಾಕಿ',\r\n          selectedText: 'ಆಯ್ಕೆಮಾಡಲಾಗಿದೆ'\r\n        };\r\n      default:\r\n        return {\r\n          dragText: 'Drag files here or click to browse',\r\n          browseText: 'Browse',\r\n          maxSizeText: `Max ${maxFileSize}MB`,\r\n          removeText: 'Remove',\r\n          selectedText: 'Selected'\r\n        };\r\n    }\r\n  };\r\n\r\n  const getBorderColor = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return isDragging ? 'border-purple-400 bg-purple-50' : 'border-purple-300';\r\n      case 'Telugu':\r\n        return isDragging ? 'border-green-400 bg-green-50' : 'border-green-300';\r\n      case 'Kannada':\r\n        return isDragging ? 'border-orange-400 bg-orange-50' : 'border-orange-300';\r\n      default:\r\n        return isDragging ? 'border-blue-400 bg-blue-50' : 'border-blue-300';\r\n    }\r\n  };\r\n\r\n  const getIconColor = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return 'text-purple-500';\r\n      case 'Telugu':\r\n        return 'text-green-500';\r\n      case 'Kannada':\r\n        return 'text-orange-500';\r\n      default:\r\n        return 'text-blue-500';\r\n    }\r\n  };\r\n\r\n  const validateFile = (file: File): string | null => {\r\n    // Check file size\r\n    if (file.size > maxFileSize * 1024 * 1024) {\r\n      return `File size exceeds ${maxFileSize}MB limit`;\r\n    }\r\n\r\n    // Check file type\r\n    const fileName = file.name.toLowerCase();\r\n    const acceptedExtensions = acceptedTypes.split(',').map(type => type.trim().toLowerCase());\r\n\r\n    // More comprehensive file type validation\r\n    const isValidType = acceptedExtensions.some(acceptedType => {\r\n      if (acceptedType.startsWith('.')) {\r\n        return fileName.endsWith(acceptedType);\r\n      }\r\n      return file.type === acceptedType;\r\n    });\r\n\r\n    if (!isValidType) {\r\n      // Provide more specific error messages based on accepted types\r\n      let typeDescription = '';\r\n      if (acceptedTypes.includes('.pdf')) {\r\n        typeDescription = 'PDF, DOC, DOCX, TXT, or RTF files';\r\n      } else if (acceptedTypes.includes('.mp3')) {\r\n        typeDescription = 'MP3, WAV, M4A, or FLAC audio files';\r\n      } else {\r\n        typeDescription = acceptedTypes;\r\n      }\r\n\r\n      return `File type not supported. Please upload ${typeDescription}`;\r\n    }\r\n\r\n    return null;\r\n  };\r\n\r\n  const handleFiles = useCallback((fileList: FileList) => {\r\n    const newFiles: FileWithStatus[] = [];\r\n    const validFiles: File[] = [];\r\n\r\n    Array.from(fileList).slice(0, maxFiles).forEach(file => {\r\n      const error = validateFile(file);\r\n      const fileWithStatus: FileWithStatus = {\r\n        file,\r\n        status: error ? 'error' : 'selected',\r\n        error\r\n      };\r\n      \r\n      newFiles.push(fileWithStatus);\r\n      \r\n      if (!error) {\r\n        validFiles.push(file);\r\n      }\r\n    });\r\n\r\n    setFiles(newFiles);\r\n    \r\n    if (validFiles.length > 0) {\r\n      onFilesSelected(validFiles);\r\n    }\r\n  }, [maxFiles, onFilesSelected]);\r\n\r\n  const handleDragOver = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragging(true);\r\n  }, []);\r\n\r\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n  }, []);\r\n\r\n  const handleDrop = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n    \r\n    if (e.dataTransfer.files) {\r\n      handleFiles(e.dataTransfer.files);\r\n    }\r\n  }, [handleFiles]);\r\n\r\n  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files) {\r\n      handleFiles(e.target.files);\r\n    }\r\n  }, [handleFiles]);\r\n\r\n  const handleRemoveFile = (index: number) => {\r\n    const newFiles = files.filter((_, i) => i !== index);\r\n    setFiles(newFiles);\r\n    \r\n    const validFiles = newFiles\r\n      .filter(f => f.status !== 'error')\r\n      .map(f => f.file);\r\n    \r\n    onFilesSelected(validFiles);\r\n  };\r\n\r\n  const handleClick = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const text = getLanguageText();\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Drop zone */}\r\n      <div\r\n        onClick={handleClick}\r\n        onDragOver={handleDragOver}\r\n        onDragLeave={handleDragLeave}\r\n        onDrop={handleDrop}\r\n        className={`\r\n          border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200\r\n          ${getBorderColor()}\r\n          hover:bg-gray-50 dark:hover:bg-gray-700/50\r\n          min-h-[160px] flex items-center justify-center\r\n        `}\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept={acceptedTypes}\r\n          multiple={maxFiles > 1}\r\n          onChange={handleFileInputChange}\r\n          className=\"hidden\"\r\n        />\r\n\r\n        <div className=\"flex flex-col items-center justify-center gap-3\">\r\n          <div className=\"p-3 rounded-full bg-gray-100 dark:bg-gray-800\">\r\n            <PiCloudArrowUp className={`w-10 h-10 ${getIconColor()}`} />\r\n          </div>\r\n          <div className=\"space-y-1\">\r\n            <p className=\"text-sm font-medium text-gray-700 dark:text-gray-300 leading-relaxed\">\r\n              {text.dragText}\r\n            </p>\r\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n              {text.maxSizeText}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Selected files */}\r\n      {files.length > 0 && (\r\n        <div className=\"space-y-3\">\r\n          <div className=\"border-t border-gray-200 dark:border-gray-700 pt-4\">\r\n            <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\r\n              {text.selectedText} ({files.length})\r\n            </h4>\r\n            <div className=\"space-y-2\">\r\n              {files.map((fileWithStatus, index) => (\r\n                <div\r\n                  key={index}\r\n                  className={`\r\n                    flex items-center gap-4 p-4 rounded-lg border transition-all duration-200\r\n                    ${fileWithStatus.status === 'error'\r\n                      ? 'border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800'\r\n                      : 'border-gray-200 bg-gray-50 dark:bg-gray-700/50 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className=\"flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600\">\r\n                    {fileWithStatus.status === 'error' ? (\r\n                      <PiX className=\"w-5 h-5 text-red-500\" />\r\n                    ) : fileWithStatus.status === 'success' ? (\r\n                      <PiCheck className=\"w-5 h-5 text-green-500\" />\r\n                    ) : (\r\n                      <PiFile className={`w-5 h-5 ${getIconColor()}`} />\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"flex-grow min-w-0 space-y-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                      {fileWithStatus.file.name}\r\n                    </p>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                        {(fileWithStatus.file.size / 1024 / 1024).toFixed(2)} MB\r\n                      </p>\r\n                      {fileWithStatus.status === 'selected' && (\r\n                        <span className=\"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\">\r\n                          Ready\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                    {fileWithStatus.error && (\r\n                      <p className=\"text-xs text-red-600 dark:text-red-400 mt-1 leading-relaxed\">\r\n                        {fileWithStatus.error}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n\r\n                  <button\r\n                    onClick={() => handleRemoveFile(index)}\r\n                    className=\"flex-shrink-0 p-2 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200\"\r\n                    title={text.removeText}\r\n                  >\r\n                    <PiX className=\"w-4 h-4\" />\r\n                  </button>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileDropZone;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAgBA,MAAM,eAA4C,CAAC,EACjD,aAAa,EACb,eAAe,EACf,WAAW,CAAC,EACZ,cAAc,EAAE,EAChB,gBAAgB,EACjB;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACvD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,aAAa,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;oBAC1C,YAAY;oBACZ,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,aAAa,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC;oBACzC,YAAY;oBACZ,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,aAAa,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC;oBACtC,YAAY;oBACZ,cAAc;gBAChB;YACF;gBACE,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC;oBACnC,YAAY;oBACZ,cAAc;gBAChB;QACJ;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO,aAAa,mCAAmC;YACzD,KAAK;gBACH,OAAO,aAAa,iCAAiC;YACvD,KAAK;gBACH,OAAO,aAAa,mCAAmC;YACzD;gBACE,OAAO,aAAa,+BAA+B;QACvD;IACF;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,kBAAkB;QAClB,IAAI,KAAK,IAAI,GAAG,cAAc,OAAO,MAAM;YACzC,OAAO,CAAC,kBAAkB,EAAE,YAAY,QAAQ,CAAC;QACnD;QAEA,kBAAkB;QAClB,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;QACtC,MAAM,qBAAqB,cAAc,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,WAAW;QAEvF,0CAA0C;QAC1C,MAAM,cAAc,mBAAmB,IAAI,CAAC,CAAA;YAC1C,IAAI,aAAa,UAAU,CAAC,MAAM;gBAChC,OAAO,SAAS,QAAQ,CAAC;YAC3B;YACA,OAAO,KAAK,IAAI,KAAK;QACvB;QAEA,IAAI,CAAC,aAAa;YAChB,+DAA+D;YAC/D,IAAI,kBAAkB;YACtB,IAAI,cAAc,QAAQ,CAAC,SAAS;gBAClC,kBAAkB;YACpB,OAAO,IAAI,cAAc,QAAQ,CAAC,SAAS;gBACzC,kBAAkB;YACpB,OAAO;gBACL,kBAAkB;YACpB;YAEA,OAAO,CAAC,uCAAuC,EAAE,iBAAiB;QACpE;QAEA,OAAO;IACT;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,WAA6B,EAAE;QACrC,MAAM,aAAqB,EAAE;QAE7B,MAAM,IAAI,CAAC,UAAU,KAAK,CAAC,GAAG,UAAU,OAAO,CAAC,CAAA;YAC9C,MAAM,QAAQ,aAAa;YAC3B,MAAM,iBAAiC;gBACrC;gBACA,QAAQ,QAAQ,UAAU;gBAC1B;YACF;YAEA,SAAS,IAAI,CAAC;YAEd,IAAI,CAAC,OAAO;gBACV,WAAW,IAAI,CAAC;YAClB;QACF;QAEA,SAAS;QAET,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAU;KAAgB;IAE9B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE;YACxB,YAAY,EAAE,YAAY,CAAC,KAAK;QAClC;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,YAAY,EAAE,MAAM,CAAC,KAAK;QAC5B;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC9C,SAAS;QAET,MAAM,aAAa,SAChB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SACzB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAElB,gBAAgB;IAClB;IAEA,MAAM,cAAc;QAClB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,OAAO;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,SAAS;gBACT,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,WAAW,CAAC;;UAEV,EAAE,iBAAiB;;;QAGrB,CAAC;;kCAED,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU,WAAW;wBACrB,UAAU;wBACV,WAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;oCAAC,WAAW,CAAC,UAAU,EAAE,gBAAgB;;;;;;;;;;;0CAE1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,KAAK,QAAQ;;;;;;kDAEhB,8OAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;YAOxB,MAAM,MAAM,GAAG,mBACd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCACX,KAAK,YAAY;gCAAC;gCAAG,MAAM,MAAM;gCAAC;;;;;;;sCAErC,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,gBAAgB,sBAC1B,8OAAC;oCAEC,WAAW,CAAC;;oBAEV,EAAE,eAAe,MAAM,KAAK,UACxB,oEACA,+GACH;kBACH,CAAC;;sDAED,8OAAC;4CAAI,WAAU;sDACZ,eAAe,MAAM,KAAK,wBACzB,8OAAC,8IAAA,CAAA,MAAG;gDAAC,WAAU;;;;;uDACb,eAAe,MAAM,KAAK,0BAC5B,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,8OAAC,8IAAA,CAAA,SAAM;gDAAC,WAAW,CAAC,QAAQ,EAAE,gBAAgB;;;;;;;;;;;sDAIlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,eAAe,IAAI,CAAC,IAAI;;;;;;8DAE3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;gEACV,CAAC,eAAe,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;wDAEtD,eAAe,MAAM,KAAK,4BACzB,8OAAC;4DAAK,WAAU;sEAA6I;;;;;;;;;;;;gDAKhK,eAAe,KAAK,kBACnB,8OAAC;oDAAE,WAAU;8DACV,eAAe,KAAK;;;;;;;;;;;;sDAK3B,8OAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;4CACV,OAAO,KAAK,UAAU;sDAEtB,cAAA,8OAAC,8IAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;mCA7CZ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDvB;uCAEe"}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/chatComponents/URLInput.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>rrowRight } from 'react-icons/pi';\r\n\r\ninterface URLInputProps {\r\n  type: 'youtube' | 'article';\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n  onSubmit: (url: string) => void;\r\n  selectedLanguage: string;\r\n}\r\n\r\nconst URLInput: React.FC<URLInputProps> = ({\r\n  type,\r\n  value,\r\n  onChange,\r\n  onSubmit,\r\n  selectedLanguage\r\n}) => {\r\n  const [isValid, setIsValid] = useState<boolean | null>(null);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  const getLanguageText = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return {\r\n          youtubePlaceholder: 'YouTube வீடியோ URL ஐ உள்ளிடவும்...',\r\n          articlePlaceholder: 'கட்டுரை அல்லது வலைப்பக்க URL ஐ உள்ளிடவும்...',\r\n          addButton: 'சேர்',\r\n          invalidUrl: 'தவறான URL வடிவம்',\r\n          invalidYoutube: 'தவறான YouTube URL',\r\n          validUrl: 'சரியான URL'\r\n        };\r\n      case 'Telugu':\r\n        return {\r\n          youtubePlaceholder: 'YouTube వీడియో URL ని ఎంటర్ చేయండి...',\r\n          articlePlaceholder: 'వ్యాసం లేదా వెబ్‌పేజీ URL ని ఎంటర్ చేయండి...',\r\n          addButton: 'జోడించు',\r\n          invalidUrl: 'చెల్లని URL ఫార్మాట్',\r\n          invalidYoutube: 'చెల్లని YouTube URL',\r\n          validUrl: 'చెల్లుబాటు అయ్యే URL'\r\n        };\r\n      case 'Kannada':\r\n        return {\r\n          youtubePlaceholder: 'YouTube ವೀಡಿಯೊ URL ಅನ್ನು ನಮೂದಿಸಿ...',\r\n          articlePlaceholder: 'ಲೇಖನ ಅಥವಾ ವೆಬ್‌ಪುಟದ URL ಅನ್ನು ನಮೂದಿಸಿ...',\r\n          addButton: 'ಸೇರಿಸಿ',\r\n          invalidUrl: 'ಅಮಾನ್ಯ URL ಸ್ವರೂಪ',\r\n          invalidYoutube: 'ಅಮಾನ್ಯ YouTube URL',\r\n          validUrl: 'ಮಾನ್ಯ URL'\r\n        };\r\n      default:\r\n        return {\r\n          youtubePlaceholder: 'Enter YouTube video URL...',\r\n          articlePlaceholder: 'Enter article or webpage URL...',\r\n          addButton: 'Add',\r\n          invalidUrl: 'Invalid URL format',\r\n          invalidYoutube: 'Invalid YouTube URL',\r\n          validUrl: 'Valid URL'\r\n        };\r\n    }\r\n  };\r\n\r\n  const validateURL = (url: string): { isValid: boolean; error: string } => {\r\n    if (!url.trim()) {\r\n      return { isValid: false, error: '' };\r\n    }\r\n\r\n    try {\r\n      const urlObj = new URL(url);\r\n      \r\n      if (type === 'youtube') {\r\n        const isYouTube = \r\n          urlObj.hostname === 'www.youtube.com' ||\r\n          urlObj.hostname === 'youtube.com' ||\r\n          urlObj.hostname === 'youtu.be' ||\r\n          urlObj.hostname === 'm.youtube.com';\r\n        \r\n        if (!isYouTube) {\r\n          return { isValid: false, error: getLanguageText().invalidYoutube };\r\n        }\r\n      }\r\n      \r\n      return { isValid: true, error: '' };\r\n    } catch {\r\n      return { isValid: false, error: getLanguageText().invalidUrl };\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newValue = e.target.value;\r\n    onChange(newValue);\r\n    \r\n    const validation = validateURL(newValue);\r\n    setIsValid(validation.isValid);\r\n    setError(validation.error);\r\n  };\r\n\r\n  const handleSubmit = () => {\r\n    if (isValid && value.trim()) {\r\n      onSubmit(value.trim());\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && isValid && value.trim()) {\r\n      e.preventDefault();\r\n      handleSubmit();\r\n    }\r\n  };\r\n\r\n  const getBorderColor = () => {\r\n    if (isValid === null) {\r\n      switch (selectedLanguage) {\r\n        case 'Tamil':\r\n          return 'border-purple-300 focus:border-purple-500';\r\n        case 'Telugu':\r\n          return 'border-green-300 focus:border-green-500';\r\n        case 'Kannada':\r\n          return 'border-orange-300 focus:border-orange-500';\r\n        default:\r\n          return 'border-blue-300 focus:border-blue-500';\r\n      }\r\n    }\r\n    \r\n    return isValid \r\n      ? 'border-green-300 focus:border-green-500' \r\n      : 'border-red-300 focus:border-red-500';\r\n  };\r\n\r\n  const getButtonColor = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return 'bg-purple-500 hover:bg-purple-600 focus:ring-purple-500';\r\n      case 'Telugu':\r\n        return 'bg-green-500 hover:bg-green-600 focus:ring-green-500';\r\n      case 'Kannada':\r\n        return 'bg-orange-500 hover:bg-orange-600 focus:ring-orange-500';\r\n      default:\r\n        return 'bg-blue-500 hover:bg-blue-600 focus:ring-blue-500';\r\n    }\r\n  };\r\n\r\n  const text = getLanguageText();\r\n\r\n  return (\r\n    <div className=\"space-y-2\">\r\n      <div className=\"relative\">\r\n        <input\r\n          type=\"url\"\r\n          value={value}\r\n          onChange={handleInputChange}\r\n          onKeyPress={handleKeyPress}\r\n          placeholder={type === 'youtube' ? text.youtubePlaceholder : text.articlePlaceholder}\r\n          className={`\r\n            w-full px-3 py-2 pr-10 border rounded-lg\r\n            focus:outline-none focus:ring-2 focus:ring-opacity-50\r\n            ${getBorderColor()}\r\n            dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\r\n          `}\r\n        />\r\n\r\n        {/* Validation icon */}\r\n        <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\r\n          {isValid === true && (\r\n            <PiCheck className=\"w-5 h-5 text-green-500\" />\r\n          )}\r\n          {isValid === false && value.trim() && (\r\n            <PiX className=\"w-5 h-5 text-red-500\" />\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <p className=\"text-xs text-red-500 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n\r\n      {/* Success message */}\r\n      {isValid && value.trim() && (\r\n        <p className=\"text-xs text-green-500 dark:text-green-400\">\r\n          {text.validUrl}\r\n        </p>\r\n      )}\r\n\r\n      {/* Add button */}\r\n      <button\r\n        type=\"button\"\r\n        onClick={handleSubmit}\r\n        disabled={!isValid || !value.trim()}\r\n        className={`\r\n          w-full flex items-center justify-center gap-2 px-4 py-2 rounded-lg\r\n          text-white font-medium text-sm transition-all\r\n          ${isValid && value.trim()\r\n            ? getButtonColor()\r\n            : 'bg-gray-400 cursor-not-allowed'\r\n          }\r\n          disabled:opacity-50\r\n        `}\r\n      >\r\n        {text.addButton}\r\n        <PiArrowRight className=\"w-4 h-4\" />\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default URLInput;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,WAAoC,CAAC,EACzC,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,gBAAgB,EACjB;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,oBAAoB;oBACpB,oBAAoB;oBACpB,WAAW;oBACX,YAAY;oBACZ,gBAAgB;oBAChB,UAAU;gBACZ;YACF,KAAK;gBACH,OAAO;oBACL,oBAAoB;oBACpB,oBAAoB;oBACpB,WAAW;oBACX,YAAY;oBACZ,gBAAgB;oBAChB,UAAU;gBACZ;YACF,KAAK;gBACH,OAAO;oBACL,oBAAoB;oBACpB,oBAAoB;oBACpB,WAAW;oBACX,YAAY;oBACZ,gBAAgB;oBAChB,UAAU;gBACZ;YACF;gBACE,OAAO;oBACL,oBAAoB;oBACpB,oBAAoB;oBACpB,WAAW;oBACX,YAAY;oBACZ,gBAAgB;oBAChB,UAAU;gBACZ;QACJ;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,IAAI,IAAI,IAAI;YACf,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAG;QACrC;QAEA,IAAI;YACF,MAAM,SAAS,IAAI,IAAI;YAEvB,IAAI,SAAS,WAAW;gBACtB,MAAM,YACJ,OAAO,QAAQ,KAAK,qBACpB,OAAO,QAAQ,KAAK,iBACpB,OAAO,QAAQ,KAAK,cACpB,OAAO,QAAQ,KAAK;gBAEtB,IAAI,CAAC,WAAW;oBACd,OAAO;wBAAE,SAAS;wBAAO,OAAO,kBAAkB,cAAc;oBAAC;gBACnE;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAM,OAAO;YAAG;QACpC,EAAE,OAAM;YACN,OAAO;gBAAE,SAAS;gBAAO,OAAO,kBAAkB,UAAU;YAAC;QAC/D;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,SAAS;QAET,MAAM,aAAa,YAAY;QAC/B,WAAW,WAAW,OAAO;QAC7B,SAAS,WAAW,KAAK;IAC3B;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW,MAAM,IAAI,IAAI;YAC3B,SAAS,MAAM,IAAI;QACrB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,WAAW,MAAM,IAAI,IAAI;YAChD,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,YAAY,MAAM;YACpB,OAAQ;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,OAAO,UACH,4CACA;IACN;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,OAAO;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,YAAY;wBACZ,aAAa,SAAS,YAAY,KAAK,kBAAkB,GAAG,KAAK,kBAAkB;wBACnF,WAAW,CAAC;;;YAGV,EAAE,iBAAiB;;UAErB,CAAC;;;;;;kCAIH,8OAAC;wBAAI,WAAU;;4BACZ,YAAY,sBACX,8OAAC,8IAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAEpB,YAAY,SAAS,MAAM,IAAI,oBAC9B,8OAAC,8IAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;;;;;;;;YAMpB,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAKJ,WAAW,MAAM,IAAI,oBACpB,8OAAC;gBAAE,WAAU;0BACV,KAAK,QAAQ;;;;;;0BAKlB,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,UAAU,CAAC,WAAW,CAAC,MAAM,IAAI;gBACjC,WAAW,CAAC;;;UAGV,EAAE,WAAW,MAAM,IAAI,KACnB,mBACA,iCACH;;QAEH,CAAC;;oBAEA,KAAK,SAAS;kCACf,8OAAC,8IAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIhC;uCAEe"}}, {"offset": {"line": 874, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/services/faissService.ts"], "sourcesContent": ["/**\r\n * FAISS Service - Handles all FAISS-related API calls\r\n * Replaces Pinecone-specific functionality with FAISS backend integration\r\n */\r\n\r\nexport interface EmbeddingModel {\r\n  name: string;\r\n  dimension: number;\r\n  description: string;\r\n}\r\n\r\nexport interface FaissCategory {\r\n  index_name: string;\r\n  email?: string;\r\n  embedding_model?: string;\r\n  embedding_dimension?: number;\r\n  created_at?: string;\r\n}\r\n\r\nexport interface FaissQueryResult {\r\n  rank: number;\r\n  score: string;\r\n  metadata: any;\r\n  text: string;\r\n}\r\n\r\nexport interface UploadProgress {\r\n  upload_id: string;\r\n  status: 'processing' | 'complete' | 'cancelled' | 'error';\r\n  total_rows: number;\r\n  processed_rows: number;\r\n  total_vectors: number;\r\n  index_name: string;\r\n  cancelled: boolean;\r\n  processing_time?: number;\r\n}\r\n\r\n// Base URL for FAISS backend\r\nconst FAISS_BASE_URL = process.env.NODE_ENV === 'development' \r\n  ? 'http://localhost:5010' \r\n  : 'http://localhost:5010';\r\n\r\n/**\r\n * Upload CSV file to FAISS backend\r\n */\r\nexport const uploadCSVToFaiss = async (\r\n  file: File,\r\n  indexName: string,\r\n  clientEmail?: string,\r\n  updateMode: 'update' | 'new' = 'update',\r\n  embedModel: string = 'all-MiniLM-L6-v2',\r\n  signal?: AbortSignal,\r\n  onProgress?: (progress: number) => void\r\n): Promise<any> => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // Validate file type\r\n      if (file.type !== 'text/csv' && !file.name.toLowerCase().endsWith('.csv')) {\r\n        reject(new Error('Only CSV files are supported for CSV upload'));\r\n        return;\r\n      }\r\n\r\n      // Create FormData\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('index_name', indexName);\r\n      formData.append('update_mode', updateMode);\r\n      formData.append('embed_model', embedModel);\r\n      \r\n      if (clientEmail) {\r\n        formData.append('client', clientEmail);\r\n      }\r\n\r\n      // Create XMLHttpRequest for progress tracking\r\n      const xhr = new XMLHttpRequest();\r\n\r\n      // Handle response\r\n      xhr.onload = () => {\r\n        if (xhr.status >= 200 && xhr.status < 300) {\r\n          try {\r\n            const response = JSON.parse(xhr.responseText);\r\n            resolve(response);\r\n          } catch (e) {\r\n            reject(new Error('Invalid JSON response from server'));\r\n          }\r\n        } else {\r\n          try {\r\n            const errorResponse = JSON.parse(xhr.responseText);\r\n            reject(new Error(errorResponse.error || `HTTP ${xhr.status}: ${xhr.statusText}`));\r\n          } catch (e) {\r\n            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));\r\n          }\r\n        }\r\n      };\r\n\r\n      xhr.onerror = () => {\r\n        reject(new Error('Network error occurred during upload'));\r\n      };\r\n\r\n      xhr.onabort = () => {\r\n        reject(new Error('Upload was cancelled'));\r\n      };\r\n\r\n      // Track upload progress\r\n      if (onProgress) {\r\n        xhr.upload.onprogress = (event) => {\r\n          if (event.lengthComputable) {\r\n            const progress = Math.round((event.loaded / event.total) * 100);\r\n            onProgress(progress);\r\n          }\r\n        };\r\n      }\r\n\r\n      // Handle cancellation\r\n      if (signal) {\r\n        signal.addEventListener('abort', () => {\r\n          xhr.abort();\r\n        });\r\n      }\r\n\r\n      // Send request\r\n      xhr.open('POST', `${FAISS_BASE_URL}/api/upload-csv`, true);\r\n      xhr.send(formData);\r\n\r\n    } catch (error) {\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Upload Excel file to FAISS backend\r\n */\r\nexport const uploadExcelToFaiss = async (\r\n  file: File,\r\n  indexName: string,\r\n  clientId: string,\r\n  updateMode: 'update' | 'new' = 'update',\r\n  embedModel: string = 'all-MiniLM-L6-v2',\r\n  signal?: AbortSignal,\r\n  onProgress?: (progress: number) => void\r\n): Promise<any> => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // Validate file type\r\n      const fileName = file.name.toLowerCase();\r\n      if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {\r\n        reject(new Error('Only Excel files (.xlsx, .xls) are supported for Excel upload'));\r\n        return;\r\n      }\r\n\r\n      // Create FormData\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('index_name', indexName);\r\n      formData.append('client_id', clientId);\r\n      formData.append('update_mode', updateMode);\r\n      formData.append('embed_model', embedModel);\r\n\r\n      // Create XMLHttpRequest for progress tracking\r\n      const xhr = new XMLHttpRequest();\r\n\r\n      // Handle response\r\n      xhr.onload = () => {\r\n        if (xhr.status >= 200 && xhr.status < 300) {\r\n          try {\r\n            const response = JSON.parse(xhr.responseText);\r\n            resolve(response);\r\n          } catch (e) {\r\n            reject(new Error('Invalid JSON response from server'));\r\n          }\r\n        } else {\r\n          try {\r\n            const errorResponse = JSON.parse(xhr.responseText);\r\n            reject(new Error(errorResponse.error?.message || errorResponse.message || `HTTP ${xhr.status}: ${xhr.statusText}`));\r\n          } catch (e) {\r\n            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));\r\n          }\r\n        }\r\n      };\r\n\r\n      xhr.onerror = () => {\r\n        reject(new Error('Network error occurred during upload'));\r\n      };\r\n\r\n      xhr.onabort = () => {\r\n        reject(new Error('Upload was cancelled'));\r\n      };\r\n\r\n      // Track upload progress\r\n      if (onProgress) {\r\n        xhr.upload.onprogress = (event) => {\r\n          if (event.lengthComputable) {\r\n            const progress = Math.round((event.loaded / event.total) * 100);\r\n            onProgress(progress);\r\n          }\r\n        };\r\n      }\r\n\r\n      // Handle cancellation\r\n      if (signal) {\r\n        signal.addEventListener('abort', () => {\r\n          xhr.abort();\r\n        });\r\n      }\r\n\r\n      // Send request\r\n      xhr.open('POST', `${FAISS_BASE_URL}/api/upload-excel`, true);\r\n      xhr.send(formData);\r\n\r\n    } catch (error) {\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Upload file to FAISS backend (supports both CSV and Excel)\r\n */\r\nexport const uploadFileToFaiss = async (\r\n  file: File,\r\n  indexName: string,\r\n  clientEmail?: string,\r\n  updateMode: 'update' | 'new' = 'update',\r\n  embedModel: string = 'all-MiniLM-L6-v2',\r\n  signal?: AbortSignal,\r\n  onProgress?: (progress: number) => void\r\n): Promise<any> => {\r\n  const fileName = file.name.toLowerCase();\r\n  const isExcel = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');\r\n\r\n  if (isExcel) {\r\n    // For Excel files, client_id is required\r\n    if (!clientEmail) {\r\n      throw new Error('Client email is required for Excel file uploads');\r\n    }\r\n    return uploadExcelToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);\r\n  } else {\r\n    // For CSV files, use the existing CSV upload function\r\n    return uploadCSVToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);\r\n  }\r\n};\r\n\r\n/**\r\n * Get list of available embedding models\r\n */\r\nexport const getEmbeddingModels = async (): Promise<{ models: Record<string, EmbeddingModel>, default_model: string }> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-embedding-models`);\r\n  if (!response.ok) {\r\n    throw new Error(`Failed to fetch embedding models: ${response.statusText}`);\r\n  }\r\n  return response.json();\r\n};\r\n\r\n/**\r\n * Get list of FAISS categories/indexes\r\n */\r\nexport const getFaissCategories = async (clientEmail?: string): Promise<FaissCategory[]> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-categories`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify(clientEmail ? { client_email: clientEmail } : {})\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to fetch FAISS categories: ${response.statusText}`);\r\n  }\r\n  \r\n  const data = await response.json();\r\n  return data.categories || [];\r\n};\r\n\r\n/**\r\n * Check if FAISS index exists\r\n */\r\nexport const checkFaissIndexExists = async (indexName: string, embedModel?: string): Promise<boolean> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/check-index`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({\r\n      index_name: indexName,\r\n      embed_model: embedModel\r\n    })\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to check FAISS index: ${response.statusText}`);\r\n  }\r\n  \r\n  const data = await response.json();\r\n  return data.exists || false;\r\n};\r\n\r\n/**\r\n * Get current user's email from session storage\r\n */\r\nconst getCurrentUserEmail = (): string | null => {\r\n  try {\r\n    if (typeof window === 'undefined') return null;\r\n\r\n    // Try multiple sources for user email\r\n    const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');\r\n    if (directEmail) return directEmail;\r\n\r\n    // Try from user session data\r\n    const userSession = sessionStorage.getItem('resultUser');\r\n    if (userSession) {\r\n      const userData = JSON.parse(userSession);\r\n      return userData.email || userData.username || null;\r\n    }\r\n\r\n    return null;\r\n  } catch (error) {\r\n    console.error('Error getting current user email:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Query FAISS index with user access validation\r\n */\r\nexport const queryFaiss = async (\r\n  query: string,\r\n  indexName: string,\r\n  k: number = 5,\r\n  userEmail?: string\r\n): Promise<FaissQueryResult[]> => {\r\n  // Get user email if not provided\r\n  const emailToUse = userEmail || getCurrentUserEmail();\r\n\r\n  const requestBody: any = {\r\n    query,\r\n    index_name: indexName,\r\n    k\r\n  };\r\n\r\n  // Add user email for access validation if available\r\n  if (emailToUse) {\r\n    requestBody.user_email = emailToUse;\r\n  }\r\n\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/query-faiss`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify(requestBody)\r\n  });\r\n\r\n  if (!response.ok) {\r\n    throw new Error(`Failed to query FAISS index: ${response.statusText}`);\r\n  }\r\n\r\n  const data = await response.json();\r\n  return data.results || [];\r\n};\r\n\r\n/**\r\n * Get upload status\r\n */\r\nexport const getUploadStatus = async (uploadId: string): Promise<UploadProgress> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/upload-status`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({ upload_id: uploadId })\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to get upload status: ${response.statusText}`);\r\n  }\r\n  \r\n  return response.json();\r\n};\r\n\r\n/**\r\n * Cancel upload\r\n */\r\nexport const cancelUpload = async (uploadId: string): Promise<void> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/cancel-upload`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({ upload_id: uploadId })\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to cancel upload: ${response.statusText}`);\r\n  }\r\n};\r\n\r\n/**\r\n * Get CSV data from database\r\n */\r\nexport const getCSVData = async (\r\n  indexName: string, \r\n  limit: number = 100, \r\n  offset: number = 0\r\n): Promise<any> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/get-csv-data`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({\r\n      index_name: indexName,\r\n      limit,\r\n      offset\r\n    })\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to get CSV data: ${response.statusText}`);\r\n  }\r\n  \r\n  return response.json();\r\n};\r\n\r\n/**\r\n * List CSV files in database\r\n */\r\nexport const listCSVFiles = async (clientEmail?: string): Promise<any[]> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-csv-files`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify(clientEmail ? { client_email: clientEmail } : {})\r\n  });\r\n\r\n  if (!response.ok) {\r\n    throw new Error(`Failed to list CSV files: ${response.statusText}`);\r\n  }\r\n\r\n  const data = await response.json();\r\n  return data.files || [];\r\n};\r\n\r\n/**\r\n * List Excel files in database\r\n */\r\nexport const listExcelFiles = async (clientId?: string): Promise<any[]> => {\r\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-excel-files`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify(clientId ? { client_id: clientId } : {})\r\n  });\r\n\r\n  if (!response.ok) {\r\n    throw new Error(`Failed to list Excel files: ${response.statusText}`);\r\n  }\r\n\r\n  const data = await response.json();\r\n  return data.excel_files || [];\r\n};\r\n\r\n// Utility functions for localStorage management\r\nexport const getFaissConfig = () => {\r\n  if (typeof window === 'undefined') return null;\r\n  \r\n  return {\r\n    indexName: localStorage.getItem('faiss_index_name'),\r\n    embedModel: localStorage.getItem('faiss_embed_model') || 'all-MiniLM-L6-v2',\r\n    clientEmail: localStorage.getItem('faiss_client_email')\r\n  };\r\n};\r\n\r\nexport const setFaissConfig = (config: {\r\n  indexName?: string;\r\n  embedModel?: string;\r\n  clientEmail?: string;\r\n}) => {\r\n  if (typeof window === 'undefined') return;\r\n  \r\n  if (config.indexName) {\r\n    localStorage.setItem('faiss_index_name', config.indexName);\r\n  }\r\n  if (config.embedModel) {\r\n    localStorage.setItem('faiss_embed_model', config.embedModel);\r\n  }\r\n  if (config.clientEmail) {\r\n    localStorage.setItem('faiss_client_email', config.clientEmail);\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;AAkCD,6BAA6B;AAC7B,MAAM,iBAAiB,uCACnB;AAMG,MAAM,mBAAmB,OAC9B,MACA,WACA,aACA,aAA+B,QAAQ,EACvC,aAAqB,kBAAkB,EACvC,QACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,qBAAqB;YACrB,IAAI,KAAK,IAAI,KAAK,cAAc,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;gBACzE,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,kBAAkB;YAClB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,cAAc;YAC9B,SAAS,MAAM,CAAC,eAAe;YAC/B,SAAS,MAAM,CAAC,eAAe;YAE/B,IAAI,aAAa;gBACf,SAAS,MAAM,CAAC,UAAU;YAC5B;YAEA,8CAA8C;YAC9C,MAAM,MAAM,IAAI;YAEhB,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM;oBACnB;gBACF,OAAO;oBACL,IAAI;wBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,IAAI,YAAY;wBACjD,OAAO,IAAI,MAAM,cAAc,KAAK,IAAI,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBACjF,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBAC1D;gBACF;YACF;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,wBAAwB;YACxB,IAAI,YAAY;gBACd,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;oBACvB,IAAI,MAAM,gBAAgB,EAAE;wBAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;wBAC3D,WAAW;oBACb;gBACF;YACF;YAEA,sBAAsB;YACtB,IAAI,QAAQ;gBACV,OAAO,gBAAgB,CAAC,SAAS;oBAC/B,IAAI,KAAK;gBACX;YACF;YAEA,eAAe;YACf,IAAI,IAAI,CAAC,QAAQ,GAAG,eAAe,eAAe,CAAC,EAAE;YACrD,IAAI,IAAI,CAAC;QAEX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAKO,MAAM,qBAAqB,OAChC,MACA,WACA,UACA,aAA+B,QAAQ,EACvC,aAAqB,kBAAkB,EACvC,QACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,qBAAqB;YACrB,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;YACtC,IAAI,CAAC,SAAS,QAAQ,CAAC,YAAY,CAAC,SAAS,QAAQ,CAAC,SAAS;gBAC7D,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,kBAAkB;YAClB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,cAAc;YAC9B,SAAS,MAAM,CAAC,aAAa;YAC7B,SAAS,MAAM,CAAC,eAAe;YAC/B,SAAS,MAAM,CAAC,eAAe;YAE/B,8CAA8C;YAC9C,MAAM,MAAM,IAAI;YAEhB,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM;oBACnB;gBACF,OAAO;oBACL,IAAI;wBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,IAAI,YAAY;wBACjD,OAAO,IAAI,MAAM,cAAc,KAAK,EAAE,WAAW,cAAc,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBACnH,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBAC1D;gBACF;YACF;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,wBAAwB;YACxB,IAAI,YAAY;gBACd,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;oBACvB,IAAI,MAAM,gBAAgB,EAAE;wBAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;wBAC3D,WAAW;oBACb;gBACF;YACF;YAEA,sBAAsB;YACtB,IAAI,QAAQ;gBACV,OAAO,gBAAgB,CAAC,SAAS;oBAC/B,IAAI,KAAK;gBACX;YACF;YAEA,eAAe;YACf,IAAI,IAAI,CAAC,QAAQ,GAAG,eAAe,iBAAiB,CAAC,EAAE;YACvD,IAAI,IAAI,CAAC;QAEX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAKO,MAAM,oBAAoB,OAC/B,MACA,WACA,aACA,aAA+B,QAAQ,EACvC,aAAqB,kBAAkB,EACvC,QACA;IAEA,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;IACtC,MAAM,UAAU,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC;IAEhE,IAAI,SAAS;QACX,yCAAyC;QACzC,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,mBAAmB,MAAM,WAAW,aAAa,YAAY,YAAY,QAAQ;IAC1F,OAAO;QACL,sDAAsD;QACtD,OAAO,iBAAiB,MAAM,WAAW,aAAa,YAAY,YAAY,QAAQ;IACxF;AACF;AAKO,MAAM,qBAAqB;IAChC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,0BAA0B,CAAC;IAC1E,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;IAC5E;IACA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,qBAAqB,OAAO;IACvC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,oBAAoB,CAAC,EAAE;QACpE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC,cAAc;YAAE,cAAc;QAAY,IAAI,CAAC;IACtE;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;IAC5E;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,UAAU,IAAI,EAAE;AAC9B;AAKO,MAAM,wBAAwB,OAAO,WAAmB;IAC7D,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,gBAAgB,CAAC,EAAE;QAChE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB,YAAY;YACZ,aAAa;QACf;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;IACvE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,MAAM,IAAI;AACxB;AAEA;;CAEC,GACD,MAAM,sBAAsB;IAC1B,IAAI;QACF,wCAAmC,OAAO;;QAE1C,sCAAsC;QACtC,MAAM;QAGN,6BAA6B;QAC7B,MAAM;IAOR,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAKO,MAAM,aAAa,OACxB,OACA,WACA,IAAY,CAAC,EACb;IAEA,iCAAiC;IACjC,MAAM,aAAa,aAAa;IAEhC,MAAM,cAAmB;QACvB;QACA,YAAY;QACZ;IACF;IAEA,oDAAoD;IACpD,IAAI,YAAY;QACd,YAAY,UAAU,GAAG;IAC3B;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,gBAAgB,CAAC,EAAE;QAChE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;IACvE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,OAAO,IAAI,EAAE;AAC3B;AAKO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,kBAAkB,CAAC,EAAE;QAClE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE,WAAW;QAAS;IAC7C;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;IACvE;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,kBAAkB,CAAC,EAAE;QAClE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE,WAAW;QAAS;IAC7C;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;IACnE;AACF;AAKO,MAAM,aAAa,OACxB,WACA,QAAgB,GAAG,EACnB,SAAiB,CAAC;IAElB,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,iBAAiB,CAAC,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB,YAAY;YACZ;YACA;QACF;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,mBAAmB,CAAC,EAAE;QACnE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC,cAAc;YAAE,cAAc;QAAY,IAAI,CAAC;IACtE;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;IACpE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,KAAK,IAAI,EAAE;AACzB;AAKO,MAAM,iBAAiB,OAAO;IACnC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,qBAAqB,CAAC,EAAE;QACrE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC,WAAW;YAAE,WAAW;QAAS,IAAI,CAAC;IAC7D;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;IACtE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,WAAW,IAAI,EAAE;AAC/B;AAGO,MAAM,iBAAiB;IAC5B,wCAAmC,OAAO;;AAO5C;AAEO,MAAM,iBAAiB,CAAC;IAK7B,wCAAmC;;AAWrC"}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/services/uploadService.ts"], "sourcesContent": ["/**\r\n * Upload Service for ChatInputUpload component\r\n * Handles file and URL uploads to the Python FAISS backend\r\n * Enhanced with cache memory functionality for faster responses\r\n */\r\n\r\nimport axios from 'axios';\r\nimport { getFaissConfig } from './faissService';\r\n\r\n// Cache configuration\r\nconst CACHE_CONFIG = {\r\n  UPLOAD_CACHE_KEY: 'upload_cache',\r\n  QUERY_CACHE_KEY: 'query_cache',\r\n  CACHE_EXPIRY_MS: 24 * 60 * 60 * 1000, // 24 hours\r\n  MAX_CACHE_SIZE: 100, // Maximum number of cached items\r\n  ENABLE_CACHE: true\r\n};\r\n\r\n// Cache interface\r\ninterface CacheItem<T> {\r\n  data: T;\r\n  timestamp: number;\r\n  key: string;\r\n  expiresAt: number;\r\n}\r\n\r\ninterface UploadCacheData {\r\n  success: boolean;\r\n  message?: string;\r\n  error?: string;\r\n  upload_id?: string;\r\n  index_name?: string;\r\n  filename?: string;\r\n  fileHash?: string;\r\n  fileSize?: number;\r\n  processingTime?: number;\r\n}\r\n\r\n// Cache utility class\r\nclass CacheManager {\r\n  private static instance: CacheManager;\r\n\r\n  private constructor() {}\r\n\r\n  static getInstance(): CacheManager {\r\n    if (!CacheManager.instance) {\r\n      CacheManager.instance = new CacheManager();\r\n    }\r\n    return CacheManager.instance;\r\n  }\r\n\r\n  // Generate cache key for uploads\r\n  generateUploadCacheKey(type: string, data: File | string, options: any = {}): string {\r\n    if (data instanceof File) {\r\n      return `upload_${type}_${data.name}_${data.size}_${data.lastModified}_${options.index_name || 'default'}`;\r\n    } else {\r\n      return `upload_${type}_${this.hashString(data)}_${options.index_name || 'default'}`;\r\n    }\r\n  }\r\n\r\n  // Generate cache key for queries\r\n  generateQueryCacheKey(query: string, indexName: string, options: any = {}): string {\r\n    return `query_${this.hashString(query)}_${indexName}_${JSON.stringify(options)}`;\r\n  }\r\n\r\n  // Simple string hash function\r\n  private hashString(str: string): string {\r\n    let hash = 0;\r\n    for (let i = 0; i < str.length; i++) {\r\n      const char = str.charCodeAt(i);\r\n      hash = ((hash << 5) - hash) + char;\r\n      hash = hash & hash; // Convert to 32-bit integer\r\n    }\r\n    return Math.abs(hash).toString(36);\r\n  }\r\n\r\n  // Get cached item\r\n  get<T>(cacheKey: string, key: string): T | null {\r\n    if (!CACHE_CONFIG.ENABLE_CACHE || typeof window === 'undefined') return null;\r\n\r\n    try {\r\n      const cacheData = localStorage.getItem(cacheKey);\r\n      if (!cacheData) return null;\r\n\r\n      const cache: Record<string, CacheItem<T>> = JSON.parse(cacheData);\r\n      const item = cache[key];\r\n\r\n      if (!item) return null;\r\n\r\n      // Check if item has expired\r\n      if (Date.now() > item.expiresAt) {\r\n        this.remove(cacheKey, key);\r\n        return null;\r\n      }\r\n\r\n      console.log(`💾 Cache hit for key: ${key}`);\r\n      return item.data;\r\n    } catch (error) {\r\n      console.error('Error reading from cache:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Set cached item\r\n  set<T>(cacheKey: string, key: string, data: T, customExpiryMs?: number): void {\r\n    if (!CACHE_CONFIG.ENABLE_CACHE || typeof window === 'undefined') return;\r\n\r\n    try {\r\n      const expiryMs = customExpiryMs || CACHE_CONFIG.CACHE_EXPIRY_MS;\r\n      const cacheData = localStorage.getItem(cacheKey);\r\n      let cache: Record<string, CacheItem<T>> = {};\r\n\r\n      if (cacheData) {\r\n        cache = JSON.parse(cacheData);\r\n      }\r\n\r\n      // Clean expired items and enforce size limit\r\n      this.cleanCache(cache);\r\n\r\n      const item: CacheItem<T> = {\r\n        data,\r\n        timestamp: Date.now(),\r\n        key,\r\n        expiresAt: Date.now() + expiryMs\r\n      };\r\n\r\n      cache[key] = item;\r\n      localStorage.setItem(cacheKey, JSON.stringify(cache));\r\n      console.log(`💾 Cached item with key: ${key}`);\r\n    } catch (error) {\r\n      console.error('Error writing to cache:', error);\r\n    }\r\n  }\r\n\r\n  // Remove cached item\r\n  remove(cacheKey: string, key: string): void {\r\n    if (typeof window === 'undefined') return;\r\n\r\n    try {\r\n      const cacheData = localStorage.getItem(cacheKey);\r\n      if (!cacheData) return;\r\n\r\n      const cache = JSON.parse(cacheData);\r\n      delete cache[key];\r\n      localStorage.setItem(cacheKey, JSON.stringify(cache));\r\n    } catch (error) {\r\n      console.error('Error removing from cache:', error);\r\n    }\r\n  }\r\n\r\n  // Clean expired items and enforce size limit\r\n  private cleanCache<T>(cache: Record<string, CacheItem<T>>): void {\r\n    const now = Date.now();\r\n    const keys = Object.keys(cache);\r\n\r\n    // Remove expired items\r\n    keys.forEach(key => {\r\n      if (cache[key].expiresAt < now) {\r\n        delete cache[key];\r\n      }\r\n    });\r\n\r\n    // Enforce size limit by removing oldest items\r\n    const remainingKeys = Object.keys(cache);\r\n    if (remainingKeys.length > CACHE_CONFIG.MAX_CACHE_SIZE) {\r\n      const sortedKeys = remainingKeys.sort((a, b) => cache[a].timestamp - cache[b].timestamp);\r\n      const keysToRemove = sortedKeys.slice(0, remainingKeys.length - CACHE_CONFIG.MAX_CACHE_SIZE);\r\n      keysToRemove.forEach(key => delete cache[key]);\r\n    }\r\n  }\r\n\r\n  // Clear all cache\r\n  clearCache(cacheKey: string): void {\r\n    if (typeof window === 'undefined') return;\r\n    localStorage.removeItem(cacheKey);\r\n    console.log(`🗑️ Cleared cache: ${cacheKey}`);\r\n  }\r\n\r\n  // Get cache statistics\r\n  getCacheStats(cacheKey: string): { size: number; oldestItem: number; newestItem: number } {\r\n    if (typeof window === 'undefined') return { size: 0, oldestItem: 0, newestItem: 0 };\r\n\r\n    try {\r\n      const cacheData = localStorage.getItem(cacheKey);\r\n      if (!cacheData) return { size: 0, oldestItem: 0, newestItem: 0 };\r\n\r\n      const cache = JSON.parse(cacheData);\r\n      const items = Object.values(cache) as CacheItem<any>[];\r\n\r\n      if (items.length === 0) return { size: 0, oldestItem: 0, newestItem: 0 };\r\n\r\n      const timestamps = items.map(item => item.timestamp);\r\n      return {\r\n        size: items.length,\r\n        oldestItem: Math.min(...timestamps),\r\n        newestItem: Math.max(...timestamps)\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting cache stats:', error);\r\n      return { size: 0, oldestItem: 0, newestItem: 0 };\r\n    }\r\n  }\r\n}\r\n\r\n// Configure axios defaults for longer timeouts\r\nconst api = axios.create({\r\n  timeout: 120000, // 2 minutes timeout for long-running operations\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  withCredentials: false, // Disable credentials for CORS\r\n});\r\n\r\n// Initialize cache manager\r\nconst cacheManager = CacheManager.getInstance();\r\n\r\n// Add request interceptor for debugging\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    console.log('🚀 Making request to:', config.url);\r\n    console.log('📤 Request config:', {\r\n      method: config.method,\r\n      url: config.url,\r\n      headers: config.headers,\r\n      timeout: config.timeout\r\n    });\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error('❌ Request interceptor error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor for debugging\r\napi.interceptors.response.use(\r\n  (response) => {\r\n    console.log('✅ Response received:', {\r\n      status: response.status,\r\n      statusText: response.statusText,\r\n      url: response.config.url\r\n    });\r\n    return response;\r\n  },\r\n  (error) => {\r\n    console.error('❌ Response interceptor error:', {\r\n      message: error.message,\r\n      code: error.code,\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      url: error.config?.url,\r\n      data: error.response?.data\r\n    });\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Backend configuration - Use the same port as FAISS service for consistency\r\nconst BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5010';\r\n\r\n// API endpoints\r\nconst ENDPOINTS = {\r\n  PROCESS_YOUTUBE: '/api/process_youtube',\r\n  PROCESS_ARTICLE: '/api/process_article',\r\n  PROCESS_DOCUMENT: '/api/process_document',\r\n  PROCESS_PDF: '/api/process_pdf',\r\n  PROCESS_AUDIO: '/api/process_audio',\r\n  SEARCH: '/api/search',\r\n  HEALTH: '/api/health'\r\n};\r\n\r\n// Types\r\nexport interface UploadResponse {\r\n  success: boolean;\r\n  message?: string;\r\n  error?: string;\r\n  upload_id?: string;\r\n  index_name?: string;\r\n  filename?: string;\r\n}\r\n\r\nexport interface SearchResponse {\r\n  success: boolean;\r\n  results: SearchResult[];\r\n  query: string;\r\n  index_name?: string;\r\n  enhanced_response?: {\r\n    content: string;\r\n    model: string;\r\n    sources_used: number;\r\n  };\r\n  error?: string;\r\n  cached?: boolean;\r\n  searchTime?: number;\r\n}\r\n\r\nexport interface SearchResult {\r\n  score: number;\r\n  text: string;\r\n  source_type: string;\r\n  url: string;\r\n  title: string;\r\n  date: string;\r\n  category: string;\r\n  vector_id: string;\r\n  index_name?: string;\r\n}\r\n\r\nexport interface UploadOptions {\r\n  index_name?: string;\r\n  client_email?: string;\r\n}\r\n\r\n/**\r\n * Process YouTube URL with caching\r\n */\r\nexport async function processYouTubeURL(\r\n  url: string,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('🎥 Starting YouTube URL processing:', url);\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for YouTube processing: ${selectedIndex}`);\r\n\r\n    // Check cache first\r\n    const cacheKey = cacheManager.generateUploadCacheKey('youtube', url, { index_name: selectedIndex });\r\n    const cachedResult = cacheManager.get<UploadCacheData>(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey);\r\n\r\n    if (cachedResult) {\r\n      console.log('💾 Using cached YouTube processing result');\r\n      return {\r\n        success: cachedResult.success,\r\n        message: cachedResult.message + ' (from cache)',\r\n        error: cachedResult.error,\r\n        upload_id: cachedResult.upload_id,\r\n        index_name: cachedResult.index_name,\r\n        filename: cachedResult.filename\r\n      };\r\n    }\r\n\r\n    const requestData = {\r\n      url,\r\n      index_name: selectedIndex,\r\n      client_email: options.client_email || ''\r\n    };\r\n\r\n    console.log('📤 Sending request to:', `${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_YOUTUBE}`);\r\n    console.log('📤 Request data:', requestData);\r\n\r\n    const startTime = Date.now();\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_YOUTUBE}`, requestData);\r\n    const processingTime = Date.now() - startTime;\r\n\r\n    console.log('✅ YouTube processing response:', response.data);\r\n\r\n    // Cache successful results\r\n    if (response.data.success) {\r\n      const cacheData: UploadCacheData = {\r\n        success: response.data.success,\r\n        message: response.data.message,\r\n        upload_id: response.data.upload_id,\r\n        index_name: response.data.index_name,\r\n        filename: response.data.filename,\r\n        processingTime\r\n      };\r\n\r\n      // Cache for 24 hours for successful URL processing\r\n      cacheManager.set(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey, cacheData);\r\n      console.log('💾 Cached YouTube processing result');\r\n    }\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing YouTube URL:', error);\r\n\r\n    // Log detailed error information first\r\n    const errorDetails = {\r\n      message: error.message,\r\n      code: error.code,\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      url: error.config?.url,\r\n      method: error.config?.method\r\n    };\r\n    console.error('🔍 Detailed error information:', errorDetails);\r\n\r\n    // Handle network errors\r\n    if (error.code === 'ERR_NETWORK') {\r\n      console.error('🌐 Network error detected - backend may be unreachable');\r\n      return {\r\n        success: false,\r\n        error: `Network error - Cannot reach backend at ${BACKEND_BASE_URL}. Please check if the server is running on port 5010.`\r\n      };\r\n    }\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      console.error('⏰ Request timeout detected');\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the video is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    // Handle CORS errors\r\n    if (error.message?.includes('CORS') || error.code === 'ERR_BLOCKED_BY_CLIENT') {\r\n      console.error('🚫 CORS error detected');\r\n      return {\r\n        success: false,\r\n        error: 'CORS error - please check backend CORS configuration'\r\n      };\r\n    }\r\n\r\n    // Handle connection refused\r\n    if (error.code === 'ECONNREFUSED') {\r\n      console.error('🔌 Connection refused - backend server not running');\r\n      return {\r\n        success: false,\r\n        error: `Connection refused - Backend server is not running on ${BACKEND_BASE_URL}`\r\n      };\r\n    }\r\n\r\n    // Handle other specific error codes\r\n    if (error.response?.status === 404) {\r\n      console.error('🔍 Endpoint not found');\r\n      return {\r\n        success: false,\r\n        error: `Endpoint not found: ${ENDPOINTS.PROCESS_YOUTUBE}`\r\n      };\r\n    }\r\n\r\n    if (error.response?.status === 500) {\r\n      console.error('💥 Server error');\r\n      return {\r\n        success: false,\r\n        error: `Server error: ${error.response?.data?.error || 'Internal server error'}`\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process YouTube URL'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Article URL with caching\r\n */\r\nexport async function processArticleURL(\r\n  url: string,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('📰 Starting Article URL processing:', url);\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for Article processing: ${selectedIndex}`);\r\n\r\n    // Check cache first\r\n    const cacheKey = cacheManager.generateUploadCacheKey('article', url, { index_name: selectedIndex });\r\n    const cachedResult = cacheManager.get<UploadCacheData>(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey);\r\n\r\n    if (cachedResult) {\r\n      console.log('💾 Using cached Article processing result');\r\n      return {\r\n        success: cachedResult.success,\r\n        message: cachedResult.message + ' (from cache)',\r\n        error: cachedResult.error,\r\n        upload_id: cachedResult.upload_id,\r\n        index_name: cachedResult.index_name,\r\n        filename: cachedResult.filename\r\n      };\r\n    }\r\n\r\n    const startTime = Date.now();\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_ARTICLE}`, {\r\n      url,\r\n      index_name: selectedIndex,\r\n      client_email: options.client_email || ''\r\n    });\r\n    const processingTime = Date.now() - startTime;\r\n\r\n    console.log('✅ Article processing response:', response.data);\r\n\r\n    // Cache successful results\r\n    if (response.data.success) {\r\n      const cacheData: UploadCacheData = {\r\n        success: response.data.success,\r\n        message: response.data.message,\r\n        upload_id: response.data.upload_id,\r\n        index_name: response.data.index_name,\r\n        filename: response.data.filename,\r\n        processingTime\r\n      };\r\n\r\n      // Cache for 24 hours for successful URL processing\r\n      cacheManager.set(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey, cacheData);\r\n      console.log('💾 Cached Article processing result');\r\n    }\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing Article URL:', error);\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the article is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process Article URL'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Document file\r\n */\r\nexport async function processDocument(\r\n  file: File,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('📄 Starting Document processing:', file.name);\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for Document processing: ${selectedIndex}`);\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('index_name', selectedIndex);\r\n    formData.append('client_email', options.client_email || '');\r\n\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_DOCUMENT}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n      timeout: 180000, // 3 minutes for file uploads\r\n    });\r\n\r\n    console.log('✅ Document processing response:', response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing Document:', error);\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the document is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process Document'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process PDF file\r\n */\r\nexport async function processPDF(\r\n  file: File,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('📄 Starting PDF processing:', file.name);\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for PDF processing: ${selectedIndex}`);\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('index_name', selectedIndex);\r\n    formData.append('client_email', options.client_email || '');\r\n\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_PDF}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n      timeout: 180000, // 3 minutes for file uploads\r\n    });\r\n\r\n    console.log('✅ PDF processing response:', response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing PDF:', error);\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the PDF is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process PDF'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Audio file\r\n */\r\nexport async function processAudio(\r\n  file: File,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('🎵 Starting Audio processing:', file.name);\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for Audio processing: ${selectedIndex}`);\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('index_name', selectedIndex);\r\n    formData.append('client_email', options.client_email || '');\r\n\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_AUDIO}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n      timeout: 300000, // 5 minutes for audio processing (transcription takes time)\r\n    });\r\n\r\n    console.log('✅ Audio processing response:', response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing Audio:', error);\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the audio is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process Audio'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Search with enhanced DeepSeek integration and caching\r\n */\r\nexport async function searchContent(\r\n  query: string,\r\n  options: {\r\n    k?: number;\r\n    index_name?: string;\r\n    use_deepseek?: boolean;\r\n  } = {}\r\n): Promise<SearchResponse> {\r\n  try {\r\n    console.log('🔍 Starting content search:', query);\r\n\r\n    // Check cache first for search results\r\n    const cacheKey = cacheManager.generateQueryCacheKey(query, options.index_name || 'default', options);\r\n    const cachedResult = cacheManager.get<SearchResponse>(CACHE_CONFIG.QUERY_CACHE_KEY, cacheKey);\r\n\r\n    // Track cache statistics\r\n    cacheHitStats.totalRequests++;\r\n\r\n    if (cachedResult) {\r\n      console.log('💾 Using cached search result');\r\n      cacheHitStats.cacheHits++;\r\n      return {\r\n        ...cachedResult,\r\n        cached: true\r\n      };\r\n    }\r\n\r\n    cacheHitStats.cacheMisses++;\r\n\r\n    const startTime = Date.now();\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.SEARCH}`, {\r\n      query,\r\n      k: options.k || 5,\r\n      index_name: options.index_name,\r\n      use_deepseek: options.use_deepseek || false\r\n    });\r\n    const searchTime = Date.now() - startTime;\r\n\r\n    console.log('✅ Search response:', response.data);\r\n\r\n    const searchResult: SearchResponse = {\r\n      success: true,\r\n      searchTime,\r\n      ...response.data\r\n    };\r\n\r\n    // Cache successful search results for 1 hour\r\n    if (searchResult.success && searchResult.results && searchResult.results.length > 0) {\r\n      cacheManager.set(CACHE_CONFIG.QUERY_CACHE_KEY, cacheKey, searchResult, 60 * 60 * 1000); // 1 hour\r\n      console.log('💾 Cached search result');\r\n    }\r\n\r\n    return searchResult;\r\n  } catch (error: any) {\r\n    console.error('❌ Error searching content:', error);\r\n    return {\r\n      success: false,\r\n      results: [],\r\n      query,\r\n      error: error.response?.data?.error || error.message || 'Failed to search content'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Test basic connection to backend\r\n */\r\nexport async function testConnection(): Promise<{ success: boolean; message?: string; error?: string }> {\r\n  try {\r\n    console.log('🔌 Testing connection to backend...');\r\n    console.log('🎯 Backend URL:', BACKEND_BASE_URL);\r\n\r\n    // Try a simple GET request first\r\n    const response = await axios.get(`${BACKEND_BASE_URL}/api/list-faiss-indexes`, {\r\n      timeout: 10000,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      withCredentials: false\r\n    });\r\n\r\n    console.log('✅ Connection test successful:', response.data);\r\n    return {\r\n      success: true,\r\n      message: 'Backend connection successful'\r\n    };\r\n  } catch (error: any) {\r\n    console.error('❌ Connection test failed:', error);\r\n\r\n    // Provide detailed error information\r\n    const errorDetails = {\r\n      message: error.message,\r\n      code: error.code,\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      url: error.config?.url\r\n    };\r\n\r\n    console.error('🔍 Error details:', errorDetails);\r\n\r\n    return {\r\n      success: false,\r\n      error: `Connection failed: ${error.message} (${error.code || 'Unknown error'})`\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Check backend health\r\n */\r\nexport async function checkBackendHealth(): Promise<{ success: boolean; message?: string; error?: string }> {\r\n  try {\r\n    console.log('🏥 Checking backend health...');\r\n    const response = await api.get(`${BACKEND_BASE_URL}${ENDPOINTS.HEALTH}`, {\r\n      timeout: 10000 // 10 seconds for health check\r\n    });\r\n    console.log('✅ Backend health check successful:', response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Backend health check failed:', error);\r\n    return {\r\n      success: false,\r\n      error: error.message || 'Backend is not available'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get upload options with FAISS configuration\r\n */\r\nfunction getUploadOptionsWithConfig(options: UploadOptions = {}): UploadOptions {\r\n  const faissConfig = getFaissConfig();\r\n\r\n  return {\r\n    index_name: options.index_name || faissConfig?.indexName || 'default',\r\n    client_email: options.client_email || faissConfig?.clientEmail || ''\r\n  };\r\n}\r\n\r\n/**\r\n * Process PDF/Document file with unified handling\r\n */\r\nexport async function processPDFDocument(\r\n  file: File,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('📄 Starting PDF/Document processing:', file.name);\r\n\r\n    // Validate file type\r\n    const fileName = file.name.toLowerCase();\r\n    const isPDF = fileName.endsWith('.pdf');\r\n    const isDocument = fileName.endsWith('.doc') || fileName.endsWith('.docx') ||\r\n                     fileName.endsWith('.txt') || fileName.endsWith('.rtf');\r\n\r\n    if (!isPDF && !isDocument) {\r\n      return {\r\n        success: false,\r\n        error: 'Unsupported file type. Please upload PDF, DOC, DOCX, TXT, or RTF files.'\r\n      };\r\n    }\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for ${isPDF ? 'PDF' : 'Document'} processing: ${selectedIndex}`);\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('index_name', selectedIndex);\r\n    formData.append('client_email', options.client_email || '');\r\n\r\n    // Choose the appropriate endpoint based on file type\r\n    const endpoint = isPDF ? ENDPOINTS.PROCESS_PDF : ENDPOINTS.PROCESS_DOCUMENT;\r\n\r\n    console.log(`📤 Sending ${isPDF ? 'PDF' : 'Document'} to endpoint:`, `${BACKEND_BASE_URL}${endpoint}`);\r\n\r\n    const response = await api.post(`${BACKEND_BASE_URL}${endpoint}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n      timeout: 180000, // 3 minutes for file uploads\r\n    });\r\n\r\n    console.log(`✅ ${isPDF ? 'PDF' : 'Document'} processing response:`, response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing PDF/Document:', error);\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the file is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    // Handle network errors\r\n    if (error.code === 'ERR_NETWORK') {\r\n      return {\r\n        success: false,\r\n        error: `Network error - Cannot reach backend at ${BACKEND_BASE_URL}. Please check if the server is running.`\r\n      };\r\n    }\r\n\r\n    // Handle file size errors\r\n    if (error.response?.status === 413) {\r\n      return {\r\n        success: false,\r\n        error: 'File too large. Please upload a file smaller than 50MB.'\r\n      };\r\n    }\r\n\r\n    // Handle unsupported file type errors\r\n    if (error.response?.status === 400 && error.response?.data?.error?.includes('Unsupported file type')) {\r\n      return {\r\n        success: false,\r\n        error: 'Unsupported file type. Please upload PDF, DOC, DOCX, TXT, or RTF files.'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process file'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Upload handler that routes to appropriate processor based on type\r\n */\r\nexport async function handleUpload(\r\n  type: 'pdf' | 'youtube' | 'article' | 'mp3',\r\n  data: File | string,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  // Merge with FAISS configuration\r\n  const uploadOptions = getUploadOptionsWithConfig(options);\r\n\r\n  switch (type) {\r\n    case 'youtube':\r\n      return processYouTubeURL(data as string, uploadOptions);\r\n    case 'article':\r\n      return processArticleURL(data as string, uploadOptions);\r\n    case 'pdf':\r\n      // Use the unified PDF/Document processor\r\n      return processPDFDocument(data as File, uploadOptions);\r\n    case 'mp3':\r\n      return processAudio(data as File, uploadOptions);\r\n    default:\r\n      return {\r\n        success: false,\r\n        error: `Unsupported upload type: ${type}`\r\n      };\r\n  }\r\n}\r\n\r\n// Cache hit rate tracking\r\nlet cacheHitStats = {\r\n  totalRequests: 0,\r\n  cacheHits: 0,\r\n  cacheMisses: 0\r\n};\r\n\r\n// Cache utility functions for external use\r\nexport const CacheUtils = {\r\n  /**\r\n   * Clear all upload cache\r\n   */\r\n  clearUploadCache: () => {\r\n    cacheManager.clearCache(CACHE_CONFIG.UPLOAD_CACHE_KEY);\r\n  },\r\n\r\n  /**\r\n   * Clear all query cache\r\n   */\r\n  clearQueryCache: () => {\r\n    cacheManager.clearCache(CACHE_CONFIG.QUERY_CACHE_KEY);\r\n  },\r\n\r\n  /**\r\n   * Clear all caches\r\n   */\r\n  clearAllCache: () => {\r\n    cacheManager.clearCache(CACHE_CONFIG.UPLOAD_CACHE_KEY);\r\n    cacheManager.clearCache(CACHE_CONFIG.QUERY_CACHE_KEY);\r\n  },\r\n\r\n  /**\r\n   * Get cache statistics\r\n   */\r\n  getCacheStats: () => {\r\n    return {\r\n      upload: cacheManager.getCacheStats(CACHE_CONFIG.UPLOAD_CACHE_KEY),\r\n      query: cacheManager.getCacheStats(CACHE_CONFIG.QUERY_CACHE_KEY)\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Get cache hit rate statistics\r\n   */\r\n  getCacheHitRate: () => {\r\n    const hitRate = cacheHitStats.totalRequests > 0\r\n      ? (cacheHitStats.cacheHits / cacheHitStats.totalRequests * 100).toFixed(2)\r\n      : '0.00';\r\n\r\n    return {\r\n      ...cacheHitStats,\r\n      hitRate: `${hitRate}%`\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Reset cache hit rate statistics\r\n   */\r\n  resetCacheHitStats: () => {\r\n    cacheHitStats = {\r\n      totalRequests: 0,\r\n      cacheHits: 0,\r\n      cacheMisses: 0\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Check if caching is enabled\r\n   */\r\n  isCacheEnabled: () => CACHE_CONFIG.ENABLE_CACHE,\r\n\r\n  /**\r\n   * Toggle cache functionality\r\n   */\r\n  toggleCache: (enabled: boolean) => {\r\n    CACHE_CONFIG.ENABLE_CACHE = enabled;\r\n    console.log(`💾 Cache ${enabled ? 'enabled' : 'disabled'}`);\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;AAGD;AADA;;;AAGA,sBAAsB;AACtB,MAAM,eAAe;IACnB,kBAAkB;IAClB,iBAAiB;IACjB,iBAAiB,KAAK,KAAK,KAAK;IAChC,gBAAgB;IAChB,cAAc;AAChB;AAsBA,sBAAsB;AACtB,MAAM;IACJ,OAAe,SAAuB;IAEtC,aAAsB,CAAC;IAEvB,OAAO,cAA4B;QACjC,IAAI,CAAC,aAAa,QAAQ,EAAE;YAC1B,aAAa,QAAQ,GAAG,IAAI;QAC9B;QACA,OAAO,aAAa,QAAQ;IAC9B;IAEA,iCAAiC;IACjC,uBAAuB,IAAY,EAAE,IAAmB,EAAE,UAAe,CAAC,CAAC,EAAU;QACnF,IAAI,gBAAgB,MAAM;YACxB,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,YAAY,CAAC,CAAC,EAAE,QAAQ,UAAU,IAAI,WAAW;QAC3G,OAAO;YACL,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,QAAQ,UAAU,IAAI,WAAW;QACrF;IACF;IAEA,iCAAiC;IACjC,sBAAsB,KAAa,EAAE,SAAiB,EAAE,UAAe,CAAC,CAAC,EAAU;QACjF,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;IAClF;IAEA,8BAA8B;IACtB,WAAW,GAAW,EAAU;QACtC,IAAI,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,MAAM,OAAO,IAAI,UAAU,CAAC;YAC5B,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ;YAC9B,OAAO,OAAO,MAAM,4BAA4B;QAClD;QACA,OAAO,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC;IACjC;IAEA,kBAAkB;IAClB,IAAO,QAAgB,EAAE,GAAW,EAAY;QAC9C,wCAAiE,OAAO;;IAuB1E;IAEA,kBAAkB;IAClB,IAAO,QAAgB,EAAE,GAAW,EAAE,IAAO,EAAE,cAAuB,EAAQ;QAC5E,wCAAiE;;IA2BnE;IAEA,qBAAqB;IACrB,OAAO,QAAgB,EAAE,GAAW,EAAQ;QAC1C,wCAAmC;;IAYrC;IAEA,6CAA6C;IACrC,WAAc,KAAmC,EAAQ;QAC/D,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAO,OAAO,IAAI,CAAC;QAEzB,uBAAuB;QACvB,KAAK,OAAO,CAAC,CAAA;YACX,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK;gBAC9B,OAAO,KAAK,CAAC,IAAI;YACnB;QACF;QAEA,8CAA8C;QAC9C,MAAM,gBAAgB,OAAO,IAAI,CAAC;QAClC,IAAI,cAAc,MAAM,GAAG,aAAa,cAAc,EAAE;YACtD,MAAM,aAAa,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,SAAS;YACvF,MAAM,eAAe,WAAW,KAAK,CAAC,GAAG,cAAc,MAAM,GAAG,aAAa,cAAc;YAC3F,aAAa,OAAO,CAAC,CAAA,MAAO,OAAO,KAAK,CAAC,IAAI;QAC/C;IACF;IAEA,kBAAkB;IAClB,WAAW,QAAgB,EAAQ;QACjC,wCAAmC;;IAGrC;IAEA,uBAAuB;IACvB,cAAc,QAAgB,EAA4D;QACxF,wCAAmC,OAAO;YAAE,MAAM;YAAG,YAAY;YAAG,YAAY;QAAE;;IAqBpF;AACF;AAEA,+CAA+C;AAC/C,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,iBAAiB;AACnB;AAEA,2BAA2B;AAC3B,MAAM,eAAe,aAAa,WAAW;AAE7C,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,QAAQ,GAAG,CAAC,yBAAyB,OAAO,GAAG;IAC/C,QAAQ,GAAG,CAAC,sBAAsB;QAChC,QAAQ,OAAO,MAAM;QACrB,KAAK,OAAO,GAAG;QACf,SAAS,OAAO,OAAO;QACvB,SAAS,OAAO,OAAO;IACzB;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,gCAAgC;IAC9C,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,yCAAyC;AACzC,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC;IACC,QAAQ,GAAG,CAAC,wBAAwB;QAClC,QAAQ,SAAS,MAAM;QACvB,YAAY,SAAS,UAAU;QAC/B,KAAK,SAAS,MAAM,CAAC,GAAG;IAC1B;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,iCAAiC;QAC7C,SAAS,MAAM,OAAO;QACtB,MAAM,MAAM,IAAI;QAChB,QAAQ,MAAM,QAAQ,EAAE;QACxB,YAAY,MAAM,QAAQ,EAAE;QAC5B,KAAK,MAAM,MAAM,EAAE;QACnB,MAAM,MAAM,QAAQ,EAAE;IACxB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,6EAA6E;AAC7E,MAAM,mBAAmB,6DAAuC;AAEhE,gBAAgB;AAChB,MAAM,YAAY;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,aAAa;IACb,eAAe;IACf,QAAQ;IACR,QAAQ;AACV;AA+CO,eAAe,kBACpB,GAAW,EACX,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,eAAe;QAErE,oBAAoB;QACpB,MAAM,WAAW,aAAa,sBAAsB,CAAC,WAAW,KAAK;YAAE,YAAY;QAAc;QACjG,MAAM,eAAe,aAAa,GAAG,CAAkB,aAAa,gBAAgB,EAAE;QAEtF,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS,aAAa,OAAO;gBAC7B,SAAS,aAAa,OAAO,GAAG;gBAChC,OAAO,aAAa,KAAK;gBACzB,WAAW,aAAa,SAAS;gBACjC,YAAY,aAAa,UAAU;gBACnC,UAAU,aAAa,QAAQ;YACjC;QACF;QAEA,MAAM,cAAc;YAClB;YACA,YAAY;YACZ,cAAc,QAAQ,YAAY,IAAI;QACxC;QAEA,QAAQ,GAAG,CAAC,0BAA0B,GAAG,mBAAmB,UAAU,eAAe,EAAE;QACvF,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,eAAe,EAAE,EAAE;QACnF,MAAM,iBAAiB,KAAK,GAAG,KAAK;QAEpC,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;QAE3D,2BAA2B;QAC3B,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;YACzB,MAAM,YAA6B;gBACjC,SAAS,SAAS,IAAI,CAAC,OAAO;gBAC9B,SAAS,SAAS,IAAI,CAAC,OAAO;gBAC9B,WAAW,SAAS,IAAI,CAAC,SAAS;gBAClC,YAAY,SAAS,IAAI,CAAC,UAAU;gBACpC,UAAU,SAAS,IAAI,CAAC,QAAQ;gBAChC;YACF;YAEA,mDAAmD;YACnD,aAAa,GAAG,CAAC,aAAa,gBAAgB,EAAE,UAAU;YAC1D,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,uCAAuC;QACvC,MAAM,eAAe;YACnB,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,MAAM,MAAM,QAAQ,EAAE;YACtB,KAAK,MAAM,MAAM,EAAE;YACnB,QAAQ,MAAM,MAAM,EAAE;QACxB;QACA,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,wBAAwB;QACxB,IAAI,MAAM,IAAI,KAAK,eAAe;YAChC,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,wCAAwC,EAAE,iBAAiB,qDAAqD,CAAC;YAC3H;QACF;QAEA,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,qBAAqB;QACrB,IAAI,MAAM,OAAO,EAAE,SAAS,WAAW,MAAM,IAAI,KAAK,yBAAyB;YAC7E,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,4BAA4B;QAC5B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,sDAAsD,EAAE,kBAAkB;YACpF;QACF;QAEA,oCAAoC;QACpC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,oBAAoB,EAAE,UAAU,eAAe,EAAE;YAC3D;QACF;QAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,MAAM,QAAQ,EAAE,MAAM,SAAS,yBAAyB;YAClF;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,kBACpB,GAAW,EACX,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,eAAe;QAErE,oBAAoB;QACpB,MAAM,WAAW,aAAa,sBAAsB,CAAC,WAAW,KAAK;YAAE,YAAY;QAAc;QACjG,MAAM,eAAe,aAAa,GAAG,CAAkB,aAAa,gBAAgB,EAAE;QAEtF,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS,aAAa,OAAO;gBAC7B,SAAS,aAAa,OAAO,GAAG;gBAChC,OAAO,aAAa,KAAK;gBACzB,WAAW,aAAa,SAAS;gBACjC,YAAY,aAAa,UAAU;gBACnC,UAAU,aAAa,QAAQ;YACjC;QACF;QAEA,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,eAAe,EAAE,EAAE;YACjF;YACA,YAAY;YACZ,cAAc,QAAQ,YAAY,IAAI;QACxC;QACA,MAAM,iBAAiB,KAAK,GAAG,KAAK;QAEpC,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;QAE3D,2BAA2B;QAC3B,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;YACzB,MAAM,YAA6B;gBACjC,SAAS,SAAS,IAAI,CAAC,OAAO;gBAC9B,SAAS,SAAS,IAAI,CAAC,OAAO;gBAC9B,WAAW,SAAS,IAAI,CAAC,SAAS;gBAClC,YAAY,SAAS,IAAI,CAAC,UAAU;gBACpC,UAAU,SAAS,IAAI,CAAC,QAAQ;gBAChC;YACF;YAEA,mDAAmD;YACnD,aAAa,GAAG,CAAC,aAAa,gBAAgB,EAAE,UAAU;YAC1D,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,gBACpB,IAAU,EACV,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,oCAAoC,KAAK,IAAI;QAEzD,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,eAAe;QAEtE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,gBAAgB,QAAQ,YAAY,IAAI;QAExD,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,gBAAgB,EAAE,EAAE,UAAU;YAC5F,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,mCAAmC,SAAS,IAAI;QAC5D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,WACpB,IAAU,EACV,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,+BAA+B,KAAK,IAAI;QAEpD,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,eAAe;QAEjE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,gBAAgB,QAAQ,YAAY,IAAI;QAExD,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,WAAW,EAAE,EAAE,UAAU;YACvF,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;QACvD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,aACpB,IAAU,EACV,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,iCAAiC,KAAK,IAAI;QAEtD,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,eAAe;QAEnE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,gBAAgB,QAAQ,YAAY,IAAI;QAExD,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,aAAa,EAAE,EAAE,UAAU;YACzF,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,gCAAgC,SAAS,IAAI;QACzD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6BAA6B;QAE3C,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,cACpB,KAAa,EACb,UAII,CAAC,CAAC;IAEN,IAAI;QACF,QAAQ,GAAG,CAAC,+BAA+B;QAE3C,uCAAuC;QACvC,MAAM,WAAW,aAAa,qBAAqB,CAAC,OAAO,QAAQ,UAAU,IAAI,WAAW;QAC5F,MAAM,eAAe,aAAa,GAAG,CAAiB,aAAa,eAAe,EAAE;QAEpF,yBAAyB;QACzB,cAAc,aAAa;QAE3B,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC;YACZ,cAAc,SAAS;YACvB,OAAO;gBACL,GAAG,YAAY;gBACf,QAAQ;YACV;QACF;QAEA,cAAc,WAAW;QAEzB,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,MAAM,EAAE,EAAE;YACxE;YACA,GAAG,QAAQ,CAAC,IAAI;YAChB,YAAY,QAAQ,UAAU;YAC9B,cAAc,QAAQ,YAAY,IAAI;QACxC;QACA,MAAM,aAAa,KAAK,GAAG,KAAK;QAEhC,QAAQ,GAAG,CAAC,sBAAsB,SAAS,IAAI;QAE/C,MAAM,eAA+B;YACnC,SAAS;YACT;YACA,GAAG,SAAS,IAAI;QAClB;QAEA,6CAA6C;QAC7C,IAAI,aAAa,OAAO,IAAI,aAAa,OAAO,IAAI,aAAa,OAAO,CAAC,MAAM,GAAG,GAAG;YACnF,aAAa,GAAG,CAAC,aAAa,eAAe,EAAE,UAAU,cAAc,KAAK,KAAK,OAAO,SAAS;YACjG,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,SAAS;YACT,SAAS,EAAE;YACX;YACA,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,mBAAmB;QAE/B,iCAAiC;QACjC,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,iBAAiB,uBAAuB,CAAC,EAAE;YAC7E,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;YACA,iBAAiB;QACnB;QAEA,QAAQ,GAAG,CAAC,iCAAiC,SAAS,IAAI;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6BAA6B;QAE3C,qCAAqC;QACrC,MAAM,eAAe;YACnB,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,KAAK,MAAM,MAAM,EAAE;QACrB;QAEA,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,OAAO;YACL,SAAS;YACT,OAAO,CAAC,mBAAmB,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,MAAM,IAAI,IAAI,gBAAgB,CAAC,CAAC;QACjF;IACF;AACF;AAKO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,GAAG,mBAAmB,UAAU,MAAM,EAAE,EAAE;YACvE,SAAS,MAAM,8BAA8B;QAC/C;QACA,QAAQ,GAAG,CAAC,sCAAsC,SAAS,IAAI;QAC/D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,OAAO,MAAM,OAAO,IAAI;QAC1B;IACF;AACF;AAEA;;CAEC,GACD,SAAS,2BAA2B,UAAyB,CAAC,CAAC;IAC7D,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL,YAAY,QAAQ,UAAU,IAAI,aAAa,aAAa;QAC5D,cAAc,QAAQ,YAAY,IAAI,aAAa,eAAe;IACpE;AACF;AAKO,eAAe,mBACpB,IAAU,EACV,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,wCAAwC,KAAK,IAAI;QAE7D,qBAAqB;QACrB,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;QACtC,MAAM,QAAQ,SAAS,QAAQ,CAAC;QAChC,MAAM,aAAa,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,YACjD,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC;QAEhE,IAAI,CAAC,SAAS,CAAC,YAAY;YACzB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ,QAAQ,WAAW,aAAa,EAAE,eAAe;QAE3F,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,gBAAgB,QAAQ,YAAY,IAAI;QAExD,qDAAqD;QACrD,MAAM,WAAW,QAAQ,UAAU,WAAW,GAAG,UAAU,gBAAgB;QAE3E,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,QAAQ,WAAW,aAAa,CAAC,EAAE,GAAG,mBAAmB,UAAU;QAErG,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,EAAE,UAAU;YAC1E,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,QAAQ,QAAQ,WAAW,qBAAqB,CAAC,EAAE,SAAS,IAAI;QACjF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oCAAoC;QAElD,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,wBAAwB;QACxB,IAAI,MAAM,IAAI,KAAK,eAAe;YAChC,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,wCAAwC,EAAE,iBAAiB,wCAAwC,CAAC;YAC9G;QACF;QAEA,0BAA0B;QAC1B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,sCAAsC;QACtC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,MAAM,QAAQ,EAAE,MAAM,OAAO,SAAS,0BAA0B;YACpG,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,aACpB,IAA2C,EAC3C,IAAmB,EACnB,UAAyB,CAAC,CAAC;IAE3B,iCAAiC;IACjC,MAAM,gBAAgB,2BAA2B;IAEjD,OAAQ;QACN,KAAK;YACH,OAAO,kBAAkB,MAAgB;QAC3C,KAAK;YACH,OAAO,kBAAkB,MAAgB;QAC3C,KAAK;YACH,yCAAyC;YACzC,OAAO,mBAAmB,MAAc;QAC1C,KAAK;YACH,OAAO,aAAa,MAAc;QACpC;YACE,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,yBAAyB,EAAE,MAAM;YAC3C;IACJ;AACF;AAEA,0BAA0B;AAC1B,IAAI,gBAAgB;IAClB,eAAe;IACf,WAAW;IACX,aAAa;AACf;AAGO,MAAM,aAAa;IACxB;;GAEC,GACD,kBAAkB;QAChB,aAAa,UAAU,CAAC,aAAa,gBAAgB;IACvD;IAEA;;GAEC,GACD,iBAAiB;QACf,aAAa,UAAU,CAAC,aAAa,eAAe;IACtD;IAEA;;GAEC,GACD,eAAe;QACb,aAAa,UAAU,CAAC,aAAa,gBAAgB;QACrD,aAAa,UAAU,CAAC,aAAa,eAAe;IACtD;IAEA;;GAEC,GACD,eAAe;QACb,OAAO;YACL,QAAQ,aAAa,aAAa,CAAC,aAAa,gBAAgB;YAChE,OAAO,aAAa,aAAa,CAAC,aAAa,eAAe;QAChE;IACF;IAEA;;GAEC,GACD,iBAAiB;QACf,MAAM,UAAU,cAAc,aAAa,GAAG,IAC1C,CAAC,cAAc,SAAS,GAAG,cAAc,aAAa,GAAG,GAAG,EAAE,OAAO,CAAC,KACtE;QAEJ,OAAO;YACL,GAAG,aAAa;YAChB,SAAS,GAAG,QAAQ,CAAC,CAAC;QACxB;IACF;IAEA;;GAEC,GACD,oBAAoB;QAClB,gBAAgB;YACd,eAAe;YACf,WAAW;YACX,aAAa;QACf;IACF;IAEA;;GAEC,GACD,gBAAgB,IAAM,aAAa,YAAY;IAE/C;;GAEC,GACD,aAAa,CAAC;QACZ,aAAa,YAAY,GAAG;QAC5B,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,YAAY,YAAY;IAC5D;AACF"}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/chatComponents/ChatInputUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Pi<PERSON>, Pi<PERSON>heck<PERSON><PERSON><PERSON>, PiWarning, PiDatabase } from 'react-icons/pi';\r\nimport UploadDropdown from './UploadDropdown';\r\nimport FileDropZone from './FileDropZone';\r\nimport URLInput from './URLInput';\r\nimport { handleUpload, UploadResponse } from '../../services/uploadService';\r\nimport toast from 'react-hot-toast';\r\n\r\ninterface ChatInputUploadProps {\r\n  selectedLanguage: string;\r\n  onFileUpload?: (files: File[]) => void;\r\n  onURLSubmit?: (url: string, type: 'youtube' | 'article') => void;\r\n  onUploadStateChange?: (isActive: boolean, showDropdown: boolean) => void;\r\n  onNetworkError?: () => void; // Callback for network errors\r\n  disabled?: boolean;\r\n}\r\n\r\nexport type UploadType = 'pdf' | 'youtube' | 'article' | 'mp3';\r\n\r\ninterface UploadState {\r\n  type: UploadType | null;\r\n  files: File[];\r\n  url: string;\r\n  isActive: boolean;\r\n  isProcessing: boolean;\r\n  uploadResult: UploadResponse | null;\r\n}\r\n\r\nconst ChatInputUpload: React.FC<ChatInputUploadProps> = ({\r\n  selectedLanguage,\r\n  onFileUpload,\r\n  onURLSubmit,\r\n  onUploadStateChange,\r\n  onNetworkError,\r\n  disabled = false\r\n}) => {\r\n  const [showDropdown, setShowDropdown] = useState(false);\r\n  const [uploadState, setUploadState] = useState<UploadState>({\r\n    type: null,\r\n    files: [],\r\n    url: '',\r\n    isActive: false,\r\n    isProcessing: false,\r\n    uploadResult: null\r\n  });\r\n\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n  const iconRef = useRef<HTMLButtonElement>(null);\r\n  const autoCloseTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Close dropdown when clicking outside or pressing Escape\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (\r\n        dropdownRef.current &&\r\n        !dropdownRef.current.contains(event.target as Node) &&\r\n        iconRef.current &&\r\n        !iconRef.current.contains(event.target as Node)\r\n      ) {\r\n        setShowDropdown(false);\r\n      }\r\n    }\r\n\r\n    function handleKeyDown(event: KeyboardEvent) {\r\n      if (event.key === 'Escape' && showDropdown) {\r\n        setShowDropdown(false);\r\n        // Return focus to the pin button\r\n        iconRef.current?.focus();\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    document.addEventListener('keydown', handleKeyDown);\r\n\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n      document.removeEventListener('keydown', handleKeyDown);\r\n    };\r\n  }, [showDropdown]);\r\n\r\n  // Cleanup timeout on component unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (autoCloseTimeoutRef.current) {\r\n        clearTimeout(autoCloseTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Notify parent about upload state changes\r\n  useEffect(() => {\r\n    if (onUploadStateChange) {\r\n      onUploadStateChange(uploadState.isActive, showDropdown);\r\n    }\r\n  }, [uploadState.isActive, showDropdown, onUploadStateChange]);\r\n\r\n  const handleUploadTypeSelect = (type: UploadType) => {\r\n    // If switching to a different type, clear previous content\r\n    const isSwitchingType = uploadState.isActive && uploadState.type !== type;\r\n\r\n    setUploadState({\r\n      type,\r\n      files: isSwitchingType ? [] : uploadState.files,\r\n      url: isSwitchingType ? '' : uploadState.url,\r\n      isActive: true,\r\n      isProcessing: false,\r\n      uploadResult: null\r\n    });\r\n    setShowDropdown(false);\r\n\r\n    // If switching types, clear previous uploads\r\n    if (isSwitchingType) {\r\n      if (onFileUpload) {\r\n        onFileUpload([]);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleAddMoreContent = () => {\r\n    // Allow adding more content of the same type or switching types\r\n    setShowDropdown(!showDropdown);\r\n  };\r\n\r\n  const getUploadStatusText = () => {\r\n    if (!uploadState.isActive) return '';\r\n\r\n    // Show processing status\r\n    if (uploadState.isProcessing) {\r\n      return 'Processing...';\r\n    }\r\n\r\n    // Show upload result status\r\n    if (uploadState.uploadResult) {\r\n      if (uploadState.uploadResult.success) {\r\n        return '✅ Processed successfully';\r\n      } else {\r\n        return '❌ Processing failed';\r\n      }\r\n    }\r\n\r\n    const fileCount = uploadState.files.length;\r\n    const hasUrl = uploadState.url.trim().length > 0;\r\n\r\n    if (uploadState.type === 'pdf' || uploadState.type === 'mp3') {\r\n      return fileCount > 0 ? `${fileCount} file${fileCount > 1 ? 's' : ''} selected` : 'No files selected';\r\n    } else {\r\n      return hasUrl ? 'URL added' : 'No URL added';\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = async (files: File[]) => {\r\n    if (!files.length || !uploadState.type || (uploadState.type !== 'pdf' && uploadState.type !== 'mp3')) {\r\n      return;\r\n    }\r\n\r\n    const file = files[0];\r\n    console.log('📁 Files selected for upload:', files);\r\n\r\n    // Validate file type and size before processing\r\n    const fileName = file.name.toLowerCase();\r\n    const fileSize = file.size;\r\n    const maxSize = 50 * 1024 * 1024; // 50MB\r\n\r\n    // Check file size\r\n    if (fileSize > maxSize) {\r\n      toast.error('File too large. Please upload a file smaller than 50MB.');\r\n      return;\r\n    }\r\n\r\n    // Validate file type based on upload type\r\n    if (uploadState.type === 'pdf') {\r\n      const isPDF = fileName.endsWith('.pdf');\r\n      const isDocument = fileName.endsWith('.doc') || fileName.endsWith('.docx') ||\r\n                        fileName.endsWith('.txt') || fileName.endsWith('.rtf');\r\n\r\n      if (!isPDF && !isDocument) {\r\n        toast.error('Please upload PDF, DOC, DOCX, TXT, or RTF files only.');\r\n        return;\r\n      }\r\n    } else if (uploadState.type === 'mp3') {\r\n      const isAudio = fileName.endsWith('.mp3') || fileName.endsWith('.wav') ||\r\n                     fileName.endsWith('.m4a') || fileName.endsWith('.flac');\r\n\r\n      if (!isAudio) {\r\n        toast.error('Please upload MP3, WAV, M4A, or FLAC audio files only.');\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Update state with files and processing status\r\n    setUploadState(prev => ({\r\n      ...prev,\r\n      files,\r\n      isProcessing: true,\r\n      uploadResult: null\r\n    }));\r\n\r\n    try {\r\n      console.log(`🚀 Starting ${uploadState.type} upload for file:`, file.name);\r\n\r\n      const result = await handleUpload(uploadState.type, file);\r\n      console.log('📥 Upload result:', result);\r\n\r\n      // Update state with result\r\n      setUploadState(prev => ({\r\n        ...prev,\r\n        isProcessing: false,\r\n        uploadResult: result\r\n      }));\r\n\r\n      // Show success/error toast with more specific messages\r\n      if (result.success) {\r\n        const fileType = uploadState.type === 'pdf' ? 'PDF/Document' : 'Audio';\r\n        const cacheMessage = result.message?.includes('cache') ? ' (from cache)' : '';\r\n        toast.success(`${fileType} file processed successfully${cacheMessage}! Content has been indexed.`);\r\n\r\n        // Call the original callback if provided\r\n        if (onFileUpload) {\r\n          onFileUpload(files);\r\n        }\r\n\r\n        // Auto-close popup after successful upload with a slight delay for user feedback\r\n        autoCloseTimeoutRef.current = setTimeout(() => {\r\n          handleClearUpload();\r\n        }, 2000); // 2 second delay to show success state\r\n      } else {\r\n        const errorMessage = result.error || 'Failed to process file';\r\n        console.error('❌ Upload failed:', errorMessage);\r\n        toast.error(errorMessage);\r\n\r\n        // Check if it's a network error and trigger connection test\r\n        if (errorMessage.includes('Network error') || errorMessage.includes('Connection refused')) {\r\n          if (onNetworkError) {\r\n            onNetworkError();\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Upload error:', error);\r\n      setUploadState(prev => ({\r\n        ...prev,\r\n        isProcessing: false,\r\n        uploadResult: { success: false, error: 'Upload failed due to network error' }\r\n      }));\r\n      toast.error('Upload failed due to network error. Please check your connection.');\r\n\r\n      // Call network error callback if provided\r\n      if (onNetworkError) {\r\n        onNetworkError();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleURLChange = (url: string) => {\r\n    setUploadState(prev => ({\r\n      ...prev,\r\n      url\r\n    }));\r\n  };\r\n\r\n  const handleURLSubmit = async (url: string) => {\r\n    if (!uploadState.type || (uploadState.type !== 'youtube' && uploadState.type !== 'article')) {\r\n      return;\r\n    }\r\n\r\n    // Set processing state\r\n    setUploadState(prev => ({ ...prev, isProcessing: true, uploadResult: null }));\r\n\r\n    try {\r\n      // Process the URL using the upload service\r\n      const result = await handleUpload(uploadState.type, url);\r\n\r\n      // Update state with result\r\n      setUploadState(prev => ({\r\n        ...prev,\r\n        isProcessing: false,\r\n        uploadResult: result\r\n      }));\r\n\r\n      // Show success/error toast\r\n      if (result.success) {\r\n        const cacheMessage = result.message?.includes('cache') ? ' (from cache)' : '';\r\n        toast.success(`${uploadState.type === 'youtube' ? 'YouTube video' : 'Article'} processed successfully${cacheMessage}!`);\r\n        // Call the original callback if provided\r\n        if (onURLSubmit) {\r\n          onURLSubmit(url, uploadState.type);\r\n        }\r\n\r\n        // Auto-close popup after successful upload with a slight delay for user feedback\r\n        autoCloseTimeoutRef.current = setTimeout(() => {\r\n          handleClearUpload();\r\n        }, 2000); // 2 second delay to show success state\r\n      } else {\r\n        toast.error(result.error || 'Failed to process URL');\r\n\r\n        // Check if it's a network error and trigger connection test\r\n        if (result.error?.includes('Network error') || result.error?.includes('Connection refused')) {\r\n          if (onNetworkError) {\r\n            onNetworkError();\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Upload error:', error);\r\n      setUploadState(prev => ({\r\n        ...prev,\r\n        isProcessing: false,\r\n        uploadResult: { success: false, error: 'Upload failed' }\r\n      }));\r\n      toast.error('Upload failed');\r\n\r\n      // Trigger network error callback for any catch block errors\r\n      if (onNetworkError) {\r\n        onNetworkError();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleClearUpload = () => {\r\n    // Clear any pending auto-close timeout\r\n    if (autoCloseTimeoutRef.current) {\r\n      clearTimeout(autoCloseTimeoutRef.current);\r\n      autoCloseTimeoutRef.current = null;\r\n    }\r\n\r\n    setUploadState({\r\n      type: null,\r\n      files: [],\r\n      url: '',\r\n      isActive: false,\r\n      isProcessing: false,\r\n      uploadResult: null\r\n    });\r\n  };\r\n\r\n  const getIconColor = () => {\r\n    if (disabled) return 'text-gray-400';\r\n    \r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return uploadState.isActive ? 'text-purple-600' : 'text-purple-500 hover:text-purple-600';\r\n      case 'Telugu':\r\n        return uploadState.isActive ? 'text-green-600' : 'text-green-500 hover:text-green-600';\r\n      case 'Kannada':\r\n        return uploadState.isActive ? 'text-orange-600' : 'text-orange-500 hover:text-orange-600';\r\n      default:\r\n        return uploadState.isActive ? 'text-blue-600' : 'text-blue-500 hover:text-blue-600';\r\n    }\r\n  };\r\n\r\n  const renderUploadContent = () => {\r\n    if (!uploadState.isActive || !uploadState.type) return null;\r\n\r\n    const isFileType = uploadState.type === 'pdf' || uploadState.type === 'mp3';\r\n    const isURLType = uploadState.type === 'youtube' || uploadState.type === 'article';\r\n\r\n    return (\r\n      <div className=\"mb-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600\">\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className={`p-1 rounded text-xs font-medium text-white ${\r\n              uploadState.type === 'pdf' ? 'bg-red-500' :\r\n              uploadState.type === 'mp3' ? 'bg-purple-500' :\r\n              uploadState.type === 'youtube' ? 'bg-red-600' :\r\n              uploadState.type === 'article' ? 'bg-blue-500' : 'bg-gray-500'\r\n            }`}>\r\n              {uploadState.type === 'pdf' ? 'PDF' :\r\n               uploadState.type === 'mp3' ? 'MP3' :\r\n               uploadState.type === 'youtube' ? 'YT' :\r\n               uploadState.type === 'article' ? 'WEB' : '?'}\r\n            </div>\r\n            <div className=\"flex flex-col\">\r\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                {uploadState.type === 'pdf' && 'PDF/Document Upload'}\r\n                {uploadState.type === 'mp3' && 'MP3 Audio Upload'}\r\n                {uploadState.type === 'youtube' && 'YouTube URL'}\r\n                {uploadState.type === 'article' && 'Article URL'}\r\n              </span>\r\n              <span className={`text-xs flex items-center gap-1 ${\r\n                uploadState.uploadResult?.success ? 'text-green-600 dark:text-green-400' :\r\n                uploadState.uploadResult && !uploadState.uploadResult.success ? 'text-red-600 dark:text-red-400' :\r\n                uploadState.isProcessing ? 'text-blue-600 dark:text-blue-400' :\r\n                'text-gray-500 dark:text-gray-400'\r\n              }`}>\r\n                {uploadState.isProcessing && (\r\n                  <div className=\"w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin\"></div>\r\n                )}\r\n                {uploadState.uploadResult?.success && (\r\n                  <>\r\n                    <PiCheckCircle className=\"w-3 h-3\" />\r\n                    {uploadState.uploadResult.message?.includes('cache') && (\r\n                      <PiDatabase className=\"w-3 h-3 text-blue-500\" title=\"Loaded from cache\" />\r\n                    )}\r\n                  </>\r\n                )}\r\n                {uploadState.uploadResult && !uploadState.uploadResult.success && (\r\n                  <PiWarning className=\"w-3 h-3\" />\r\n                )}\r\n                {getUploadStatusText()}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            {/* Functional pin icon positioned near close button when upload is active */}\r\n            <div className=\"relative\">\r\n              <button\r\n                ref={iconRef}\r\n                type=\"button\"\r\n                onClick={handleAddMoreContent}\r\n                onKeyDown={(e) => {\r\n                  if (e.key === 'Enter' || e.key === ' ') {\r\n                    e.preventDefault();\r\n                    handleAddMoreContent();\r\n                  }\r\n                }}\r\n                disabled={disabled}\r\n                className={`p-1.5 rounded-full transition-all duration-200 transform hover:scale-105 border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 ${getIconColor()} ${\r\n                  disabled ? 'cursor-not-allowed opacity-50 bg-gray-100' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 bg-white dark:bg-gray-700 shadow-sm hover:shadow-md'\r\n                } ${showDropdown ? 'ring-2 ring-offset-1 ' + (\r\n                  selectedLanguage === 'Tamil' ? 'ring-purple-300 focus:ring-purple-500' :\r\n                  selectedLanguage === 'Telugu' ? 'ring-green-300 focus:ring-green-500' :\r\n                  selectedLanguage === 'Kannada' ? 'ring-orange-300 focus:ring-orange-500' : 'ring-blue-300 focus:ring-blue-500'\r\n                ) : (\r\n                  selectedLanguage === 'Tamil' ? 'focus:ring-purple-500' :\r\n                  selectedLanguage === 'Telugu' ? 'focus:ring-green-500' :\r\n                  selectedLanguage === 'Kannada' ? 'focus:ring-orange-500' : 'focus:ring-blue-500'\r\n                )}`}\r\n                title={disabled ? 'Upload disabled' : showDropdown ? 'Close upload options (Press Esc)' : 'Add more files or change upload type (Press Enter)'}\r\n                aria-label={disabled ? 'Upload disabled' : showDropdown ? 'Close upload options' : 'Add more files or change upload type'}\r\n                aria-expanded={showDropdown}\r\n                aria-haspopup=\"menu\"\r\n              >\r\n                <PiPaperclip className={`w-4 h-4 transition-transform duration-200 ${showDropdown ? 'rotate-45' : ''}`} />\r\n              </button>\r\n\r\n              {/* Badge indicator for active upload type */}\r\n              <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full text-xs flex items-center justify-center text-white font-bold ${\r\n                uploadState.type === 'pdf' ? 'bg-red-500' :\r\n                uploadState.type === 'mp3' ? 'bg-purple-500' :\r\n                uploadState.type === 'youtube' ? 'bg-red-600' :\r\n                uploadState.type === 'article' ? 'bg-blue-500' : 'bg-gray-500'\r\n              }`}>\r\n                {uploadState.type === 'pdf' ? 'P' :\r\n                 uploadState.type === 'mp3' ? '♪' :\r\n                 uploadState.type === 'youtube' ? 'Y' :\r\n                 uploadState.type === 'article' ? 'A' : '?'}\r\n              </div>\r\n            </div>\r\n            <button\r\n              onClick={handleClearUpload}\r\n              className=\"p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors rounded-full hover:bg-gray-200 dark:hover:bg-gray-600\"\r\n              title=\"Clear upload\"\r\n            >\r\n              <PiX className=\"w-4 h-4\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {isFileType && (\r\n          <div className={uploadState.isProcessing ? 'opacity-50 pointer-events-none' : ''}>\r\n            <FileDropZone\r\n              acceptedTypes={uploadState.type === 'pdf' ? '.pdf,.doc,.docx,.txt,.rtf' : '.mp3,.wav,.m4a,.flac'}\r\n              onFilesSelected={handleFileUpload}\r\n              maxFiles={1}\r\n              selectedLanguage={selectedLanguage}\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {isURLType && uploadState.type && (uploadState.type === 'youtube' || uploadState.type === 'article') && (\r\n          <div className={uploadState.isProcessing ? 'opacity-50 pointer-events-none' : ''}>\r\n            <URLInput\r\n              type={uploadState.type}\r\n              value={uploadState.url}\r\n              onChange={handleURLChange}\r\n              onSubmit={handleURLSubmit}\r\n              selectedLanguage={selectedLanguage}\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {/* Upload content area */}\r\n      {renderUploadContent()}\r\n\r\n      {/* Upload icon with improved alignment - hide when upload is active */}\r\n      {/* {!uploadState.isActive && (\r\n        <button\r\n          ref={iconRef}\r\n          type=\"button\"\r\n          onClick={() => setShowDropdown(!showDropdown)}\r\n          disabled={disabled}\r\n          className={`p-2.5 rounded-full transition-all duration-200 transform hover:scale-105 ${getIconColor()} ${\r\n            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm hover:shadow-md'\r\n          }`}\r\n          title={disabled ? 'Upload disabled' : 'Upload file or add URL'}\r\n        >\r\n          <PiPaperclip className=\"w-5 h-5\" />\r\n        </button>\r\n      )} */}\r\n\r\n      {/* Dropdown menu - positioned based on upload state */}\r\n      {showDropdown && !disabled && (\r\n        <div\r\n          ref={dropdownRef}\r\n          className={uploadState.isActive ? \"absolute top-full right-0 mt-2 z-50\" : \"\"}\r\n        >\r\n          <UploadDropdown\r\n            onSelect={handleUploadTypeSelect}\r\n            selectedLanguage={selectedLanguage}\r\n          />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatInputUpload;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AALA;;;;;;;;;AA2BA,MAAM,kBAAkD,CAAC,EACvD,gBAAgB,EAChB,YAAY,EACZ,WAAW,EACX,mBAAmB,EACnB,cAAc,EACd,WAAW,KAAK,EACjB;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,MAAM;QACN,OAAO,EAAE;QACT,KAAK;QACL,UAAU;QACV,cAAc;QACd,cAAc;IAChB;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC1C,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAE1D,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC1C,QAAQ,OAAO,IACf,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GACtC;gBACA,gBAAgB;YAClB;QACF;QAEA,SAAS,cAAc,KAAoB;YACzC,IAAI,MAAM,GAAG,KAAK,YAAY,cAAc;gBAC1C,gBAAgB;gBAChB,iCAAiC;gBACjC,QAAQ,OAAO,EAAE;YACnB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,WAAW;QAErC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;KAAa;IAEjB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,aAAa,oBAAoB,OAAO;YAC1C;QACF;IACF,GAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,qBAAqB;YACvB,oBAAoB,YAAY,QAAQ,EAAE;QAC5C;IACF,GAAG;QAAC,YAAY,QAAQ;QAAE;QAAc;KAAoB;IAE5D,MAAM,yBAAyB,CAAC;QAC9B,2DAA2D;QAC3D,MAAM,kBAAkB,YAAY,QAAQ,IAAI,YAAY,IAAI,KAAK;QAErE,eAAe;YACb;YACA,OAAO,kBAAkB,EAAE,GAAG,YAAY,KAAK;YAC/C,KAAK,kBAAkB,KAAK,YAAY,GAAG;YAC3C,UAAU;YACV,cAAc;YACd,cAAc;QAChB;QACA,gBAAgB;QAEhB,6CAA6C;QAC7C,IAAI,iBAAiB;YACnB,IAAI,cAAc;gBAChB,aAAa,EAAE;YACjB;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,gEAAgE;QAChE,gBAAgB,CAAC;IACnB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,YAAY,QAAQ,EAAE,OAAO;QAElC,yBAAyB;QACzB,IAAI,YAAY,YAAY,EAAE;YAC5B,OAAO;QACT;QAEA,4BAA4B;QAC5B,IAAI,YAAY,YAAY,EAAE;YAC5B,IAAI,YAAY,YAAY,CAAC,OAAO,EAAE;gBACpC,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;QAEA,MAAM,YAAY,YAAY,KAAK,CAAC,MAAM;QAC1C,MAAM,SAAS,YAAY,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG;QAE/C,IAAI,YAAY,IAAI,KAAK,SAAS,YAAY,IAAI,KAAK,OAAO;YAC5D,OAAO,YAAY,IAAI,GAAG,UAAU,KAAK,EAAE,YAAY,IAAI,MAAM,GAAG,SAAS,CAAC,GAAG;QACnF,OAAO;YACL,OAAO,SAAS,cAAc;QAChC;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,YAAY,IAAI,IAAK,YAAY,IAAI,KAAK,SAAS,YAAY,IAAI,KAAK,OAAQ;YACpG;QACF;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,gDAAgD;QAChD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;QACtC,MAAM,WAAW,KAAK,IAAI;QAC1B,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;QAEzC,kBAAkB;QAClB,IAAI,WAAW,SAAS;YACtB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,0CAA0C;QAC1C,IAAI,YAAY,IAAI,KAAK,OAAO;YAC9B,MAAM,QAAQ,SAAS,QAAQ,CAAC;YAChC,MAAM,aAAa,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,YAChD,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC;YAEjE,IAAI,CAAC,SAAS,CAAC,YAAY;gBACzB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF,OAAO,IAAI,YAAY,IAAI,KAAK,OAAO;YACrC,MAAM,UAAU,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,WAChD,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC;YAE9D,IAAI,CAAC,SAAS;gBACZ,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF;QAEA,gDAAgD;QAChD,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP;gBACA,cAAc;gBACd,cAAc;YAChB,CAAC;QAED,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,YAAY,IAAI,CAAC,iBAAiB,CAAC,EAAE,KAAK,IAAI;YAEzE,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,YAAY,IAAI,EAAE;YACpD,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,2BAA2B;YAC3B,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,cAAc;oBACd,cAAc;gBAChB,CAAC;YAED,uDAAuD;YACvD,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,WAAW,YAAY,IAAI,KAAK,QAAQ,iBAAiB;gBAC/D,MAAM,eAAe,OAAO,OAAO,EAAE,SAAS,WAAW,kBAAkB;gBAC3E,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,SAAS,4BAA4B,EAAE,aAAa,2BAA2B,CAAC;gBAEjG,yCAAyC;gBACzC,IAAI,cAAc;oBAChB,aAAa;gBACf;gBAEA,iFAAiF;gBACjF,oBAAoB,OAAO,GAAG,WAAW;oBACvC;gBACF,GAAG,OAAO,uCAAuC;YACnD,OAAO;gBACL,MAAM,eAAe,OAAO,KAAK,IAAI;gBACrC,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBAEZ,4DAA4D;gBAC5D,IAAI,aAAa,QAAQ,CAAC,oBAAoB,aAAa,QAAQ,CAAC,uBAAuB;oBACzF,IAAI,gBAAgB;wBAClB;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,cAAc;oBACd,cAAc;wBAAE,SAAS;wBAAO,OAAO;oBAAqC;gBAC9E,CAAC;YACD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAEZ,0CAA0C;YAC1C,IAAI,gBAAgB;gBAClB;YACF;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP;YACF,CAAC;IACH;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,YAAY,IAAI,IAAK,YAAY,IAAI,KAAK,aAAa,YAAY,IAAI,KAAK,WAAY;YAC3F;QACF;QAEA,uBAAuB;QACvB,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;gBAAM,cAAc;YAAK,CAAC;QAE3E,IAAI;YACF,2CAA2C;YAC3C,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,YAAY,IAAI,EAAE;YAEpD,2BAA2B;YAC3B,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,cAAc;oBACd,cAAc;gBAChB,CAAC;YAED,2BAA2B;YAC3B,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,eAAe,OAAO,OAAO,EAAE,SAAS,WAAW,kBAAkB;gBAC3E,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,YAAY,IAAI,KAAK,YAAY,kBAAkB,UAAU,uBAAuB,EAAE,aAAa,CAAC,CAAC;gBACtH,yCAAyC;gBACzC,IAAI,aAAa;oBACf,YAAY,KAAK,YAAY,IAAI;gBACnC;gBAEA,iFAAiF;gBACjF,oBAAoB,OAAO,GAAG,WAAW;oBACvC;gBACF,GAAG,OAAO,uCAAuC;YACnD,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;gBAE5B,4DAA4D;gBAC5D,IAAI,OAAO,KAAK,EAAE,SAAS,oBAAoB,OAAO,KAAK,EAAE,SAAS,uBAAuB;oBAC3F,IAAI,gBAAgB;wBAClB;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,cAAc;oBACd,cAAc;wBAAE,SAAS;wBAAO,OAAO;oBAAgB;gBACzD,CAAC;YACD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAEZ,4DAA4D;YAC5D,IAAI,gBAAgB;gBAClB;YACF;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,uCAAuC;QACvC,IAAI,oBAAoB,OAAO,EAAE;YAC/B,aAAa,oBAAoB,OAAO;YACxC,oBAAoB,OAAO,GAAG;QAChC;QAEA,eAAe;YACb,MAAM;YACN,OAAO,EAAE;YACT,KAAK;YACL,UAAU;YACV,cAAc;YACd,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,UAAU,OAAO;QAErB,OAAQ;YACN,KAAK;gBACH,OAAO,YAAY,QAAQ,GAAG,oBAAoB;YACpD,KAAK;gBACH,OAAO,YAAY,QAAQ,GAAG,mBAAmB;YACnD,KAAK;gBACH,OAAO,YAAY,QAAQ,GAAG,oBAAoB;YACpD;gBACE,OAAO,YAAY,QAAQ,GAAG,kBAAkB;QACpD;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,YAAY,QAAQ,IAAI,CAAC,YAAY,IAAI,EAAE,OAAO;QAEvD,MAAM,aAAa,YAAY,IAAI,KAAK,SAAS,YAAY,IAAI,KAAK;QACtE,MAAM,YAAY,YAAY,IAAI,KAAK,aAAa,YAAY,IAAI,KAAK;QAEzE,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAC,2CAA2C,EAC1D,YAAY,IAAI,KAAK,QAAQ,eAC7B,YAAY,IAAI,KAAK,QAAQ,kBAC7B,YAAY,IAAI,KAAK,YAAY,eACjC,YAAY,IAAI,KAAK,YAAY,gBAAgB,eACjD;8CACC,YAAY,IAAI,KAAK,QAAQ,QAC7B,YAAY,IAAI,KAAK,QAAQ,QAC7B,YAAY,IAAI,KAAK,YAAY,OACjC,YAAY,IAAI,KAAK,YAAY,QAAQ;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDACb,YAAY,IAAI,KAAK,SAAS;gDAC9B,YAAY,IAAI,KAAK,SAAS;gDAC9B,YAAY,IAAI,KAAK,aAAa;gDAClC,YAAY,IAAI,KAAK,aAAa;;;;;;;sDAErC,8OAAC;4CAAK,WAAW,CAAC,gCAAgC,EAChD,YAAY,YAAY,EAAE,UAAU,uCACpC,YAAY,YAAY,IAAI,CAAC,YAAY,YAAY,CAAC,OAAO,GAAG,mCAChE,YAAY,YAAY,GAAG,qCAC3B,oCACA;;gDACC,YAAY,YAAY,kBACvB,8OAAC;oDAAI,WAAU;;;;;;gDAEhB,YAAY,YAAY,EAAE,yBACzB;;sEACE,8OAAC,8IAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDACxB,YAAY,YAAY,CAAC,OAAO,EAAE,SAAS,0BAC1C,8OAAC,8IAAA,CAAA,aAAU;4DAAC,WAAU;4DAAwB,OAAM;;;;;;;;gDAIzD,YAAY,YAAY,IAAI,CAAC,YAAY,YAAY,CAAC,OAAO,kBAC5D,8OAAC,8IAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAEtB;;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK;4CACL,MAAK;4CACL,SAAS;4CACT,WAAW,CAAC;gDACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;oDACtC,EAAE,cAAc;oDAChB;gDACF;4CACF;4CACA,UAAU;4CACV,WAAW,CAAC,yKAAyK,EAAE,eAAe,CAAC,EACrM,WAAW,8CAA8C,8GAC1D,CAAC,EAAE,eAAe,0BAA0B,CAC3C,qBAAqB,UAAU,0CAC/B,qBAAqB,WAAW,wCAChC,qBAAqB,YAAY,0CAA0C,mCAC7E,IACE,qBAAqB,UAAU,0BAC/B,qBAAqB,WAAW,yBAChC,qBAAqB,YAAY,0BAA0B,uBAC1D;4CACH,OAAO,WAAW,oBAAoB,eAAe,qCAAqC;4CAC1F,cAAY,WAAW,oBAAoB,eAAe,yBAAyB;4CACnF,iBAAe;4CACf,iBAAc;sDAEd,cAAA,8OAAC,8IAAA,CAAA,cAAW;gDAAC,WAAW,CAAC,0CAA0C,EAAE,eAAe,cAAc,IAAI;;;;;;;;;;;sDAIxG,8OAAC;4CAAI,WAAW,CAAC,4GAA4G,EAC3H,YAAY,IAAI,KAAK,QAAQ,eAC7B,YAAY,IAAI,KAAK,QAAQ,kBAC7B,YAAY,IAAI,KAAK,YAAY,eACjC,YAAY,IAAI,KAAK,YAAY,gBAAgB,eACjD;sDACC,YAAY,IAAI,KAAK,QAAQ,MAC7B,YAAY,IAAI,KAAK,QAAQ,MAC7B,YAAY,IAAI,KAAK,YAAY,MACjC,YAAY,IAAI,KAAK,YAAY,MAAM;;;;;;;;;;;;8CAG5C,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,8IAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAKpB,4BACC,8OAAC;oBAAI,WAAW,YAAY,YAAY,GAAG,mCAAmC;8BAC5E,cAAA,8OAAC,6IAAA,CAAA,UAAY;wBACX,eAAe,YAAY,IAAI,KAAK,QAAQ,8BAA8B;wBAC1E,iBAAiB;wBACjB,UAAU;wBACV,kBAAkB;;;;;;;;;;;gBAKvB,aAAa,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI,KAAK,aAAa,YAAY,IAAI,KAAK,SAAS,mBACjG,8OAAC;oBAAI,WAAW,YAAY,YAAY,GAAG,mCAAmC;8BAC5E,cAAA,8OAAC,yIAAA,CAAA,UAAQ;wBACP,MAAM,YAAY,IAAI;wBACtB,OAAO,YAAY,GAAG;wBACtB,UAAU;wBACV,UAAU;wBACV,kBAAkB;;;;;;;;;;;;;;;;;IAM9B;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ;YAmBA,gBAAgB,CAAC,0BAChB,8OAAC;gBACC,KAAK;gBACL,WAAW,YAAY,QAAQ,GAAG,wCAAwC;0BAE1E,cAAA,8OAAC,+IAAA,CAAA,UAAc;oBACb,UAAU;oBACV,kBAAkB;;;;;;;;;;;;;;;;;AAM9B;uCAEe"}}, {"offset": {"line": 2544, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2550, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/chatComponents/services/ApiService.ts"], "sourcesContent": ["import { CacheService } from './CacheService';\r\n\r\n// API configuration\r\nexport const API_CONFIG = {\r\n  // Production endpoint (previously used)\r\n  PROD_ENDPOINT: \"http://localhost:5010/financial_query\",\r\n  // Development endpoint for suggest.py (running locally)\r\n  DEV_ENDPOINT: \"http://localhost:5010/financial_query\",\r\n  // Use DEV_ENDPOINT for local development, PROD_ENDPOINT for production\r\n  ACTIVE_ENDPOINT: \"http://localhost:5010/financial_query\",\r\n  // FAISS collection endpoint (keeping PINE for backward compatibility)\r\n  PINE_COLLECTION_ENDPOINT: \"https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS\"\r\n};\r\n\r\nexport interface ApiRequestBody {\r\n  query: string;\r\n  client_email?: string;\r\n  index_name?: string;\r\n  embed_model?: string;\r\n  upload_context?: string;\r\n  has_recent_uploads?: boolean;\r\n  target_language?: string;\r\n  enable_translation?: boolean;\r\n}\r\n\r\nexport interface ApiResponse {\r\n  ai_response: string;\r\n  related_questions?: string[];\r\n  pinecone_indexes?: string[];\r\n  sentence_analysis?: Array<{ sentence: string; url: string; summary?: string }>;\r\n  faiss_categories?: any[];\r\n  has_uploaded_content?: boolean;\r\n  upload_sources?: any[];\r\n  error?: string;\r\n  translation_applied?: boolean;\r\n  query_language?: string;\r\n  target_language?: string;\r\n  translation_metadata?: any;\r\n  source_language?: string;\r\n  translation_timestamp?: string;\r\n}\r\n\r\nexport class ApiService {\r\n\r\n  // Function to fetch PINE collection data for the current user\r\n  // Falls back to default configuration (default index) when:\r\n  // - No user email is found\r\n  // - API requests fail\r\n  // - No PINE data exists for user\r\n  // - Any errors occur\r\n  static async fetchPineCollection() {\r\n    try {\r\n      const userEmail = localStorage.getItem(\"user_email\");\r\n      console.log(\"local mail\", userEmail);\r\n      if (!userEmail) {\r\n        console.warn(\"No user email found in localStorage - using default configuration\");\r\n        // Set default configuration\r\n        const defaultConfig = {\r\n          api_key: \"pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua\",\r\n          index_name: \"default\",\r\n          embed_model: \"all-MiniLM-L6-v2\"\r\n        };\r\n        localStorage.setItem(\"faiss_index_name\", defaultConfig.index_name);\r\n        if (defaultConfig.embed_model) {\r\n          localStorage.setItem(\"faiss_embed_model\", defaultConfig.embed_model);\r\n        }\r\n        return defaultConfig;\r\n      }\r\n\r\n      // Use filtered API endpoint to get ALL entries for this user (remove filtercount=1 to get all matches)\r\n      const filterUrl = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(userEmail.trim())}`;\r\n\r\n      const response = await fetch(filterUrl, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'xxxid': 'FAISS'\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        console.warn(`Failed to fetch PINE collection: ${response.status} - using default configuration`);\r\n        // Set default FAISS configuration\r\n        const defaultConfig = {\r\n          index_name: \"default\",\r\n          embed_model: \"all-MiniLM-L6-v2\"\r\n        };\r\n        localStorage.setItem(\"faiss_index_name\", defaultConfig.index_name);\r\n        localStorage.setItem(\"faiss_embed_model\", defaultConfig.embed_model);\r\n        return defaultConfig;\r\n      }\r\n\r\n      const data = await response.json();\r\n      console.log(\"FAISS collection response:\", data);\r\n\r\n      // Check if user exists in FAISS collection and extract all their indexes\r\n      if (data.statusCode === 200 && data.source && data.source.length > 0) {\r\n        // Parse each item in the source array (they are JSON strings)\r\n        const faissData = data.source.map((item: string) => JSON.parse(item));\r\n\r\n        // Extract all indexes and embedding models for this user\r\n        const userIndexes: string[] = [];\r\n        const userEmbedModels: string[] = [];\r\n        let firstUserEntry: any | null = null;\r\n\r\n        faissData.forEach((item: any) => {\r\n          if (item.client && item.client.trim().toLowerCase() === userEmail.trim().toLowerCase()) {\r\n            if (!firstUserEntry) firstUserEntry = item; // Keep first entry for return\r\n            if (item.index_name && !userIndexes.includes(item.index_name)) {\r\n              userIndexes.push(item.index_name);\r\n            }\r\n            if (item.embed_model && !userEmbedModels.includes(item.embed_model)) {\r\n              userEmbedModels.push(item.embed_model);\r\n            }\r\n          }\r\n        });\r\n\r\n        if (userIndexes.length > 0) {\r\n          // User exists in FAISS collection - store all their configurations\r\n          localStorage.setItem(\"faiss_index_name\", userIndexes[0]); // Store first index as default\r\n          localStorage.setItem(\"faiss_embed_model\", userEmbedModels[0] || \"all-MiniLM-L6-v2\"); // Store first embed model as default\r\n          localStorage.setItem('userFaissIndexes', JSON.stringify(userIndexes)); // Store all indexes\r\n          localStorage.setItem('userEmbedModels', JSON.stringify(userEmbedModels)); // Store all embed models\r\n          console.log(\"Found existing FAISS data for user:\", userEmail);\r\n          console.log(\"User indexes:\", userIndexes);\r\n          console.log(\"User embed models:\", userEmbedModels);\r\n          return firstUserEntry;\r\n        } else {\r\n          // User doesn't exist in FAISS collection - use default values without auto-creation\r\n          console.log(\"No FAISS data found for user:\", userEmail, \"- using default configuration without auto-creation\");\r\n\r\n          const defaultConfig = {\r\n            index_name: \"default\",\r\n            embed_model: \"all-MiniLM-L6-v2\"\r\n          };\r\n          localStorage.setItem(\"faiss_index_name\", defaultConfig.index_name);\r\n          localStorage.setItem(\"faiss_embed_model\", defaultConfig.embed_model);\r\n          localStorage.setItem(\"faiss_client_email\", userEmail);\r\n          console.log(\"Using default FAISS configuration for user:\", userEmail);\r\n          return defaultConfig;\r\n        }\r\n      } else {\r\n        // No FAISS data found - use default values without auto-creation\r\n        console.log(\"No FAISS data found for user:\", userEmail, \"- using default configuration without auto-creation\");\r\n\r\n        const defaultConfig = {\r\n          index_name: \"default\",\r\n          embed_model: \"all-MiniLM-L6-v2\"\r\n        };\r\n        localStorage.setItem(\"faiss_index_name\", defaultConfig.index_name);\r\n        localStorage.setItem(\"faiss_embed_model\", defaultConfig.embed_model);\r\n        localStorage.setItem(\"faiss_client_email\", userEmail);\r\n        console.log(\"Using default FAISS configuration for user:\", userEmail);\r\n        return defaultConfig;\r\n      }\r\n    } catch (error) {\r\n      console.warn(\"Error fetching FAISS collection - using default configuration:\", error);\r\n      // Fallback to default values on error\r\n      const defaultConfig = {\r\n        index_name: \"default\",\r\n        embed_model: \"all-MiniLM-L6-v2\"\r\n      };\r\n      localStorage.setItem(\"faiss_index_name\", defaultConfig.index_name);\r\n      localStorage.setItem(\"faiss_embed_model\", defaultConfig.embed_model);\r\n      return defaultConfig;\r\n    }\r\n  }\r\n\r\n  // Function to get current user's email from session storage\r\n  private static getCurrentUserEmail(): string | null {\r\n    try {\r\n      if (typeof window === 'undefined') return null;\r\n\r\n      // Try multiple sources for user email\r\n      const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');\r\n      if (directEmail) return directEmail;\r\n\r\n      // Try from user session data\r\n      const userSession = sessionStorage.getItem('resultUser');\r\n      if (userSession) {\r\n        const userData = JSON.parse(userSession);\r\n        return userData.email || userData.username || null;\r\n      }\r\n\r\n      return null;\r\n    } catch (error) {\r\n      console.error('Error getting current user email:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Function to fetch user-specific FAISS indexes from the backend\r\n  static async fetchUserIndexes(): Promise<string[]> {\r\n    try {\r\n      console.log(\"Fetching user-specific FAISS indexes...\");\r\n\r\n      // Get current user's email\r\n      const userEmail = this.getCurrentUserEmail();\r\n\r\n      if (!userEmail) {\r\n        console.warn(\"⚠️ No user email found, fetching all available indexes\");\r\n        // Fall back to GET request for all indexes if no user email\r\n        const response = await fetch('http://localhost:5010/api/list-faiss-indexes', {\r\n          method: 'GET',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          }\r\n        });\r\n\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          if (data.success && data.indexes && data.indexes.length > 0) {\r\n            console.log(\"✅ Retrieved all FAISS indexes:\", data.indexes);\r\n            return data.indexes;\r\n          }\r\n        }\r\n\r\n        console.warn(\"⚠️ Falling back to default index\");\r\n        return [\"default\"];\r\n      }\r\n\r\n      console.log(`🔍 Fetching indexes for user: ${userEmail}`);\r\n\r\n      // Call the list-faiss-indexes endpoint with user email for filtering\r\n      const response = await fetch('http://localhost:5010/api/list-faiss-indexes', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          email: userEmail\r\n        })\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        if (data.success && data.indexes && data.indexes.length > 0) {\r\n          console.log(`✅ Retrieved ${data.indexes.length} FAISS indexes for user ${userEmail}:`, data.indexes);\r\n          return data.indexes;\r\n        } else {\r\n          console.warn(`⚠️ No FAISS indexes found for user ${userEmail}:`, data.error || \"Unknown error\");\r\n          // Return empty array for users with no indexes instead of default\r\n          return [];\r\n        }\r\n      } else {\r\n        console.warn(\"⚠️ FAISS indexes API failed:\", response.status);\r\n      }\r\n\r\n      // Fallback to empty array if user-specific request fails\r\n      console.warn(\"⚠️ No indexes available for user\");\r\n      return [];\r\n    } catch (error) {\r\n      console.error(\"Error fetching user-specific FAISS indexes:\", error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // Function to send query to API\r\n  static async sendQuery(requestBody: ApiRequestBody): Promise<ApiResponse> {\r\n    const response = await fetch(API_CONFIG.ACTIVE_ENDPOINT, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(requestBody),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      // Check if the error is related to the index\r\n      if (response.status === 404) {\r\n        const errorData = await response.json();\r\n        if (errorData.error && errorData.error.includes(\"No matching documents found or index not available\")) {\r\n          throw new Error(`The selected index is not available or contains no relevant data. Please try another index.`);\r\n        }\r\n      }\r\n      throw new Error(`API response error: ${response.status}`);\r\n    }\r\n\r\n    return await response.json();\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAGO,MAAM,aAAa;IACxB,wCAAwC;IACxC,eAAe;IACf,wDAAwD;IACxD,cAAc;IACd,uEAAuE;IACvE,iBAAiB;IACjB,sEAAsE;IACtE,0BAA0B;AAC5B;AA8BO,MAAM;IAEX,8DAA8D;IAC9D,4DAA4D;IAC5D,2BAA2B;IAC3B,sBAAsB;IACtB,iCAAiC;IACjC,qBAAqB;IACrB,aAAa,sBAAsB;QACjC,IAAI;YACF,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,QAAQ,GAAG,CAAC,cAAc;YAC1B,IAAI,CAAC,WAAW;gBACd,QAAQ,IAAI,CAAC;gBACb,4BAA4B;gBAC5B,MAAM,gBAAgB;oBACpB,SAAS;oBACT,YAAY;oBACZ,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,oBAAoB,cAAc,UAAU;gBACjE,IAAI,cAAc,WAAW,EAAE;oBAC7B,aAAa,OAAO,CAAC,qBAAqB,cAAc,WAAW;gBACrE;gBACA,OAAO;YACT;YAEA,uGAAuG;YACvG,MAAM,YAAY,CAAC,6GAA6G,EAAE,mBAAmB,UAAU,IAAI,KAAK;YAExK,MAAM,WAAW,MAAM,MAAM,WAAW;gBACtC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,SAAS;gBACX;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,IAAI,CAAC,CAAC,iCAAiC,EAAE,SAAS,MAAM,CAAC,8BAA8B,CAAC;gBAChG,kCAAkC;gBAClC,MAAM,gBAAgB;oBACpB,YAAY;oBACZ,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,oBAAoB,cAAc,UAAU;gBACjE,aAAa,OAAO,CAAC,qBAAqB,cAAc,WAAW;gBACnE,OAAO;YACT;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,yEAAyE;YACzE,IAAI,KAAK,UAAU,KAAK,OAAO,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;gBACpE,8DAA8D;gBAC9D,MAAM,YAAY,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAiB,KAAK,KAAK,CAAC;gBAE/D,yDAAyD;gBACzD,MAAM,cAAwB,EAAE;gBAChC,MAAM,kBAA4B,EAAE;gBACpC,IAAI,iBAA6B;gBAEjC,UAAU,OAAO,CAAC,CAAC;oBACjB,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,GAAG,WAAW,OAAO,UAAU,IAAI,GAAG,WAAW,IAAI;wBACtF,IAAI,CAAC,gBAAgB,iBAAiB,MAAM,8BAA8B;wBAC1E,IAAI,KAAK,UAAU,IAAI,CAAC,YAAY,QAAQ,CAAC,KAAK,UAAU,GAAG;4BAC7D,YAAY,IAAI,CAAC,KAAK,UAAU;wBAClC;wBACA,IAAI,KAAK,WAAW,IAAI,CAAC,gBAAgB,QAAQ,CAAC,KAAK,WAAW,GAAG;4BACnE,gBAAgB,IAAI,CAAC,KAAK,WAAW;wBACvC;oBACF;gBACF;gBAEA,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,mEAAmE;oBACnE,aAAa,OAAO,CAAC,oBAAoB,WAAW,CAAC,EAAE,GAAG,+BAA+B;oBACzF,aAAa,OAAO,CAAC,qBAAqB,eAAe,CAAC,EAAE,IAAI,qBAAqB,qCAAqC;oBAC1H,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC,eAAe,oBAAoB;oBAC3F,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC,mBAAmB,yBAAyB;oBACnG,QAAQ,GAAG,CAAC,uCAAuC;oBACnD,QAAQ,GAAG,CAAC,iBAAiB;oBAC7B,QAAQ,GAAG,CAAC,sBAAsB;oBAClC,OAAO;gBACT,OAAO;oBACL,oFAAoF;oBACpF,QAAQ,GAAG,CAAC,iCAAiC,WAAW;oBAExD,MAAM,gBAAgB;wBACpB,YAAY;wBACZ,aAAa;oBACf;oBACA,aAAa,OAAO,CAAC,oBAAoB,cAAc,UAAU;oBACjE,aAAa,OAAO,CAAC,qBAAqB,cAAc,WAAW;oBACnE,aAAa,OAAO,CAAC,sBAAsB;oBAC3C,QAAQ,GAAG,CAAC,+CAA+C;oBAC3D,OAAO;gBACT;YACF,OAAO;gBACL,iEAAiE;gBACjE,QAAQ,GAAG,CAAC,iCAAiC,WAAW;gBAExD,MAAM,gBAAgB;oBACpB,YAAY;oBACZ,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,oBAAoB,cAAc,UAAU;gBACjE,aAAa,OAAO,CAAC,qBAAqB,cAAc,WAAW;gBACnE,aAAa,OAAO,CAAC,sBAAsB;gBAC3C,QAAQ,GAAG,CAAC,+CAA+C;gBAC3D,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,kEAAkE;YAC/E,sCAAsC;YACtC,MAAM,gBAAgB;gBACpB,YAAY;gBACZ,aAAa;YACf;YACA,aAAa,OAAO,CAAC,oBAAoB,cAAc,UAAU;YACjE,aAAa,OAAO,CAAC,qBAAqB,cAAc,WAAW;YACnE,OAAO;QACT;IACF;IAEA,4DAA4D;IAC5D,OAAe,sBAAqC;QAClD,IAAI;YACF,wCAAmC,OAAO;;YAE1C,sCAAsC;YACtC,MAAM;YAGN,6BAA6B;YAC7B,MAAM;QAOR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;QACT;IACF;IAEA,iEAAiE;IACjE,aAAa,mBAAsC;QACjD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,2BAA2B;YAC3B,MAAM,YAAY,IAAI,CAAC,mBAAmB;YAE1C,IAAI,CAAC,WAAW;gBACd,QAAQ,IAAI,CAAC;gBACb,4DAA4D;gBAC5D,MAAM,WAAW,MAAM,MAAM,gDAAgD;oBAC3E,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,GAAG;wBAC3D,QAAQ,GAAG,CAAC,kCAAkC,KAAK,OAAO;wBAC1D,OAAO,KAAK,OAAO;oBACrB;gBACF;gBAEA,QAAQ,IAAI,CAAC;gBACb,OAAO;oBAAC;iBAAU;YACpB;YAEA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,WAAW;YAExD,qEAAqE;YACrE,MAAM,WAAW,MAAM,MAAM,gDAAgD;gBAC3E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;gBACT;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,GAAG;oBAC3D,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,wBAAwB,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,OAAO;oBACnG,OAAO,KAAK,OAAO;gBACrB,OAAO;oBACL,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI;oBAC/E,kEAAkE;oBAClE,OAAO,EAAE;gBACX;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC,gCAAgC,SAAS,MAAM;YAC9D;YAEA,yDAAyD;YACzD,QAAQ,IAAI,CAAC;YACb,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,OAAO,EAAE;QACX;IACF;IAEA,gCAAgC;IAChC,aAAa,UAAU,WAA2B,EAAwB;QACxE,MAAM,WAAW,MAAM,MAAM,WAAW,eAAe,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,6CAA6C;YAC7C,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,IAAI,UAAU,KAAK,IAAI,UAAU,KAAK,CAAC,QAAQ,CAAC,uDAAuD;oBACrG,MAAM,IAAI,MAAM,CAAC,2FAA2F,CAAC;gBAC/G;YACF;YACA,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B;AACF"}}, {"offset": {"line": 2773, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2779, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/chatComponents/services/TranslationService.ts"], "sourcesContent": ["export class TranslationService {\r\n  private static translationCache = new Map<string, string>();\r\n\r\n  // Language detection helper\r\n  static detectLanguage(text: string): string {\r\n    if (!text || !text.trim()) return 'en';\r\n\r\n    // Tamil detection using Unicode ranges\r\n    if (/[\\u0B80-\\u0BFF]/.test(text)) return 'ta';\r\n\r\n    // Telugu detection using Unicode ranges\r\n    if (/[\\u0C00-\\u0C7F]/.test(text)) return 'te';\r\n\r\n    // Kannada detection using Unicode ranges\r\n    if (/[\\u0C80-\\u0CFF]/.test(text)) return 'kn';\r\n\r\n    // Hindi detection using Unicode ranges\r\n    if (/[\\u0900-\\u097F]/.test(text)) return 'hi';\r\n\r\n    // Arabic detection\r\n    if (/[\\u0600-\\u06FF]/.test(text)) return 'ar';\r\n\r\n    // Chinese detection\r\n    if (/[\\u4e00-\\u9fff]/.test(text)) return 'zh';\r\n\r\n    // Default to English\r\n    return 'en';\r\n  }\r\n\r\n  // Fast translation function with timeout for better performance\r\n  static async translateText(text: string, sourceLang: string, targetLang: string): Promise<string> {\r\n    console.log(`🚀 Fast translating from ${sourceLang} to ${targetLang}: ${text.substring(0, 50)}${text.length > 50 ? '...' : ''}`);\r\n\r\n    // If source and target languages are the same, return original text immediately\r\n    if (sourceLang === targetLang) {\r\n      console.log(\"⚠️ Source and target languages are the same, returning original text\");\r\n      return text;\r\n    }\r\n\r\n    // Check cache first for instant response\r\n    const cacheKey = `${sourceLang}-${targetLang}-${text}`;\r\n    const cachedTranslation = this.translationCache.get(cacheKey);\r\n    if (cachedTranslation) {\r\n      console.log(\"💾 Using cached translation (instant)\");\r\n      return cachedTranslation;\r\n    }\r\n\r\n    try {\r\n      console.log(\"🚀 Using fast direct translation service with timeout\");\r\n\r\n      // Create a timeout promise for faster responses\r\n      const timeoutPromise = new Promise<string>((_, reject) => {\r\n        setTimeout(() => reject(new Error('Translation timeout')), 5000); // 5 second timeout\r\n      });\r\n\r\n      // Try direct MyMemory API first (faster than backend)\r\n      const translationPromise = this.translateWithMyMemory(text, sourceLang, targetLang);\r\n\r\n      const directTranslation = await Promise.race([translationPromise, timeoutPromise]);\r\n      if (directTranslation && directTranslation !== text) {\r\n        console.log(`✅ Direct translation successful: ${directTranslation.substring(0, 50)}${directTranslation.length > 50 ? '...' : ''}`);\r\n        this.translationCache.set(cacheKey, directTranslation);\r\n        return directTranslation;\r\n      }\r\n\r\n      // If direct translation fails, try backend as fallback\r\n      console.log(\"🔄 Direct translation failed, trying backend service\");\r\n      const response = await fetch('http://localhost:5010/api/translate', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          text: text,\r\n          source_lang: sourceLang,\r\n          target_lang: targetLang\r\n        })\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        if (data.success && data.data && data.data.translated_text) {\r\n          const translatedText = data.data.translated_text;\r\n          console.log(`✅ Backend translation successful: ${translatedText.substring(0, 50)}${translatedText.length > 50 ? '...' : ''}`);\r\n          this.translationCache.set(cacheKey, translatedText);\r\n          return translatedText;\r\n        }\r\n      }\r\n\r\n      // If both fail, try fallback patterns\r\n      console.log(\"🔄 All translation services failed, trying fallback patterns\");\r\n      const fallbackTranslation = this.getFallbackTranslation(text, sourceLang, targetLang);\r\n\r\n      if (fallbackTranslation !== text) {\r\n        console.log(`✅ Fallback translation successful: ${fallbackTranslation.substring(0, 50)}${fallbackTranslation.length > 50 ? '...' : ''}`);\r\n        this.translationCache.set(cacheKey, fallbackTranslation);\r\n        return fallbackTranslation;\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error(\"❌ Translation error:\", error);\r\n    }\r\n    \r\n    // Final fallback - return original text\r\n    console.log(\"🔄 All translation methods failed, returning original text\");\r\n    this.translationCache.set(cacheKey, text);\r\n    return text;\r\n  }\r\n\r\n  // Fallback translation using pattern matching\r\n  static getFallbackTranslation(text: string, sourceLang: string, targetLang: string): string {\r\n    // Common translation patterns for basic phrases\r\n    const translationPatterns: Record<string, Record<string, string>> = {\r\n      'en-ta': {\r\n        'Hello': 'வணக்கம்',\r\n        'Thank you': 'நன்றி',\r\n        'Yes': 'ஆம்',\r\n        'No': 'இல்லை',\r\n        'Please': 'தயவுசெய்து',\r\n        'Sorry': 'மன்னிக்கவும்',\r\n        'Good morning': 'காலை வணக்கம்',\r\n        'Good evening': 'மாலை வணக்கம்',\r\n        'How are you?': 'நீங்கள் எப்படி இருக்கிறீர்கள்?',\r\n        'What is your name?': 'உங்கள் பெயர் என்ன?'\r\n      },\r\n      'en-te': {\r\n        'Hello': 'హలో',\r\n        'Thank you': 'ధన్యవాదాలు',\r\n        'Yes': 'అవును',\r\n        'No': 'లేదు',\r\n        'Please': 'దయచేసి',\r\n        'Sorry': 'క్షమించండి',\r\n        'Good morning': 'శుభోదయం',\r\n        'Good evening': 'శుభ సాయంత్రం',\r\n        'How are you?': 'మీరు ఎలా ఉన్నారు?',\r\n        'What is your name?': 'మీ పేరు ఏమిటి?'\r\n      },\r\n      'en-kn': {\r\n        'Hello': 'ಹಲೋ',\r\n        'Thank you': 'ಧನ್ಯವಾದಗಳು',\r\n        'Yes': 'ಹೌದು',\r\n        'No': 'ಇಲ್ಲ',\r\n        'Please': 'ದಯವಿಟ್ಟು',\r\n        'Sorry': 'ಕ್ಷಮಿಸಿ',\r\n        'Good morning': 'ಶುಭೋದಯ',\r\n        'Good evening': 'ಶುಭ ಸಂಜೆ',\r\n        'How are you?': 'ನೀವು ಹೇಗಿದ್ದೀರಿ?',\r\n        'What is your name?': 'ನಿಮ್ಮ ಹೆಸರು ಏನು?'\r\n      },\r\n      'ta-en': {\r\n        'வணக்கம்': 'Hello',\r\n        'நன்றி': 'Thank you',\r\n        'ஆம்': 'Yes',\r\n        'இல்லை': 'No',\r\n        'தயவுசெய்து': 'Please',\r\n        'மன்னிக்கவும்': 'Sorry',\r\n        'காலை வணக்கம்': 'Good morning',\r\n        'மாலை வணக்கம்': 'Good evening'\r\n      },\r\n      'te-en': {\r\n        'హలో': 'Hello',\r\n        'ధన్యవాదాలు': 'Thank you',\r\n        'అవును': 'Yes',\r\n        'లేదు': 'No',\r\n        'దయచేసి': 'Please',\r\n        'క్షమించండి': 'Sorry',\r\n        'శుభోదయం': 'Good morning',\r\n        'శుభ సాయంత్రం': 'Good evening'\r\n      },\r\n      'kn-en': {\r\n        'ಹಲೋ': 'Hello',\r\n        'ಧನ್ಯವಾದಗಳು': 'Thank you',\r\n        'ಹೌದು': 'Yes',\r\n        'ಇಲ್ಲ': 'No',\r\n        'ದಯವಿಟ್ಟು': 'Please',\r\n        'ಕ್ಷಮಿಸಿ': 'Sorry',\r\n        'ಶುಭೋದಯ': 'Good morning',\r\n        'ಶುಭ ಸಂಜೆ': 'Good evening'\r\n      }\r\n    };\r\n\r\n    const patternKey = `${sourceLang}-${targetLang}`;\r\n    const patterns = translationPatterns[patternKey];\r\n    \r\n    if (patterns) {\r\n      // Try exact match first\r\n      if (patterns[text]) {\r\n        return patterns[text];\r\n      }\r\n      \r\n      // Try partial matches for longer text\r\n      let translatedText = text;\r\n      for (const [source, target] of Object.entries(patterns)) {\r\n        if (text.includes(source)) {\r\n          translatedText = translatedText.replace(source, target);\r\n        }\r\n      }\r\n      \r\n      if (translatedText !== text) {\r\n        return translatedText;\r\n      }\r\n    }\r\n\r\n    return text; // Return original if no translation found\r\n  }\r\n\r\n  // Function to extract and preserve capital words during translation\r\n  static extractCapitalWords(text: string): { text: string; capitalWords: Array<{ word: string; placeholder: string }> } {\r\n    const capitalWordsMatches = text.match(/\\b[A-Z]{2,}\\b/g) || [];\r\n    const capitalWords = capitalWordsMatches.map((word: string) => ({\r\n      word,\r\n      placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`\r\n    }));\r\n\r\n    let textWithPlaceholders = text;\r\n    capitalWords.forEach((item) => {\r\n      textWithPlaceholders = textWithPlaceholders.replace(item.word, item.placeholder);\r\n    });\r\n\r\n    return { text: textWithPlaceholders, capitalWords };\r\n  }\r\n\r\n  // Function to restore capital words after translation\r\n  static restoreCapitalWords(text: string, capitalWords: Array<{ word: string; placeholder: string }>): string {\r\n    let restoredText = text;\r\n    capitalWords.forEach((item) => {\r\n      restoredText = restoredText.replace(item.placeholder, item.word);\r\n    });\r\n    return restoredText;\r\n  }\r\n\r\n  // Function to translate text while preserving capital words\r\n  static async translateWithCapitalWordsPreservation(\r\n    text: string,\r\n    sourceLang: string,\r\n    targetLang: string\r\n  ): Promise<string> {\r\n    const { text: textWithPlaceholders, capitalWords } = this.extractCapitalWords(text);\r\n    const translatedText = await this.translateText(textWithPlaceholders, sourceLang, targetLang);\r\n    return this.restoreCapitalWords(translatedText, capitalWords);\r\n  }\r\n\r\n  // Function to translate entire response objects\r\n  static async translateResponse(response: any, targetLang: string): Promise<any> {\r\n    if (!response || !targetLang) return response;\r\n\r\n    const translatedResponse = { ...response };\r\n\r\n    try {\r\n      // Detect source language from AI response\r\n      const sourceLang = response.ai_response ? this.detectLanguage(response.ai_response) : 'en';\r\n\r\n      // Skip translation if source and target are the same\r\n      if (sourceLang === targetLang) {\r\n        console.log(`⚠️ Source and target languages are the same (${targetLang}), skipping translation`);\r\n        return response;\r\n      }\r\n\r\n      console.log(`🌐 Translating response from ${sourceLang} to ${targetLang}`);\r\n\r\n      // Translate AI response\r\n      if (response.ai_response) {\r\n        translatedResponse.ai_response = await this.translateWithCapitalWordsPreservation(\r\n          response.ai_response,\r\n          sourceLang,\r\n          targetLang\r\n        );\r\n      }\r\n\r\n      // Translate related questions\r\n      if (response.related_questions && Array.isArray(response.related_questions)) {\r\n        translatedResponse.related_questions = await Promise.all(\r\n          response.related_questions.map((question: string) =>\r\n            this.translateWithCapitalWordsPreservation(question, sourceLang, targetLang)\r\n          )\r\n        );\r\n      }\r\n\r\n      // Add translation metadata\r\n      translatedResponse.translation_applied = true;\r\n      translatedResponse.source_language = sourceLang;\r\n      translatedResponse.target_language = targetLang;\r\n      translatedResponse.translation_timestamp = new Date().toISOString();\r\n\r\n      console.log(`✅ Response translation completed: ${sourceLang} -> ${targetLang}`);\r\n      return translatedResponse;\r\n\r\n    } catch (error) {\r\n      console.error('❌ Error translating response:', error);\r\n      // Return original response with error metadata\r\n      return {\r\n        ...response,\r\n        translation_applied: false,\r\n        translation_error: error instanceof Error ? error.message : 'Unknown translation error'\r\n      };\r\n    }\r\n  }\r\n\r\n  // Function to get language name from code\r\n  static getLanguageName(langCode: string): string {\r\n    const languageNames: Record<string, string> = {\r\n      'en': 'English',\r\n      'ta': 'Tamil',\r\n      'te': 'Telugu',\r\n      'kn': 'Kannada',\r\n      'hi': 'Hindi',\r\n      'es': 'Spanish',\r\n      'fr': 'French',\r\n      'de': 'German',\r\n      'zh': 'Chinese',\r\n      'ja': 'Japanese',\r\n      'ko': 'Korean',\r\n      'ar': 'Arabic'\r\n    };\r\n\r\n    return languageNames[langCode] || langCode;\r\n  }\r\n\r\n  // Function to clear translation cache\r\n  static clearCache(): void {\r\n    this.translationCache.clear();\r\n    console.log('🗑️ Translation cache cleared');\r\n  }\r\n\r\n  // Function to get cache statistics\r\n  static getCacheStats(): { size: number; keys: string[] } {\r\n    return {\r\n      size: this.translationCache.size,\r\n      keys: Array.from(this.translationCache.keys()).slice(0, 10) // Show first 10 keys\r\n    };\r\n  }\r\n\r\n  // Fast direct translation using MyMemory API\r\n  static async translateWithMyMemory(text: string, sourceLang: string, targetLang: string): Promise<string | null> {\r\n    try {\r\n      console.log(`🌐 Attempting direct MyMemory translation: ${sourceLang} -> ${targetLang}`);\r\n\r\n      const url = \"https://api.mymemory.translated.net/get\";\r\n      const params = new URLSearchParams({\r\n        q: text,\r\n        langpair: `${sourceLang}|${targetLang}`\r\n      });\r\n\r\n      const response = await fetch(`${url}?${params}`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Accept': 'application/json',\r\n        },\r\n        signal: AbortSignal.timeout(4000) // 4 second timeout for the API call\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        if (data.responseStatus === 200) {\r\n          const translatedText = data.responseData?.translatedText;\r\n          if (translatedText && translatedText !== text) {\r\n            console.log(`✅ MyMemory translation successful: ${translatedText.substring(0, 50)}${translatedText.length > 50 ? '...' : ''}`);\r\n            return translatedText;\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.warn(`⚠️ MyMemory translation failed:`, error);\r\n    }\r\n\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,MAAM;IACX,OAAe,mBAAmB,IAAI,MAAsB;IAE5D,4BAA4B;IAC5B,OAAO,eAAe,IAAY,EAAU;QAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI,OAAO;QAElC,uCAAuC;QACvC,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,wCAAwC;QACxC,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,yCAAyC;QACzC,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,uCAAuC;QACvC,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,mBAAmB;QACnB,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,oBAAoB;QACpB,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,qBAAqB;QACrB,OAAO;IACT;IAEA,gEAAgE;IAChE,aAAa,cAAc,IAAY,EAAE,UAAkB,EAAE,UAAkB,EAAmB;QAChG,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE,EAAE,KAAK,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,KAAK,QAAQ,IAAI;QAE/H,gFAAgF;QAChF,IAAI,eAAe,YAAY;YAC7B,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,yCAAyC;QACzC,MAAM,WAAW,GAAG,WAAW,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM;QACtD,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QACpD,IAAI,mBAAmB;YACrB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,gDAAgD;YAChD,MAAM,iBAAiB,IAAI,QAAgB,CAAC,GAAG;gBAC7C,WAAW,IAAM,OAAO,IAAI,MAAM,yBAAyB,OAAO,mBAAmB;YACvF;YAEA,sDAAsD;YACtD,MAAM,qBAAqB,IAAI,CAAC,qBAAqB,CAAC,MAAM,YAAY;YAExE,MAAM,oBAAoB,MAAM,QAAQ,IAAI,CAAC;gBAAC;gBAAoB;aAAe;YACjF,IAAI,qBAAqB,sBAAsB,MAAM;gBACnD,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,kBAAkB,SAAS,CAAC,GAAG,MAAM,kBAAkB,MAAM,GAAG,KAAK,QAAQ,IAAI;gBACjI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU;gBACpC,OAAO;YACT;YAEA,uDAAuD;YACvD,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,MAAM,uCAAuC;gBAClE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN,aAAa;oBACb,aAAa;gBACf;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE;oBAC1D,MAAM,iBAAiB,KAAK,IAAI,CAAC,eAAe;oBAChD,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,eAAe,SAAS,CAAC,GAAG,MAAM,eAAe,MAAM,GAAG,KAAK,QAAQ,IAAI;oBAC5H,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU;oBACpC,OAAO;gBACT;YACF;YAEA,sCAAsC;YACtC,QAAQ,GAAG,CAAC;YACZ,MAAM,sBAAsB,IAAI,CAAC,sBAAsB,CAAC,MAAM,YAAY;YAE1E,IAAI,wBAAwB,MAAM;gBAChC,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,oBAAoB,SAAS,CAAC,GAAG,MAAM,oBAAoB,MAAM,GAAG,KAAK,QAAQ,IAAI;gBACvI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU;gBACpC,OAAO;YACT;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;QAEA,wCAAwC;QACxC,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU;QACpC,OAAO;IACT;IAEA,8CAA8C;IAC9C,OAAO,uBAAuB,IAAY,EAAE,UAAkB,EAAE,UAAkB,EAAU;QAC1F,gDAAgD;QAChD,MAAM,sBAA8D;YAClE,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,SAAS;gBACT,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,sBAAsB;YACxB;YACA,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,SAAS;gBACT,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,sBAAsB;YACxB;YACA,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,SAAS;gBACT,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,sBAAsB;YACxB;YACA,SAAS;gBACP,WAAW;gBACX,SAAS;gBACT,OAAO;gBACP,SAAS;gBACT,cAAc;gBACd,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;YAClB;YACA,SAAS;gBACP,OAAO;gBACP,cAAc;gBACd,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,cAAc;gBACd,WAAW;gBACX,gBAAgB;YAClB;YACA,SAAS;gBACP,OAAO;gBACP,cAAc;gBACd,QAAQ;gBACR,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,UAAU;gBACV,YAAY;YACd;QACF;QAEA,MAAM,aAAa,GAAG,WAAW,CAAC,EAAE,YAAY;QAChD,MAAM,WAAW,mBAAmB,CAAC,WAAW;QAEhD,IAAI,UAAU;YACZ,wBAAwB;YACxB,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,OAAO,QAAQ,CAAC,KAAK;YACvB;YAEA,sCAAsC;YACtC,IAAI,iBAAiB;YACrB,KAAK,MAAM,CAAC,QAAQ,OAAO,IAAI,OAAO,OAAO,CAAC,UAAW;gBACvD,IAAI,KAAK,QAAQ,CAAC,SAAS;oBACzB,iBAAiB,eAAe,OAAO,CAAC,QAAQ;gBAClD;YACF;YAEA,IAAI,mBAAmB,MAAM;gBAC3B,OAAO;YACT;QACF;QAEA,OAAO,MAAM,0CAA0C;IACzD;IAEA,oEAAoE;IACpE,OAAO,oBAAoB,IAAY,EAAgF;QACrH,MAAM,sBAAsB,KAAK,KAAK,CAAC,qBAAqB,EAAE;QAC9D,MAAM,eAAe,oBAAoB,GAAG,CAAC,CAAC,OAAiB,CAAC;gBAC9D;gBACA,aAAa,CAAC,eAAe,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,CAAC;YAChF,CAAC;QAED,IAAI,uBAAuB;QAC3B,aAAa,OAAO,CAAC,CAAC;YACpB,uBAAuB,qBAAqB,OAAO,CAAC,KAAK,IAAI,EAAE,KAAK,WAAW;QACjF;QAEA,OAAO;YAAE,MAAM;YAAsB;QAAa;IACpD;IAEA,sDAAsD;IACtD,OAAO,oBAAoB,IAAY,EAAE,YAA0D,EAAU;QAC3G,IAAI,eAAe;QACnB,aAAa,OAAO,CAAC,CAAC;YACpB,eAAe,aAAa,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;QACjE;QACA,OAAO;IACT;IAEA,4DAA4D;IAC5D,aAAa,sCACX,IAAY,EACZ,UAAkB,EAClB,UAAkB,EACD;QACjB,MAAM,EAAE,MAAM,oBAAoB,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC9E,MAAM,iBAAiB,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,YAAY;QAClF,OAAO,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;IAClD;IAEA,gDAAgD;IAChD,aAAa,kBAAkB,QAAa,EAAE,UAAkB,EAAgB;QAC9E,IAAI,CAAC,YAAY,CAAC,YAAY,OAAO;QAErC,MAAM,qBAAqB;YAAE,GAAG,QAAQ;QAAC;QAEzC,IAAI;YACF,0CAA0C;YAC1C,MAAM,aAAa,SAAS,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,WAAW,IAAI;YAEtF,qDAAqD;YACrD,IAAI,eAAe,YAAY;gBAC7B,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,WAAW,uBAAuB,CAAC;gBAC/F,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,WAAW,IAAI,EAAE,YAAY;YAEzE,wBAAwB;YACxB,IAAI,SAAS,WAAW,EAAE;gBACxB,mBAAmB,WAAW,GAAG,MAAM,IAAI,CAAC,qCAAqC,CAC/E,SAAS,WAAW,EACpB,YACA;YAEJ;YAEA,8BAA8B;YAC9B,IAAI,SAAS,iBAAiB,IAAI,MAAM,OAAO,CAAC,SAAS,iBAAiB,GAAG;gBAC3E,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CACtD,SAAS,iBAAiB,CAAC,GAAG,CAAC,CAAC,WAC9B,IAAI,CAAC,qCAAqC,CAAC,UAAU,YAAY;YAGvE;YAEA,2BAA2B;YAC3B,mBAAmB,mBAAmB,GAAG;YACzC,mBAAmB,eAAe,GAAG;YACrC,mBAAmB,eAAe,GAAG;YACrC,mBAAmB,qBAAqB,GAAG,IAAI,OAAO,WAAW;YAEjE,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,WAAW,IAAI,EAAE,YAAY;YAC9E,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,+CAA+C;YAC/C,OAAO;gBACL,GAAG,QAAQ;gBACX,qBAAqB;gBACrB,mBAAmB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D;QACF;IACF;IAEA,0CAA0C;IAC1C,OAAO,gBAAgB,QAAgB,EAAU;QAC/C,MAAM,gBAAwC;YAC5C,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QAEA,OAAO,aAAa,CAAC,SAAS,IAAI;IACpC;IAEA,sCAAsC;IACtC,OAAO,aAAmB;QACxB,IAAI,CAAC,gBAAgB,CAAC,KAAK;QAC3B,QAAQ,GAAG,CAAC;IACd;IAEA,mCAAmC;IACnC,OAAO,gBAAkD;QACvD,OAAO;YACL,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAChC,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,qBAAqB;QACnF;IACF;IAEA,6CAA6C;IAC7C,aAAa,sBAAsB,IAAY,EAAE,UAAkB,EAAE,UAAkB,EAA0B;QAC/G,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,WAAW,IAAI,EAAE,YAAY;YAEvF,MAAM,MAAM;YACZ,MAAM,SAAS,IAAI,gBAAgB;gBACjC,GAAG;gBACH,UAAU,GAAG,WAAW,CAAC,EAAE,YAAY;YACzC;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,QAAQ,EAAE;gBAC/C,QAAQ;gBACR,SAAS;oBACP,UAAU;gBACZ;gBACA,QAAQ,YAAY,OAAO,CAAC,MAAM,oCAAoC;YACxE;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,cAAc,KAAK,KAAK;oBAC/B,MAAM,iBAAiB,KAAK,YAAY,EAAE;oBAC1C,IAAI,kBAAkB,mBAAmB,MAAM;wBAC7C,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,eAAe,SAAS,CAAC,GAAG,MAAM,eAAe,MAAM,GAAG,KAAK,QAAQ,IAAI;wBAC7H,OAAO;oBACT;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,+BAA+B,CAAC,EAAE;QAClD;QAEA,OAAO;IACT;AACF"}}, {"offset": {"line": 3095, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/chatComponents/services/CacheService.ts"], "sourcesContent": ["export interface CachedResponse {\r\n  ai_response: string;\r\n  related_questions?: string[];\r\n  sentence_analysis?: Array<{ sentence: string; url: string; summary?: string }>;\r\n  pinecone_indexes?: string[];\r\n  faiss_categories?: any[];\r\n  has_uploaded_content?: boolean;\r\n  upload_sources?: any[];\r\n  timestamp: number;\r\n  query: string;\r\n  context?: string; // For context-aware caching (index, email, etc.)\r\n  language?: string; // Language of the response\r\n  query_language?: string; // Language of the original query\r\n  translation_applied?: boolean; // Whether translation was applied\r\n  translation_metadata?: any; // Translation metadata\r\n}\r\n\r\nexport interface CacheStats {\r\n  totalQueries: number;\r\n  cacheHits: number;\r\n  cacheMisses: number;\r\n  hitRate: number;\r\n  cacheSize: number;\r\n}\r\n\r\nexport class CacheService {\r\n  private static readonly CACHE_KEY = 'financial_query_cache';\r\n  private static readonly MAX_CACHE_SIZE = 100; // Maximum number of cached responses\r\n  private static readonly CACHE_EXPIRY_HOURS = 24; // Cache expires after 24 hours\r\n  private static readonly STATS_KEY = 'financial_query_cache_stats';\r\n  public static readonly ARTIFICIAL_DELAY_MS = 2000; // 2-second delay for cached responses\r\n\r\n  /**\r\n   * Generate a cache key based on query, context, and language\r\n   */\r\n  private static generateCacheKey(query: string, context?: string, language?: string): string {\r\n    const normalizedQuery = query.trim().toLowerCase();\r\n    const contextStr = context || '';\r\n    const languageStr = language || 'en';\r\n    return `${normalizedQuery}|${contextStr}|${languageStr}`;\r\n  }\r\n\r\n  /**\r\n   * Get cached response for a query with language support\r\n   */\r\n  static getCachedResponse(query: string, context?: string, language?: string): CachedResponse | null {\r\n    try {\r\n      if (typeof window === 'undefined') return null;\r\n\r\n      const cacheKey = this.generateCacheKey(query, context, language);\r\n      const cacheData = localStorage.getItem(this.CACHE_KEY);\r\n\r\n      if (!cacheData) {\r\n        this.updateStats('miss');\r\n        return null;\r\n      }\r\n\r\n      const cache: Record<string, CachedResponse> = JSON.parse(cacheData);\r\n      const cachedItem = cache[cacheKey];\r\n\r\n      if (!cachedItem) {\r\n        this.updateStats('miss');\r\n        return null;\r\n      }\r\n\r\n      // Check if cache has expired\r\n      const now = Date.now();\r\n      const expiryTime = cachedItem.timestamp + (this.CACHE_EXPIRY_HOURS * 60 * 60 * 1000);\r\n\r\n      if (now > expiryTime) {\r\n        // Remove expired item\r\n        delete cache[cacheKey];\r\n        localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));\r\n        this.updateStats('miss');\r\n        return null;\r\n      }\r\n\r\n      this.updateStats('hit');\r\n      console.log(`🎯 Cache HIT for query: \"${query.substring(0, 50)}...\" (Language: ${language || 'default'})`);\r\n      console.log(`⚡ Cached response found - will apply ${this.ARTIFICIAL_DELAY_MS}ms delay for consistent UX`);\r\n      return cachedItem;\r\n    } catch (error) {\r\n      console.error('Error retrieving cached response:', error);\r\n      this.updateStats('miss');\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cache a response for a query with language support\r\n   */\r\n  static setCachedResponse(query: string, response: any, context?: string, language?: string): void {\r\n    try {\r\n      if (typeof window === 'undefined') return;\r\n\r\n      const cacheKey = this.generateCacheKey(query, context, language);\r\n      const cacheData = localStorage.getItem(this.CACHE_KEY);\r\n\r\n      let cache: Record<string, CachedResponse> = {};\r\n      if (cacheData) {\r\n        cache = JSON.parse(cacheData);\r\n      }\r\n\r\n      // Create cached response object with language metadata\r\n      const cachedResponse: CachedResponse = {\r\n        ai_response: response.ai_response || '',\r\n        related_questions: response.related_questions || [],\r\n        sentence_analysis: response.sentence_analysis || [],\r\n        pinecone_indexes: response.pinecone_indexes || [],\r\n        faiss_categories: response.faiss_categories || [],\r\n        has_uploaded_content: response.has_uploaded_content,\r\n        upload_sources: response.upload_sources || [],\r\n        timestamp: Date.now(),\r\n        query: query.trim(),\r\n        context: context,\r\n        language: language,\r\n        query_language: response.query_language,\r\n        translation_applied: response.translation_applied || false,\r\n        translation_metadata: response.translation_metadata\r\n      };\r\n\r\n      // Add to cache\r\n      cache[cacheKey] = cachedResponse;\r\n\r\n      // Implement LRU eviction if cache is too large\r\n      const cacheKeys = Object.keys(cache);\r\n      if (cacheKeys.length > this.MAX_CACHE_SIZE) {\r\n        // Sort by timestamp and remove oldest entries\r\n        const sortedEntries = cacheKeys\r\n          .map(key => ({ key, timestamp: cache[key].timestamp }))\r\n          .sort((a, b) => a.timestamp - b.timestamp);\r\n\r\n        // Remove oldest entries to make room\r\n        const entriesToRemove = sortedEntries.slice(0, cacheKeys.length - this.MAX_CACHE_SIZE + 1);\r\n        entriesToRemove.forEach(entry => {\r\n          delete cache[entry.key];\r\n        });\r\n      }\r\n\r\n      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));\r\n      console.log(`💾 Cached response for query: \"${query.substring(0, 50)}...\" (Language: ${language || 'default'})`);\r\n    } catch (error) {\r\n      console.error('Error caching response:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update cache statistics\r\n   */\r\n  private static updateStats(type: 'hit' | 'miss'): void {\r\n    try {\r\n      if (typeof window === 'undefined') return;\r\n\r\n      const statsData = localStorage.getItem(this.STATS_KEY);\r\n      let stats = {\r\n        totalQueries: 0,\r\n        cacheHits: 0,\r\n        cacheMisses: 0\r\n      };\r\n\r\n      if (statsData) {\r\n        stats = JSON.parse(statsData);\r\n      }\r\n\r\n      stats.totalQueries++;\r\n      if (type === 'hit') {\r\n        stats.cacheHits++;\r\n      } else {\r\n        stats.cacheMisses++;\r\n      }\r\n\r\n      localStorage.setItem(this.STATS_KEY, JSON.stringify(stats));\r\n    } catch (error) {\r\n      console.error('Error updating cache stats:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cache statistics\r\n   */\r\n  static getCacheStats(): CacheStats {\r\n    try {\r\n      if (typeof window === 'undefined') {\r\n        return { totalQueries: 0, cacheHits: 0, cacheMisses: 0, hitRate: 0, cacheSize: 0 };\r\n      }\r\n\r\n      const statsData = localStorage.getItem(this.STATS_KEY);\r\n      const cacheData = localStorage.getItem(this.CACHE_KEY);\r\n      \r\n      let stats = {\r\n        totalQueries: 0,\r\n        cacheHits: 0,\r\n        cacheMisses: 0\r\n      };\r\n\r\n      if (statsData) {\r\n        stats = JSON.parse(statsData);\r\n      }\r\n\r\n      const cacheSize = cacheData ? Object.keys(JSON.parse(cacheData)).length : 0;\r\n      const hitRate = stats.totalQueries > 0 ? (stats.cacheHits / stats.totalQueries) * 100 : 0;\r\n\r\n      return {\r\n        totalQueries: stats.totalQueries,\r\n        cacheHits: stats.cacheHits,\r\n        cacheMisses: stats.cacheMisses,\r\n        hitRate: Math.round(hitRate * 100) / 100,\r\n        cacheSize\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting cache stats:', error);\r\n      return { totalQueries: 0, cacheHits: 0, cacheMisses: 0, hitRate: 0, cacheSize: 0 };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear all cached responses\r\n   */\r\n  static clearCache(): void {\r\n    try {\r\n      if (typeof window === 'undefined') return;\r\n      \r\n      localStorage.removeItem(this.CACHE_KEY);\r\n      localStorage.removeItem(this.STATS_KEY);\r\n      console.log('🗑️ Cache cleared successfully');\r\n    } catch (error) {\r\n      console.error('Error clearing cache:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove expired cache entries\r\n   */\r\n  static cleanupExpiredCache(): void {\r\n    try {\r\n      if (typeof window === 'undefined') return;\r\n\r\n      const cacheData = localStorage.getItem(this.CACHE_KEY);\r\n      if (!cacheData) return;\r\n\r\n      const cache: Record<string, CachedResponse> = JSON.parse(cacheData);\r\n      const now = Date.now();\r\n      const expiryTime = this.CACHE_EXPIRY_HOURS * 60 * 60 * 1000;\r\n\r\n      let removedCount = 0;\r\n      Object.keys(cache).forEach(key => {\r\n        if (now - cache[key].timestamp > expiryTime) {\r\n          delete cache[key];\r\n          removedCount++;\r\n        }\r\n      });\r\n\r\n      if (removedCount > 0) {\r\n        localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));\r\n        console.log(`🧹 Cleaned up ${removedCount} expired cache entries`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error cleaning up expired cache:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Apply artificial delay for cached responses to ensure consistent UX\r\n   */\r\n  static async applyCachedResponseDelay(): Promise<void> {\r\n    console.log(`⏳ Applying ${this.ARTIFICIAL_DELAY_MS}ms artificial delay for cached response...`);\r\n    return new Promise(resolve => {\r\n      setTimeout(() => {\r\n        console.log(`✅ Artificial delay completed - returning cached response`);\r\n        resolve();\r\n      }, this.ARTIFICIAL_DELAY_MS);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get cached response with automatic delay application\r\n   */\r\n  static async getCachedResponseWithDelay(query: string, context?: string, language?: string): Promise<CachedResponse | null> {\r\n    const cachedResponse = this.getCachedResponse(query, context, language);\r\n\r\n    if (cachedResponse) {\r\n      // Apply artificial delay for consistent UX\r\n      await this.applyCachedResponseDelay();\r\n      return cachedResponse;\r\n    }\r\n\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAyBO,MAAM;IACX,OAAwB,YAAY,wBAAwB;IAC5D,OAAwB,iBAAiB,IAAI;IAC7C,OAAwB,qBAAqB,GAAG;IAChD,OAAwB,YAAY,8BAA8B;IAClE,OAAuB,sBAAsB,KAAK;IAElD;;GAEC,GACD,OAAe,iBAAiB,KAAa,EAAE,OAAgB,EAAE,QAAiB,EAAU;QAC1F,MAAM,kBAAkB,MAAM,IAAI,GAAG,WAAW;QAChD,MAAM,aAAa,WAAW;QAC9B,MAAM,cAAc,YAAY;QAChC,OAAO,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,EAAE,aAAa;IAC1D;IAEA;;GAEC,GACD,OAAO,kBAAkB,KAAa,EAAE,OAAgB,EAAE,QAAiB,EAAyB;QAClG,IAAI;YACF,wCAAmC,OAAO;;YAE1C,MAAM;YACN,MAAM;YAON,MAAM;YACN,MAAM;YAON,6BAA6B;YAC7B,MAAM;YACN,MAAM;QAcR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,IAAI,CAAC,WAAW,CAAC;YACjB,OAAO;QACT;IACF;IAEA;;GAEC,GACD,OAAO,kBAAkB,KAAa,EAAE,QAAa,EAAE,OAAgB,EAAE,QAAiB,EAAQ;QAChG,IAAI;YACF,wCAAmC;;YAEnC,MAAM;YACN,MAAM;YAEN,IAAI;YAKJ,uDAAuD;YACvD,MAAM;YAoBN,+CAA+C;YAC/C,MAAM;QAgBR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA;;GAEC,GACD,OAAe,YAAY,IAAoB,EAAQ;QACrD,IAAI;YACF,wCAAmC;;YAEnC,MAAM;YACN,IAAI;QAkBN,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA;;GAEC,GACD,OAAO,gBAA4B;QACjC,IAAI;YACF,wCAAmC;gBACjC,OAAO;oBAAE,cAAc;oBAAG,WAAW;oBAAG,aAAa;oBAAG,SAAS;oBAAG,WAAW;gBAAE;YACnF;;YAEA,MAAM;YACN,MAAM;YAEN,IAAI;YAUJ,MAAM;YACN,MAAM;QASR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBAAE,cAAc;gBAAG,WAAW;gBAAG,aAAa;gBAAG,SAAS;gBAAG,WAAW;YAAE;QACnF;IACF;IAEA;;GAEC,GACD,OAAO,aAAmB;QACxB,IAAI;YACF,wCAAmC;;QAKrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA;;GAEC,GACD,OAAO,sBAA4B;QACjC,IAAI;YACF,wCAAmC;;YAEnC,MAAM;YAGN,MAAM;YACN,MAAM;YACN,MAAM;YAEN,IAAI;QAYN,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA;;GAEC,GACD,aAAa,2BAA0C;QACrD,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,0CAA0C,CAAC;QAC9F,OAAO,IAAI,QAAQ,CAAA;YACjB,WAAW;gBACT,QAAQ,GAAG,CAAC,CAAC,wDAAwD,CAAC;gBACtE;YACF,GAAG,IAAI,CAAC,mBAAmB;QAC7B;IACF;IAEA;;GAEC,GACD,aAAa,2BAA2B,KAAa,EAAE,OAAgB,EAAE,QAAiB,EAAkC;QAC1H,MAAM,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,OAAO,SAAS;QAE9D,IAAI,gBAAgB;YAClB,2CAA2C;YAC3C,MAAM,IAAI,CAAC,wBAAwB;YACnC,OAAO;QACT;QAEA,OAAO;IACT;AACF"}}, {"offset": {"line": 3244, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3250, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/debug/ConnectionTest.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { testConnection, checkBackendHealth, processYouTubeURL } from '../../services/uploadService';\r\n\r\ninterface ConnectionTestProps {\r\n  onClose?: () => void;\r\n}\r\n\r\nconst ConnectionTest: React.FC<ConnectionTestProps> = ({ onClose }) => {\r\n  const [testResults, setTestResults] = useState<any[]>([]);\r\n  const [isRunning, setIsRunning] = useState(false);\r\n\r\n  const addResult = (test: string, result: any) => {\r\n    setTestResults(prev => [...prev, { test, result, timestamp: new Date().toISOString() }]);\r\n  };\r\n\r\n  const runTests = async () => {\r\n    setIsRunning(true);\r\n    setTestResults([]);\r\n\r\n    try {\r\n      // Test 1: Basic connection\r\n      console.log('🔌 Running connection test...');\r\n      const connectionResult = await testConnection();\r\n      addResult('Connection Test', connectionResult);\r\n\r\n      // Test 2: Health check\r\n      console.log('🏥 Running health check...');\r\n      const healthResult = await checkBackendHealth();\r\n      addResult('Health Check', healthResult);\r\n\r\n      // Test 3: YouTube URL processing (with a short test video)\r\n      console.log('🎥 Testing YouTube processing...');\r\n      const youtubeResult = await processYouTubeURL('https://youtu.be/dQw4w9WgXcQ', {\r\n        index_name: 'default',\r\n        client_email: '<EMAIL>'\r\n      });\r\n      addResult('YouTube Processing', youtubeResult);\r\n\r\n    } catch (error) {\r\n      console.error('❌ Test suite error:', error);\r\n      addResult('Test Suite Error', { success: false, error: error.message });\r\n    } finally {\r\n      setIsRunning(false);\r\n    }\r\n  };\r\n\r\n  const clearResults = () => {\r\n    setTestResults([]);\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-auto\">\r\n        <div className=\"flex justify-between items-center mb-4\">\r\n          <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\r\n            Backend Connection Test\r\n          </h2>\r\n          {onClose && (\r\n            <button\r\n              onClick={onClose}\r\n              className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n            >\r\n              ✕\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"space-y-4\">\r\n          <div className=\"flex gap-2\">\r\n            <button\r\n              onClick={runTests}\r\n              disabled={isRunning}\r\n              className={`px-4 py-2 rounded-lg text-white font-medium ${\r\n                isRunning\r\n                  ? 'bg-gray-400 cursor-not-allowed'\r\n                  : 'bg-blue-500 hover:bg-blue-600'\r\n              }`}\r\n            >\r\n              {isRunning ? 'Running Tests...' : 'Run Tests'}\r\n            </button>\r\n            <button\r\n              onClick={clearResults}\r\n              className=\"px-4 py-2 rounded-lg bg-gray-500 hover:bg-gray-600 text-white font-medium\"\r\n            >\r\n              Clear Results\r\n            </button>\r\n          </div>\r\n\r\n          {testResults.length > 0 && (\r\n            <div className=\"space-y-3\">\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\r\n                Test Results\r\n              </h3>\r\n              {testResults.map((result, index) => (\r\n                <div\r\n                  key={index}\r\n                  className={`p-4 rounded-lg border ${\r\n                    result.result.success\r\n                      ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'\r\n                      : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'\r\n                  }`}\r\n                >\r\n                  <div className=\"flex items-center gap-2 mb-2\">\r\n                    <span\r\n                      className={`w-3 h-3 rounded-full ${\r\n                        result.result.success ? 'bg-green-500' : 'bg-red-500'\r\n                      }`}\r\n                    />\r\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n                      {result.test}\r\n                    </h4>\r\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                      {new Date(result.timestamp).toLocaleTimeString()}\r\n                    </span>\r\n                  </div>\r\n                  \r\n                  {result.result.success ? (\r\n                    <p className=\"text-green-700 dark:text-green-300\">\r\n                      ✅ {result.result.message || 'Success'}\r\n                    </p>\r\n                  ) : (\r\n                    <p className=\"text-red-700 dark:text-red-300\">\r\n                      ❌ {result.result.error || 'Failed'}\r\n                    </p>\r\n                  )}\r\n\r\n                  {/* Show detailed result data */}\r\n                  <details className=\"mt-2\">\r\n                    <summary className=\"cursor-pointer text-sm text-gray-600 dark:text-gray-400\">\r\n                      Show Details\r\n                    </summary>\r\n                    <pre className=\"mt-2 text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto\">\r\n                      {JSON.stringify(result.result, null, 2)}\r\n                    </pre>\r\n                  </details>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n\r\n          {isRunning && (\r\n            <div className=\"flex items-center gap-2 text-blue-600 dark:text-blue-400\">\r\n              <div className=\"animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full\" />\r\n              <span>Running tests...</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n          <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\r\n            Debug Information\r\n          </h4>\r\n          <div className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\r\n            <p><strong>Backend URL:</strong> http://localhost:5010</p>\r\n            <p><strong>Environment:</strong> {process.env.NODE_ENV || 'development'}</p>\r\n            <p><strong>User Agent:</strong> {navigator.userAgent}</p>\r\n            <p><strong>Current Time:</strong> {new Date().toISOString()}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ConnectionTest;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,iBAAgD,CAAC,EAAE,OAAO,EAAE;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,YAAY,CAAC,MAAc;QAC/B,eAAe,CAAA,OAAQ;mBAAI;gBAAM;oBAAE;oBAAM;oBAAQ,WAAW,IAAI,OAAO,WAAW;gBAAG;aAAE;IACzF;IAEA,MAAM,WAAW;QACf,aAAa;QACb,eAAe,EAAE;QAEjB,IAAI;YACF,2BAA2B;YAC3B,QAAQ,GAAG,CAAC;YACZ,MAAM,mBAAmB,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD;YAC5C,UAAU,mBAAmB;YAE7B,uBAAuB;YACvB,QAAQ,GAAG,CAAC;YACZ,MAAM,eAAe,MAAM,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD;YAC5C,UAAU,gBAAgB;YAE1B,2DAA2D;YAC3D,QAAQ,GAAG,CAAC;YACZ,MAAM,gBAAgB,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,gCAAgC;gBAC5E,YAAY;gBACZ,cAAc;YAChB;YACA,UAAU,sBAAsB;QAElC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,UAAU,oBAAoB;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QACvE,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,eAAe,EAAE;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;wBAG/D,yBACC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAML,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAW,CAAC,4CAA4C,EACtD,YACI,mCACA,iCACJ;8CAED,YAAY,qBAAqB;;;;;;8CAEpC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;wBAKF,YAAY,MAAM,GAAG,mBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;gCAGnE,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;wCAEC,WAAW,CAAC,sBAAsB,EAChC,OAAO,MAAM,CAAC,OAAO,GACjB,4EACA,mEACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAW,CAAC,qBAAqB,EAC/B,OAAO,MAAM,CAAC,OAAO,GAAG,iBAAiB,cACzC;;;;;;kEAEJ,8OAAC;wDAAG,WAAU;kEACX,OAAO,IAAI;;;;;;kEAEd,8OAAC;wDAAK,WAAU;kEACb,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB;;;;;;;;;;;;4CAIjD,OAAO,MAAM,CAAC,OAAO,iBACpB,8OAAC;gDAAE,WAAU;;oDAAqC;oDAC7C,OAAO,MAAM,CAAC,OAAO,IAAI;;;;;;qEAG9B,8OAAC;gDAAE,WAAU;;oDAAiC;oDACzC,OAAO,MAAM,CAAC,KAAK,IAAI;;;;;;;0DAK9B,8OAAC;gDAAQ,WAAU;;kEACjB,8OAAC;wDAAQ,WAAU;kEAA0D;;;;;;kEAG7E,8OAAC;wDAAI,WAAU;kEACZ,KAAK,SAAS,CAAC,OAAO,MAAM,EAAE,MAAM;;;;;;;;;;;;;uCArCpC;;;;;;;;;;;wBA6CZ,2BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;8BAKZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAqB;;;;;;;8CAChC,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAqB;wCAAE,mDAAwB;;;;;;;8CAC1D,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAoB;wCAAE,UAAU,SAAS;;;;;;;8CACpD,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAsB;wCAAE,IAAI,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrE;uCAEe"}}, {"offset": {"line": 3605, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3611, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/chatComponents/ChatBox.tsx"], "sourcesContent": ["import React, { FormEvent, useState, useRef, useEffect, forwardRef, useImperativeHandle } from \"react\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport {\r\n  PiArrowUp,\r\n  PiMicrophone,\r\n  PiPaperclip,\r\n  PiLightbulb,\r\n  PiSparkle,\r\n  PiGlobe,\r\n  PiStop,\r\n  PiWaveform,\r\n  PiPencilSimple,\r\n  PiCheck,\r\n  PiSpinner,\r\n  PiFile,\r\n  PiFileText,\r\n  PiMusicNote,\r\n  PiYoutubeLogo,\r\n  PiLink,\r\n  PiX\r\n} from \"react-icons/pi\";\r\nimport { useChatHandler } from \"@/stores/chatList\";\r\nimport SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';\r\n// Import components that are actually used\r\nimport ChatInputUpload from \"./ChatInputUpload\";\r\n// import UploadedContentIndicator from \"./UploadedContentIndicator\";\r\n// Import services that are actually used\r\nimport { ApiService } from \"./services/ApiService\";\r\nimport { TranslationService } from \"./services/TranslationService\";\r\nimport { CacheService } from \"./services/CacheService\";\r\n// Import debug component for connection testing\r\nimport ConnectionTest from \"../debug/ConnectionTest\";\r\n\r\n\r\n\r\ninterface ChatBoxProps {\r\n  onLanguageChange?: (language: string) => void;\r\n}\r\n\r\nexport interface ChatBoxRef {\r\n  setInputFromQuestion: (question: string) => void;\r\n}\r\n\r\nconst ChatBox = forwardRef<ChatBoxRef, ChatBoxProps>(({ onLanguageChange }, ref) => {\r\n  const [inputText, setInputText] = useState(\"\");\r\n  const [showSuggestions, setShowSuggestions] = useState(false);\r\n  // Add language dropup state for responsive design\r\n  const [showLanguageMenu, setShowLanguageMenu] = useState(false);\r\n  const [selectedLanguage, setSelectedLanguage] = useState(\"English\");\r\n  const [isListening, setIsListening] = useState(false);\r\n  const [speaking, setSpeaking] = useState(false);\r\n  const [wordCount, setWordCount] = useState(0);\r\n  // Track recent words for word count\r\n  const [recentWords, setRecentWords] = useState<string[]>([]);\r\n  // Add state for transcript editing\r\n  const [isEditingTranscript, setIsEditingTranscript] = useState(false);\r\n  const [editedTranscript, setEditedTranscript] = useState(\"\");\r\n  // Add state for language validation\r\n  const [languageError, setLanguageError] = useState<string | null>(null);\r\n  // Add state to track if language buttons should be disabled\r\n  const [languageButtonsDisabled, setLanguageButtonsDisabled] = useState(false);\r\n  // Add state for user email from localStorage\r\n  const [userEmail, setUserEmail] = useState<string | null>(null);\r\n  // Add state for FAISS indexes - fetch from PINE collection\r\n  const [pineconeIndexes, setPineconeIndexes] = useState<string[]>([]);\r\n  const [selectedIndex, setSelectedIndex] = useState<string>('');\r\n  const [apiEnvironment, setApiEnvironment] = useState<'development' | 'production'>('production');\r\n  // Add state for loading indexes\r\n  const [indexesLoading, setIndexesLoading] = useState<boolean>(true);\r\n  // Add state for index selector dropdown visibility\r\n  const [showIndexSelector, setShowIndexSelector] = useState<boolean>(false);\r\n  // Add state for index selection confirmation\r\n  const [showIndexConfirmation, setShowIndexConfirmation] = useState<boolean>(false);\r\n  // Add state to track if user has made first request - check localStorage for persistence\r\n  const [hasUserMadeFirstRequest, setHasUserMadeFirstRequest] = useState<boolean>(() => {\r\n    if (typeof window !== 'undefined') {\r\n      const hasFirstRequest = localStorage.getItem('hasUserMadeFirstRequest') === 'true';\r\n      console.log('🔍 ChatBox: hasUserMadeFirstRequest initialized to:', hasFirstRequest);\r\n      return hasFirstRequest;\r\n    }\r\n    return false;\r\n  });\r\n  // Add state to track upload component state\r\n  const [uploadIsActive, setUploadIsActive] = useState<boolean>(false);\r\n  const [uploadDropdownVisible, setUploadDropdownVisible] = useState<boolean>(false);\r\n  // Add state for connection test dialog\r\n  const [showConnectionTest, setShowConnectionTest] = useState<boolean>(false);\r\n\r\n  // Handle upload state changes\r\n  const handleUploadStateChange = (isActive: boolean, showDropdown: boolean) => {\r\n    setUploadIsActive(isActive);\r\n    setUploadDropdownVisible(showDropdown);\r\n  };\r\n  // Add state for uploaded content display\r\n  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);\r\n  const [uploadedURLs, setUploadedURLs] = useState<Array<{url: string, type: 'youtube' | 'article'}>>([]);\r\n  const [showUploadedContent, setShowUploadedContent] = useState<boolean>(false);\r\n  // Add state for response data sources\r\n  const [responseHasUploadedContent, setResponseHasUploadedContent] = useState<boolean>(false);\r\n  const [responseUploadSources, setResponseUploadSources] = useState<string[]>([]);\r\n  // Reference for the index selector dropdown\r\n  const indexSelectorRef = useRef<HTMLDivElement>(null);\r\n  const suggestionsRef = useRef<HTMLDivElement>(null);\r\n  // Add language menu ref for responsive design\r\n  const languageMenuRef = useRef<HTMLDivElement>(null);\r\n  const transcriptRef = useRef<HTMLDivElement>(null);\r\n  const editableTranscriptRef = useRef<HTMLTextAreaElement>(null);\r\n  const router = useRouter();\r\n  const path = usePathname();\r\n  const { userQuery, handleSubmit, addMessage, setUserQuery, isLoading, setIsLoading, chatList } = useChatHandler();\r\n\r\n  // Check if current chat has existing messages\r\n  useEffect(() => {\r\n    const chatIdUrl = path && path.includes(\"/chat/\") ? path.split(\"/chat/\")[1] : \"\";\r\n    if (chatIdUrl) {\r\n      const currentChat = chatList.find(chat => chat.id === chatIdUrl);\r\n      if (currentChat && currentChat.messages && currentChat.messages.length > 0) {\r\n        console.log(\"🔍 ChatBox: Found existing chat with messages, setting hasUserMadeFirstRequest to true\");\r\n        setHasUserMadeFirstRequest(true);\r\n        if (typeof window !== 'undefined') {\r\n          localStorage.setItem('hasUserMadeFirstRequest', 'true');\r\n        }\r\n      }\r\n    }\r\n  }, [chatList, path]);\r\n\r\n  // Speech detection timer\r\n  const speakingTimerRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Helper function to send a message with a specific text (for related questions)\r\n  const handleSendMessageWithText = async (text: string) => {\r\n    console.log(\"🎯 ChatBox: handleSendMessageWithText called with:\", text);\r\n    await sendMessageInternal(text);\r\n  };\r\n\r\n  // Expose methods to parent component\r\n  useImperativeHandle(ref, () => ({\r\n    setInputFromQuestion: (question: string) => {\r\n      console.log(\"🎯 ChatBox: setInputFromQuestion called with:\", question);\r\n      console.log(\"🎯 ChatBox: Current isLoading state:\", isLoading);\r\n      \r\n      // Update both state variables\r\n      setInputText(question);\r\n      setUserQuery(question);\r\n\r\n      // Use requestAnimationFrame to ensure DOM updates are complete\r\n      requestAnimationFrame(() => {\r\n        const inputElement = document.querySelector('input.w-full.outline-none.p-4.pr-12.bg-transparent') as HTMLInputElement;\r\n        if (inputElement) {\r\n          console.log(\"✅ ChatBox: Found input element, updating value\");\r\n          // Set the value directly on the input element\r\n          inputElement.value = question;\r\n          // Create and dispatch input event\r\n          const inputEvent = new Event('input', { bubbles: true });\r\n          inputElement.dispatchEvent(inputEvent);\r\n          // Create and dispatch change event\r\n          const changeEvent = new Event('change', { bubbles: true });\r\n          inputElement.dispatchEvent(changeEvent);\r\n          // Focus the input\r\n          inputElement.focus();\r\n          // Set cursor to end of text\r\n          inputElement.setSelectionRange(question.length, question.length);\r\n          console.log(\"✅ ChatBox: Input element updated and focused\");\r\n          \r\n          // Automatically trigger the send action after a short delay\r\n          setTimeout(() => {\r\n            console.log(\"🎯 ChatBox: Timeout triggered, checking conditions\");\r\n            console.log(\"🎯 ChatBox: question.trim():\", question.trim());\r\n            console.log(\"🎯 ChatBox: isLoading:\", isLoading);\r\n            console.log(\"🎯 ChatBox: question.trim() && !isLoading:\", question.trim() && !isLoading);\r\n            \r\n            if (question.trim() && !isLoading) {\r\n              console.log(\"✅ ChatBox: Conditions met, calling handleSendMessage directly with question\");\r\n              // Create a synthetic keyboard event to simulate pressing Enter\r\n              const keyboardEvent = new KeyboardEvent('keydown', {\r\n                key: 'Enter',\r\n                code: 'Enter',\r\n                keyCode: 13,\r\n                which: 13,\r\n                bubbles: true,\r\n                cancelable: true\r\n              });\r\n              inputElement.dispatchEvent(keyboardEvent);\r\n              console.log(\"🚀 ChatBox: Automatically triggered Enter key press for selected question\");\r\n            } else {\r\n              console.warn(\"❌ ChatBox: Conditions not met for automatic send\");\r\n              console.warn(\"❌ ChatBox: question.trim():\", question.trim());\r\n              console.warn(\"❌ ChatBox: isLoading:\", isLoading);\r\n            }\r\n          }, 300); // Slightly longer delay to ensure all state updates are complete\r\n        } else {\r\n          console.warn(\"❌ ChatBox: Could not find input element!\");\r\n        }\r\n      });\r\n    }\r\n  }));\r\n\r\n\r\n\r\n  // Available languages for voice input with their language codes - English, Tamil, Telugu, and Kannada\r\n  const languages = [\r\n    { name: \"English\", code: \"en-US\", color: \"blue\" },\r\n    { name: \"Tamil\", code: \"ta-IN\", color: \"purple\" },\r\n    { name: \"Telugu\", code: \"te-IN\", color: \"green\" },\r\n    { name: \"Kannada\", code: \"kn-IN\", color: \"orange\" }\r\n  ];\r\n\r\n  // Get the language code for the selected language\r\n  const getLanguageCode = () => {\r\n    const language = languages.find(lang => lang.name === selectedLanguage);\r\n    return language ? language.code : \"en-US\"; // Default to English if not found\r\n  };\r\n\r\n  // Initialize with default values for server-side rendering\r\n  const [transcript, setTranscript] = useState(\"\");\r\n  const [listening, setListening] = useState(false);\r\n  const [browserSupportsSpeechRecognition, setBrowserSupportsSpeechRecognition] = useState(false);\r\n  const [isMicrophoneAvailable, setIsMicrophoneAvailable] = useState(false);\r\n\r\n  // Speech recognition setup - only run on client side\r\n  const {\r\n    transcript: clientTranscript,\r\n    listening: clientListening,\r\n    resetTranscript,\r\n    browserSupportsSpeechRecognition: clientBrowserSupport,\r\n    isMicrophoneAvailable: clientMicrophoneAvailable,\r\n    // We don't need these transcripts as we're using the combined transcript\r\n    // interimTranscript,\r\n    // finalTranscript\r\n  } = typeof window !== 'undefined' ? useSpeechRecognition({\r\n    clearTranscriptOnListen: false,\r\n    transcribing: true,\r\n    commands: [\r\n      {\r\n        command: '*',\r\n        callback: (command) => {\r\n          console.log('Voice command detected:', command);\r\n        }\r\n      }\r\n    ]\r\n  }) : {\r\n    transcript: \"\",\r\n    listening: false,\r\n    resetTranscript: () => {},\r\n    browserSupportsSpeechRecognition: false,\r\n    isMicrophoneAvailable: false\r\n  };\r\n\r\n  // Handle click outside to close dropdowns\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      // Close suggestions dropdown if clicked outside\r\n      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {\r\n        setShowSuggestions(false);\r\n      }\r\n\r\n      // Close language menu if clicked outside\r\n      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target as Node)) {\r\n        setShowLanguageMenu(false);\r\n      }\r\n\r\n      // Close index selector if clicked outside\r\n      if (indexSelectorRef.current && !indexSelectorRef.current.contains(event.target as Node)) {\r\n        setShowIndexSelector(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  // Auto-scroll transcript to bottom when it gets too long\r\n  useEffect(() => {\r\n    if (transcriptRef.current && transcript) {\r\n      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;\r\n    }\r\n  }, [transcript]);\r\n\r\n  // Update word count when transcript changes\r\n  useEffect(() => {\r\n    if (transcript) {\r\n      const words = transcript.trim().split(/\\s+/).filter(word => word !== \"\");\r\n      setWordCount(words.length);\r\n\r\n      // Track most recent words for animation\r\n      if (words.length > 0) {\r\n        const lastWord = words[words.length - 1];\r\n        if (lastWord && lastWord.length > 0) {\r\n          setRecentWords((prev: string[]) => {\r\n            const newWords = [...prev, lastWord];\r\n            return newWords.slice(-5); // Keep only the last 5 words\r\n          });\r\n        }\r\n      }\r\n\r\n      // Set speaking state when new words are detected\r\n      if (isListening) {\r\n        setSpeaking(true);\r\n\r\n        // Clear previous timer if it exists\r\n        if (speakingTimerRef.current) {\r\n          clearTimeout(speakingTimerRef.current);\r\n        }\r\n\r\n        // Set a timer to detect when speaking has paused\r\n        speakingTimerRef.current = setTimeout(() => {\r\n          setSpeaking(false);\r\n        }, 1500); // 1.5 seconds of silence is considered a pause\r\n      }\r\n    }\r\n  }, [transcript, isListening]);\r\n\r\n  // Update input text when transcript changes\r\n  useEffect(() => {\r\n    // Always update with the latest transcript when listening\r\n    if (listening || isListening) {\r\n      setInputText(transcript);\r\n      setUserQuery(transcript);\r\n\r\n      // Also update the edited transcript to match the current transcript\r\n      // This ensures that when we switch to edit mode, we have the latest transcript\r\n      setEditedTranscript(transcript);\r\n\r\n      console.log(\"Speech recognized:\", transcript);\r\n\r\n      if (transcript && !isListening) {\r\n        console.log(\"Got transcript but isListening was false. Fixing state...\");\r\n        setIsListening(true);\r\n      }\r\n    }\r\n  }, [transcript, listening, isListening, setUserQuery]);\r\n\r\n  // Separate effect to handle listening state changes\r\n  useEffect(() => {\r\n    if (isListening) {\r\n      if (inputText !== \"\") {\r\n        setInputText(\"\");\r\n        setUserQuery(\"\");\r\n      }\r\n    } else {\r\n      console.log(\"Stopped listening, transcript:\", transcript);\r\n\r\n      if (transcript && transcript.trim() !== \"\") {\r\n        setInputText(transcript);\r\n        setUserQuery(transcript);\r\n      } else if (typeof window !== 'undefined') {\r\n        resetTranscript();\r\n      }\r\n\r\n      // Reset speaking state and clear any timers\r\n      setSpeaking(false);\r\n      if (speakingTimerRef.current) {\r\n        clearTimeout(speakingTimerRef.current);\r\n        speakingTimerRef.current = null;\r\n      }\r\n\r\n      // No longer need to close language dropdown\r\n      // setShowLanguageDropdown(false);\r\n    }\r\n\r\n    console.log(\"Listening state changed:\", isListening);\r\n  }, [isListening, transcript, inputText]);\r\n\r\n  // Update listening state when speech recognition status changes\r\n  useEffect(() => {\r\n    if (typeof window === 'undefined') return; // Skip on server-side\r\n\r\n    console.log(\"Speech recognition listening state changed:\", listening);\r\n\r\n    if (isListening !== listening) {\r\n      setIsListening(listening);\r\n    }\r\n\r\n    if (!listening && isListening) {\r\n      console.warn(\"Speech recognition stopped unexpectedly. Checking if we should restart...\");\r\n\r\n      // Only attempt to restart if we're still supposed to be listening\r\n      // This prevents infinite restart loops\r\n      const restartTimeout = setTimeout(() => {\r\n        if (isListening && typeof window !== 'undefined') {\r\n          console.log(\"Attempting to restart speech recognition...\");\r\n\r\n          // Try to restart speech recognition\r\n          const languageCode = getLanguageCode();\r\n          SpeechRecognition.startListening({\r\n            continuous: true,\r\n            language: languageCode,\r\n            interimResults: true\r\n          });\r\n        }\r\n      }, 1000);\r\n\r\n      return () => clearTimeout(restartTimeout);\r\n    }\r\n  }, [listening, isListening]);\r\n\r\n  // Effect to handle language changes\r\n  useEffect(() => {\r\n    if (typeof window === 'undefined') return; // Skip on server-side\r\n\r\n    console.log(`Language changed to: ${selectedLanguage}`);\r\n    // This effect can be used to update any UI elements when language changes\r\n    // The placeholder text and suggestions are already handled by the render logic\r\n\r\n    // If we're listening, we need to restart with the new language\r\n    if (isListening) {\r\n      const restartWithNewLanguage = async () => {\r\n        try {\r\n          await SpeechRecognition.stopListening();\r\n          await new Promise(resolve => setTimeout(resolve, 300));\r\n\r\n          const languageCode = getLanguageCode();\r\n          await SpeechRecognition.startListening({\r\n            continuous: true,\r\n            language: languageCode,\r\n            interimResults: true\r\n          });\r\n\r\n          console.log(`Restarted speech recognition with language: ${selectedLanguage} (${languageCode})`);\r\n        } catch (error) {\r\n          console.error(\"Error restarting speech recognition with new language:\", error);\r\n        }\r\n      };\r\n\r\n      restartWithNewLanguage();\r\n    }\r\n  }, [selectedLanguage]);\r\n\r\n  // Effect to hide index confirmation after 5 seconds\r\n  useEffect(() => {\r\n    let timer: NodeJS.Timeout;\r\n    if (showIndexConfirmation) {\r\n      timer = setTimeout(() => {\r\n        setShowIndexConfirmation(false);\r\n      }, 5000);\r\n    }\r\n    return () => {\r\n      if (timer) clearTimeout(timer);\r\n    };\r\n  }, [showIndexConfirmation]);\r\n\r\n  // Sample recommended suggestions\r\n  const englishSuggestions = [\r\n    \"How does the new tax regime (post-2023) compare with the old tax regime in terms of benefits for salaried individuals?\",\r\n    \"How has the rise of UPI (Unified Payments Interface) transformed retail banking and cashless transactions in India?\",\r\n    \"What factors should a retail investor in India consider before investing in IPOs?\",\r\n    \"How effective has the Pradhan Mantri Jan Dhan Yojana (PMJDY) been in achieving financial inclusion in rural India?\",\r\n    \"How are fintech startups like Zerodha and Groww changing the investment landscape for young Indians?\"\r\n  ];\r\n\r\n  // Tamil financial suggestions\r\n  const tamilSuggestions = [\r\n    \"இந்தியாவில் டிஜிட்டல் வாலட் மற்றும் மொபைல் பேமெண்ட் பயன்பாடுகள் நிதி சேவைகளை அணுகுவதை எவ்வாறு மாற்றியுள்ளன?\",\r\n    \"நீண்ட கால ஓய்வூதிய திட்டங்களில் முதலீடு செய்வதற்கான சிறந்த வழிகள் என்ன மற்றும் அவற்றின் வரி நன்மைகள் என்ன?\",\r\n    \"சிறு மற்றும் நடுத்தர தொழில்களுக்கு (SMEs) இந்தியாவில் கிடைக்கும் நிதி ஆதரவு திட்டங்கள் என்னென்ன?\",\r\n    \"பங்குச் சந்தை முதலீட்டிற்கும் தங்கம் மற்றும் நிலம் போன்ற பாரம்பரிய முதலீடுகளுக்கும் இடையே உள்ள முக்கிய வேறுபாடுகள் என்ன?\",\r\n    \"இந்தியாவில் கிரிப்டோகரன்சி மற்றும் டிஜிட்டல் சொத்துக்களுக்கான தற்போதைய ஒழுங்குமுறை நிலைப்பாடு என்ன?\"\r\n  ];\r\n\r\n  // Telugu financial suggestions\r\n  const teluguSuggestions = [\r\n    \"భారతదేశంలో మ్యూచువల్ ఫండ్స్ లో పెట్టుబడి పెట్టడానికి ఉత్తమ వ్యూహాలు ఏమిటి మరియు వాటి ప్రయోజనాలు ఏమిటి?\",\r\n    \"వ్యక్తిగత ఆర్థిక ప్రణాళిక కోసం డిజిటల్ టూల్స్ మరియు యాప్‌లు ఎలా ఉపయోగపడతాయి?\",\r\n    \"భారతదేశంలో స్టార్టప్‌లకు వెంచర్ క్యాపిటల్ మరియు ఏంజెల్ ఇన్వెస్టర్‌ల నుండి నిధులు సేకరించడం ఎలా?\",\r\n    \"రియల్ ఎస్టేట్ పెట్టుబడులకు REIT (రియల్ ఎస్టేట్ ఇన్వెస్ట్‌మెంట్ ట్రస్ట్‌లు) ఎలా ప్రత్యామ్నాయంగా పనిచేస్తాయి?\",\r\n    \"భారతదేశంలో ఫినాన్షియల్ లిటరసీని మెరుగుపరచడానికి ప్రభుత్వం మరియు ప్రైవేట్ రంగం తీసుకుంటున్న చర్యలు ఏమిటి?\"\r\n  ];\r\n\r\n  // Kannada financial suggestions\r\n  const kannadaSuggestions = [\r\n    \"ಭಾರತದಲ್ಲಿ ಸಣ್ಣ ಉಳಿತಾಯ ಯೋಜನೆಗಳು ಮತ್ತು ಸರ್ಕಾರಿ ಬಾಂಡ್‌ಗಳಲ್ಲಿ ಹೂಡಿಕೆ ಮಾಡುವುದರ ಪ್ರಯೋಜನಗಳು ಯಾವುವು?\",\r\n    \"ಹಣದುಬ್ಬರದ ಸಮಯದಲ್ಲಿ ಹಣಕಾಸು ಸ್ಥಿರತೆಯನ್ನು ಕಾಪಾಡಿಕೊಳ್ಳಲು ಉತ್ತಮ ಆರ್ಥಿಕ ತಂತ್ರಗಳು ಯಾವುವು?\",\r\n    \"ಭಾರತದಲ್ಲಿ ಸ್ವಯಂ ಉದ್ಯೋಗಿಗಳು ಮತ್ತು ಫ್ರೀಲ್ಯಾನ್ಸರ್‌ಗಳಿಗೆ ಲಭ್ಯವಿರುವ ತೆರಿಗೆ ಯೋಜನೆ ಮತ್ತು ಹಣಕಾಸು ಸಾಧನಗಳು ಯಾವುವು?\",\r\n    \"ಭಾರತದಲ್ಲಿ ಹೊಸ ಪೆನ್ಷನ್ ಯೋಜನೆ (NPS) ಮತ್ತು ಅಟಲ್ ಪೆನ್ಷನ್ ಯೋಜನೆ (APY) ನಡುವಿನ ವ್ಯತ್ಯಾಸಗಳು ಯಾವುವು?\",\r\n    \"ಭಾರತದಲ್ಲಿ ಮಹಿಳಾ ಉದ್ಯಮಿಗಳಿಗೆ ಲಭ್ಯವಿರುವ ವಿಶೇಷ ಹಣಕಾಸು ಯೋಜನೆಗಳು ಮತ್ತು ಸಾಲ ಕಾರ್ಯಕ್ರಮಗಳು ಯಾವುವು?\"\r\n  ];\r\n\r\n  // Get suggestions based on selected language\r\n  const getSuggestionsByLanguage = () => {\r\n    switch(selectedLanguage) {\r\n      case \"Tamil\":\r\n        return tamilSuggestions;\r\n      case \"Telugu\":\r\n        return teluguSuggestions;\r\n      case \"Kannada\":\r\n        return kannadaSuggestions;\r\n      default:\r\n        return englishSuggestions;\r\n    }\r\n  };\r\n\r\n  const recommendedSuggestions = getSuggestionsByLanguage();\r\n\r\n  // Extract chat ID from URL\r\n  const chatIdUrl = path && path.includes(\"/chat/\") ? path.split(\"/chat/\")[1] : \"\";\r\n\r\n  // Handle selecting a suggestion\r\n  const handleSelectSuggestion = (suggestion: string) => {\r\n    setInputText(suggestion);\r\n    setUserQuery(suggestion);\r\n    setShowSuggestions(false);\r\n  };\r\n\r\n  // Function to detect if text is likely Tamil\r\n  const isTamilText = (text: string): boolean => {\r\n    // Tamil Unicode range: \\u0B80-\\u0BFF\r\n    const tamilRegex = /[\\u0B80-\\u0BFF]/;\r\n    return tamilRegex.test(text);\r\n  };\r\n\r\n  // Function to detect if text is likely Telugu\r\n  const isTeluguText = (text: string): boolean => {\r\n    // Telugu Unicode range: \\u0C00-\\u0C7F\r\n    const teluguRegex = /[\\u0C00-\\u0C7F]/;\r\n    return teluguRegex.test(text);\r\n  };\r\n\r\n  // Function to detect if text is likely Kannada\r\n  const isKannadaText = (text: string): boolean => {\r\n    // Kannada Unicode range: \\u0C80-\\u0CFF\r\n    const kannadaRegex = /[\\u0C80-\\u0CFF]/;\r\n    return kannadaRegex.test(text);\r\n  };\r\n\r\n  // Function to validate if text matches the selected language\r\n  const validateLanguageMatch = (text: string, language: string): boolean => {\r\n    if (!text || text.trim() === \"\") return true; // Empty text is valid for any language\r\n\r\n    // First, remove continuous capital English words (acronyms, proper nouns, etc.)\r\n    // These should be preserved in any language and not affect validation\r\n    const textWithoutCapitalWords = text.replace(/\\b[A-Z]{2,}\\b/g, '');\r\n\r\n    // Check if the remaining text contains characters from different languages\r\n    const hasTamilChars = isTamilText(textWithoutCapitalWords);\r\n    const hasTeluguChars = isTeluguText(textWithoutCapitalWords);\r\n    const hasKannadaChars = isKannadaText(textWithoutCapitalWords);\r\n\r\n    // Count how many different language scripts are present\r\n    const scriptCount = (hasTamilChars ? 1 : 0) + (hasTeluguChars ? 1 : 0) + (hasKannadaChars ? 1 : 0);\r\n\r\n    // If there are multiple scripts, it's likely a mismatch\r\n    if (scriptCount > 1) {\r\n      return false;\r\n    }\r\n\r\n    // English can contain any characters, so we only validate non-English languages\r\n    if (language === \"English\") {\r\n      // For English, we consider it valid if it doesn't contain Tamil, Telugu, or Kannada characters\r\n      return !(hasTamilChars || hasTeluguChars || hasKannadaChars);\r\n    } else if (language === \"Tamil\") {\r\n      // For Tamil, it should contain Tamil characters\r\n      return hasTamilChars || textWithoutCapitalWords.trim() === '';\r\n    } else if (language === \"Telugu\") {\r\n      // For Telugu, it should contain Telugu characters\r\n      return hasTeluguChars || textWithoutCapitalWords.trim() === '';\r\n    } else if (language === \"Kannada\") {\r\n      // For Kannada, it should contain Kannada characters\r\n      return hasKannadaChars || textWithoutCapitalWords.trim() === '';\r\n    }\r\n\r\n    return true; // Default case\r\n  };\r\n\r\n\r\n  // Function to get language-specific text for uploaded content display\r\n  const getUploadDisplayText = () => {\r\n    switch(selectedLanguage) {\r\n      case \"Tamil\":\r\n        return {\r\n          uploadedFiles: 'பதிவேற்றப்பட்ட கோப்புகள்',\r\n          uploadedUrls: 'பதிவேற்றப்பட்ட இணைப்புகள்',\r\n          removeFile: 'கோப்பை அகற்று',\r\n          removeUrl: 'இணைப்பை அகற்று',\r\n          pdfDocument: 'PDF ஆவணம்',\r\n          mp3Audio: 'MP3 ஆடியோ',\r\n          youtubeVideo: 'YouTube வீடியோ',\r\n          articleLink: 'கட்டுரை இணைப்பு'\r\n        };\r\n      case \"Telugu\":\r\n        return {\r\n          uploadedFiles: 'అప్‌లోడ్ చేసిన ఫైల్‌లు',\r\n          uploadedUrls: 'అప్‌లోడ్ చేసిన లింక్‌లు',\r\n          removeFile: 'ఫైల్‌ను తొలగించండి',\r\n          removeUrl: 'లింక్‌ను తొలగించండి',\r\n          pdfDocument: 'PDF డాక్యుమెంట్',\r\n          mp3Audio: 'MP3 ఆడియో',\r\n          youtubeVideo: 'YouTube వీడియో',\r\n          articleLink: 'వ్యాసం లింక్'\r\n        };\r\n      case \"Kannada\":\r\n        return {\r\n          uploadedFiles: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಫೈಲ್‌ಗಳು',\r\n          uploadedUrls: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಲಿಂಕ್‌ಗಳು',\r\n          removeFile: 'ಫೈಲ್ ತೆಗೆದುಹಾಕಿ',\r\n          removeUrl: 'ಲಿಂಕ್ ತೆಗೆದುಹಾಕಿ',\r\n          pdfDocument: 'PDF ಡಾಕ್ಯುಮೆಂಟ್',\r\n          mp3Audio: 'MP3 ಆಡಿಯೋ',\r\n          youtubeVideo: 'YouTube ವೀಡಿಯೊ',\r\n          articleLink: 'ಲೇಖನ ಲಿಂಕ್'\r\n        };\r\n      default:\r\n        return {\r\n          uploadedFiles: 'Uploaded Files',\r\n          uploadedUrls: 'Uploaded URLs',\r\n          removeFile: 'Remove file',\r\n          removeUrl: 'Remove URL',\r\n          pdfDocument: 'PDF Document',\r\n          mp3Audio: 'MP3 Audio',\r\n          youtubeVideo: 'YouTube Video',\r\n          articleLink: 'Article Link'\r\n        };\r\n    }\r\n  };\r\n\r\n  // Function to get file icon based on file type\r\n  const getFileIcon = (fileName: string) => {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n\r\n    // Document files\r\n    if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension || '')) {\r\n      return <PiFileText className=\"w-5 h-5\" />;\r\n    }\r\n\r\n    // Audio files\r\n    if (['mp3', 'wav', 'm4a', 'aac', 'flac', 'ogg', 'wma'].includes(extension || '')) {\r\n      return <PiMusicNote className=\"w-5 h-5\" />;\r\n    }\r\n\r\n    // Default file icon\r\n    return <PiFile className=\"w-5 h-5\" />;\r\n  };\r\n\r\n  // Function to get URL icon based on type\r\n  const getUrlIcon = (type: 'youtube' | 'article') => {\r\n    if (type === 'youtube') {\r\n      return <PiYoutubeLogo className=\"w-5 h-5\" />;\r\n    }\r\n    return <PiLink className=\"w-5 h-5\" />;\r\n  };\r\n\r\n  // Function to remove uploaded file\r\n  const removeUploadedFile = (index: number) => {\r\n    setUploadedFiles(prev => prev.filter((_, i) => i !== index));\r\n    if (uploadedFiles.length === 1 && uploadedURLs.length === 0) {\r\n      setShowUploadedContent(false);\r\n    }\r\n  };\r\n\r\n  // Function to remove uploaded URL\r\n  const removeUploadedURL = (index: number) => {\r\n    setUploadedURLs(prev => prev.filter((_, i) => i !== index));\r\n    if (uploadedURLs.length === 1 && uploadedFiles.length === 0) {\r\n      setShowUploadedContent(false);\r\n    }\r\n  };\r\n\r\n  // Handle selecting a language for voice input and UI\r\n  const handleSelectLanguage = async (e: React.MouseEvent, language: string) => {\r\n    // Prevent the event from bubbling up and triggering form submission\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n\r\n    setSelectedLanguage(language);\r\n    // Close language menu when a language is selected\r\n    setShowLanguageMenu(false);\r\n\r\n    // Clear any language error when the user changes the language\r\n    if (languageError) {\r\n      setLanguageError(null);\r\n    }\r\n\r\n    // Update placeholder and UI based on language\r\n    console.log(`Language set to: ${language}`);\r\n\r\n    // Notify parent component about language change if callback is provided\r\n    if (onLanguageChange) {\r\n      onLanguageChange(language);\r\n    }\r\n\r\n    // If suggestions are showing, close and reopen to refresh the content\r\n    if (showSuggestions) {\r\n      setShowSuggestions(false);\r\n      setTimeout(() => setShowSuggestions(true), 100);\r\n    }\r\n\r\n    // If currently listening, restart with new language\r\n    if (isListening && typeof window !== 'undefined') {\r\n      try {\r\n        await SpeechRecognition.stopListening();\r\n        console.log(\"Speech recognition stopped for language change\");\r\n\r\n        await new Promise(resolve => setTimeout(resolve, 300));\r\n        await startListening();\r\n      } catch (error) {\r\n        console.error(\"Error changing speech recognition language:\", error);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Toggle voice recognition\r\n  const toggleListening = async () => {\r\n    // Skip on server-side\r\n    if (typeof window === 'undefined') {\r\n      console.warn(\"Attempted to toggle listening on server-side\");\r\n      return;\r\n    }\r\n\r\n    if (isListening) {\r\n      try {\r\n        await SpeechRecognition.stopListening();\r\n        setIsListening(false); // Explicitly set to false to ensure state is updated\r\n\r\n        // Don't reset transcript when stopping, so it can be edited\r\n        // resetTranscript();\r\n\r\n        console.log(\"Speech recognition stopped successfully\");\r\n      } catch (error) {\r\n        console.error(\"Error stopping speech recognition:\", error);\r\n      }\r\n    } else {\r\n      await startListening();\r\n    }\r\n  };\r\n\r\n  // Toggle between edit and view modes for the transcript\r\n  const toggleTranscriptEditMode = () => {\r\n    if (isEditingTranscript) {\r\n      // Save the edited transcript\r\n      if (editedTranscript.trim() !== \"\") {\r\n        setInputText(editedTranscript);\r\n        setUserQuery(editedTranscript);\r\n      }\r\n      setIsEditingTranscript(false);\r\n    } else {\r\n      // Enter edit mode\r\n      setIsEditingTranscript(true);\r\n\r\n      // Focus the editable textarea after a short delay to ensure it's rendered\r\n      setTimeout(() => {\r\n        if (editableTranscriptRef.current) {\r\n          editableTranscriptRef.current.focus();\r\n        }\r\n      }, 50);\r\n    }\r\n  };\r\n\r\n  // Handle changes to the editable transcript\r\n  const handleTranscriptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    setEditedTranscript(e.target.value);\r\n\r\n    // Update word count\r\n    const words = e.target.value.trim().split(/\\s+/).filter(word => word !== \"\");\r\n    setWordCount(words.length);\r\n\r\n    // Clear any language error when the user edits the transcript\r\n    if (languageError) {\r\n      setLanguageError(null);\r\n    }\r\n  };\r\n\r\n  // Start listening with the selected language\r\n  const startListening = async () => {\r\n    // Skip on server-side\r\n    if (typeof window === 'undefined') {\r\n      console.warn(\"Attempted to start listening on server-side\");\r\n      return;\r\n    }\r\n\r\n    // Only reset transcript if we're starting fresh\r\n    // If we have edited transcript, keep it\r\n    if (!editedTranscript || editedTranscript.trim() === \"\") {\r\n      resetTranscript();\r\n      setInputText(\"\");\r\n      setUserQuery(\"\");\r\n      setWordCount(0);\r\n      setRecentWords([]);\r\n    }\r\n\r\n    const languageCode = getLanguageCode();\r\n    console.log(`Starting speech recognition in ${selectedLanguage} (${languageCode})`);\r\n\r\n    try {\r\n      setIsListening(true);\r\n\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if (!browserSupportsSpeechRecognition) {\r\n        console.error(\"Browser doesn't support speech recognition\");\r\n        alert(\"Your browser doesn't support speech recognition. Please try using Chrome.\");\r\n        setIsListening(false);\r\n        return;\r\n      }\r\n\r\n      if (!isMicrophoneAvailable) {\r\n        console.error(\"Microphone is not available\");\r\n        alert(\"Microphone is not available. Please check your microphone permissions.\");\r\n        setIsListening(false);\r\n        return;\r\n      }\r\n\r\n      await SpeechRecognition.startListening({\r\n        continuous: true,\r\n        language: languageCode,\r\n        interimResults: true\r\n      });\r\n\r\n      console.log(\"Speech recognition started successfully\");\r\n\r\n      setTimeout(() => {\r\n        if (!listening && typeof window !== 'undefined') {\r\n          console.warn(\"Speech recognition may not have started properly. Trying again...\");\r\n          SpeechRecognition.startListening({\r\n            continuous: true,\r\n            language: languageCode,\r\n            interimResults: true\r\n          });\r\n        }\r\n      }, 500);\r\n\r\n    } catch (error) {\r\n      console.error(\"Error starting speech recognition:\", error);\r\n      setIsListening(false);\r\n      alert(\"There was an error starting speech recognition. Please try again.\");\r\n    }\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  const handleSendMessage = async (e: FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // If we're in edit mode, use the edited transcript\r\n    // Otherwise, use the transcript if listening, or the input text\r\n    let textToSend = isEditingTranscript ? editedTranscript : (isListening ? transcript : inputText);\r\n\r\n    // If textToSend is empty but we have a related question scenario, \r\n    // check the actual input element value as a fallback\r\n    if (!textToSend || !textToSend.trim()) {\r\n      const inputElement = document.querySelector('input.w-full.outline-none.p-4.pr-12.bg-transparent') as HTMLInputElement;\r\n      if (inputElement && inputElement.value && inputElement.value.trim()) {\r\n        console.log(\"🎯 ChatBox: Using input element value as fallback:\", inputElement.value);\r\n        textToSend = inputElement.value;\r\n      }\r\n    }\r\n\r\n    if (!textToSend || !textToSend.trim()) {\r\n      console.log(\"❌ ChatBox: No text to send\");\r\n      return;\r\n    }\r\n\r\n    console.log(\"🎯 ChatBox: Sending message with text:\", textToSend);\r\n\r\n    // Call the internal send function with the text\r\n    await sendMessageInternal(textToSend);\r\n  };\r\n\r\n  // Internal function to send a message with a specific text\r\n  const sendMessageInternal = async (textToSend: string) => {\r\n    // Validate that the input text matches the selected language\r\n    if (!validateLanguageMatch(textToSend, selectedLanguage)) {\r\n      setLanguageError(\"Please select the proper language or type in the currently selected language.\");\r\n      return;\r\n    }\r\n\r\n    // Clear any previous language errors\r\n    setLanguageError(null);\r\n\r\n    // If we're in edit mode, exit edit mode\r\n    if (isEditingTranscript) {\r\n      setIsEditingTranscript(false);\r\n    }\r\n\r\n    if (isListening && typeof window !== 'undefined') {\r\n      SpeechRecognition.stopListening();\r\n      setIsListening(false); // Explicitly set to false\r\n      setSpeaking(false); // Reset speaking state\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n    }\r\n\r\n    // Close any open dropdowns/popups\r\n    setShowLanguageMenu(false);\r\n    setShowSuggestions(false);\r\n\r\n    // Disable language buttons during query processing\r\n    setLanguageButtonsDisabled(true);\r\n\r\n    // Mark that user has made their first request and persist it\r\n    setHasUserMadeFirstRequest(true);\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('hasUserMadeFirstRequest', 'true');\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    const currentChatId = chatIdUrl || uuidv4();\r\n\r\n    if (!chatIdUrl) {\r\n      router.push(`/chat/${currentChatId}`);\r\n    }\r\n\r\n    const queryText = textToSend.trim();\r\n\r\n    // Determine the language of the query\r\n    const isTamil = isTamilText(queryText) || selectedLanguage === \"Tamil\";\r\n    const isTelugu = isTeluguText(queryText) || selectedLanguage === \"Telugu\";\r\n    const isKannada = isKannadaText(queryText) || selectedLanguage === \"Kannada\";\r\n\r\n    // Log language detection for Tamil support\r\n    if (isTamil) {\r\n      console.log(`🌏 Tamil language detected - Selected: ${selectedLanguage}, Text detection: ${isTamilText(queryText)}`);\r\n    }\r\n\r\n    // Store the original query for display purposes (commented out as it's not currently used)\r\n    // const originalQuery = queryText;\r\n    let translatedQuery = queryText;\r\n    let needsTranslation = false;\r\n\r\n    // Use the financial_query endpoint for all languages\r\n    // For Tamil, Telugu, and Kannada, we'll translate the query and response\r\n\r\n    // Extract continuous capital English words that should not be translated\r\n    const capitalWordsMatches = queryText.match(/\\b[A-Z]{2,}\\b/g) || [];\r\n    const capitalWords = capitalWordsMatches.map((word: string) => ({\r\n      word,\r\n      placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`\r\n    }));\r\n\r\n    // Create a version of the query with placeholders for capital words\r\n    let queryWithPlaceholders = queryText;\r\n    capitalWords.forEach((item: { word: string, placeholder: string }) => {\r\n      queryWithPlaceholders = queryWithPlaceholders.replace(item.word, item.placeholder);\r\n    });\r\n\r\n    if (isTamil) {\r\n      // For Tamil, we need to translate the query to English before sending\r\n      needsTranslation = true;\r\n\r\n      // Translate Tamil to English\r\n      try {\r\n        console.log(`Translating Tamil query to English (preserving capital words): \"${queryWithPlaceholders}\"`);\r\n        translatedQuery = await TranslationService.translateText(queryWithPlaceholders, \"ta\", \"en\");\r\n\r\n        // After translation, restore the capital words\r\n        capitalWords.forEach(item => {\r\n          translatedQuery = translatedQuery.replace(item.placeholder, item.word);\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Error translating Tamil query:\", error);\r\n      }\r\n    } else if (isTelugu) {\r\n      // For Telugu, we need to translate the query to English before sending\r\n      needsTranslation = true;\r\n\r\n      // Translate Telugu to English\r\n      try {\r\n        console.log(`Translating Telugu query to English (preserving capital words): \"${queryWithPlaceholders}\"`);\r\n        translatedQuery = await TranslationService.translateText(queryWithPlaceholders, \"te\", \"en\");\r\n\r\n        // After translation, restore the capital words\r\n        capitalWords.forEach(item => {\r\n          translatedQuery = translatedQuery.replace(item.placeholder, item.word);\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Error translating Telugu query:\", error);\r\n      }\r\n    } else if (isKannada) {\r\n      // For Kannada, we need to translate the query to English before sending\r\n      needsTranslation = true;\r\n\r\n      // Translate Kannada to English\r\n      try {\r\n        console.log(`Translating Kannada query to English (preserving capital words): \"${queryWithPlaceholders}\"`);\r\n        translatedQuery = await TranslationService.translateText(queryWithPlaceholders, \"kn\", \"en\");\r\n\r\n        // After translation, restore the capital words\r\n        capitalWords.forEach(item => {\r\n          translatedQuery = translatedQuery.replace(item.placeholder, item.word);\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Error translating Kannada query:\", error);\r\n      }\r\n    }\r\n\r\n    console.log(`Query detected as ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : isKannada ? 'Kannada' : 'English'}`);\r\n\r\n    setInputText(\"\");\r\n    setUserQuery(\"\");\r\n    resetTranscript();\r\n    setWordCount(0);\r\n    setRecentWords([]);\r\n\r\n    // Note: Uploaded content is intentionally NOT cleared here to allow users to send multiple messages\r\n    // with the same uploaded files/URLs. Content will only be cleared when explicitly removed by user\r\n    // or when starting a new chat session.\r\n\r\n    const userMessageTimestamp = new Date().toISOString();\r\n\r\n    // Convert uploaded files to the format expected by the Message interface\r\n    const messageUploadedFiles = uploadedFiles.map(file => ({\r\n      name: file.name,\r\n      size: file.size,\r\n      type: file.type,\r\n      lastModified: file.lastModified\r\n    }));\r\n\r\n    addMessage({\r\n      isUser: true,\r\n      text: queryText,\r\n      timestamp: userMessageTimestamp,\r\n      uploadedFiles: messageUploadedFiles.length > 0 ? messageUploadedFiles : undefined,\r\n      uploadedURLs: uploadedURLs.length > 0 ? uploadedURLs : undefined\r\n    }, currentChatId);\r\n\r\n    const loadingMessageTimestamp = new Date().toISOString();\r\n    const loadingMessageId = `loading-${currentChatId}-${Date.now()}`;\r\n\r\n    addMessage({\r\n      isUser: false,\r\n      text: \"__LOADING__\",\r\n      timestamp: loadingMessageTimestamp,\r\n      messageId: loadingMessageId\r\n    }, currentChatId);\r\n\r\n    try {\r\n      // Use the translated query for Telugu, otherwise use the original query\r\n      const queryToSend = needsTranslation ? translatedQuery : queryText;\r\n\r\n      // Get FAISS configuration from localStorage if available\r\n      const faissIndexName = typeof window !== 'undefined' ? localStorage.getItem('faiss_index_name') : null;\r\n      const faissEmbedModel = typeof window !== 'undefined' ? localStorage.getItem('faiss_embed_model') : null;\r\n      const faissClientEmail = typeof window !== 'undefined' ? localStorage.getItem('faiss_client_email') : null;\r\n\r\n      // Prepare request body with all available data including language preference\r\n      const requestBody: any = {\r\n        query: queryToSend,\r\n        language: selectedLanguage  // Add language preference for Tamil support\r\n      };\r\n\r\n      // Add client email if available\r\n      if (userEmail) {\r\n        requestBody.client_email = userEmail;\r\n        requestBody.user_email = userEmail; // Also add for direct FAISS query validation\r\n        console.log(`Including user email in request: ${userEmail}`);\r\n      }\r\n\r\n      // Determine which index to use for FAISS\r\n      const indexToUse = selectedIndex || faissIndexName;\r\n      console.log(`🎯 Selected index from UI: ${selectedIndex}`);\r\n      console.log(`💾 Stored index from localStorage: ${faissIndexName}`);\r\n      console.log(`📌 Final index to use: ${indexToUse}`);\r\n\r\n      // Add context about uploaded content if any\r\n      if (uploadedFiles.length > 0 || uploadedURLs.length > 0) {\r\n        const uploadContext = [];\r\n\r\n        if (uploadedFiles.length > 0) {\r\n          uploadContext.push(`Recently uploaded files: ${uploadedFiles.map(f => f.name).join(', ')}`);\r\n        }\r\n\r\n        if (uploadedURLs.length > 0) {\r\n          uploadContext.push(`Recently processed URLs: ${uploadedURLs.map(u => u.url).join(', ')}`);\r\n        }\r\n\r\n        requestBody.upload_context = uploadContext.join('. ');\r\n        requestBody.has_recent_uploads = true;\r\n        console.log(`📎 Including upload context: ${requestBody.upload_context}`);\r\n      }\r\n\r\n      // For FAISS, we don't need API keys, just the index name\r\n      if (indexToUse) {\r\n        requestBody.index_name = indexToUse;\r\n        console.log(`✅ SENDING REQUEST WITH - Selected FAISS Index: \"${indexToUse}\"`);\r\n      }\r\n      // If no index selected, fall back to default index\r\n      else {\r\n        requestBody.index_name = 'default';\r\n        console.log(`🔁 FALLING BACK TO DEFAULT INDEX - Using: \"default\"`);\r\n        console.log(`💡 Note: Default index is accessible to all users`);\r\n      }\r\n\r\n      // Detect query language for better caching and translation\r\n      const queryLanguage = TranslationService.detectLanguage(queryToSend);\r\n      const targetLanguage = selectedLanguage === 'Tamil' ? 'ta' :\r\n                           selectedLanguage === 'Telugu' ? 'te' :\r\n                           selectedLanguage === 'Kannada' ? 'kn' : 'en';\r\n\r\n      // 🚀 CACHE-FIRST APPROACH: Check cache before making API call with language support\r\n      const cacheContext = `${requestBody.index_name || 'default'}|${userEmail || 'anonymous'}|${requestBody.upload_context || ''}`;\r\n      const cachedResponse = await CacheService.getCachedResponseWithDelay(queryToSend, cacheContext, targetLanguage);\r\n\r\n      let data;\r\n      if (cachedResponse) {\r\n        // Use cached response (delay already applied by getCachedResponseWithDelay)\r\n        console.log(`⚡ Using cached response for query: \"${queryToSend.substring(0, 50)}...\" (Language: ${targetLanguage})`);\r\n\r\n        data = {\r\n          ai_response: cachedResponse.ai_response,\r\n          related_questions: cachedResponse.related_questions,\r\n          sentence_analysis: cachedResponse.sentence_analysis,\r\n          pinecone_indexes: cachedResponse.pinecone_indexes,\r\n          faiss_categories: cachedResponse.faiss_categories,\r\n          has_uploaded_content: cachedResponse.has_uploaded_content,\r\n          upload_sources: cachedResponse.upload_sources,\r\n          translation_applied: cachedResponse.translation_applied,\r\n          query_language: cachedResponse.query_language\r\n        };\r\n\r\n        console.log(`✅ Cached response ready with language support`);\r\n      } else {\r\n        // No cache hit - make API call\r\n        console.log(`🌐 Making API call for query: \"${queryToSend.substring(0, 50)}...\" (Language: ${selectedLanguage})`);\r\n\r\n        // Add translation parameters to request if needed\r\n        if (targetLanguage !== 'en') {\r\n          requestBody.target_language = targetLanguage;\r\n          requestBody.enable_translation = true;\r\n        }\r\n\r\n        data = await ApiService.sendQuery(requestBody);\r\n\r\n        // Apply client-side translation if backend translation wasn't applied\r\n        if (!data.translation_applied && targetLanguage !== 'en') {\r\n          console.log(`🌐 Applying client-side translation to ${targetLanguage}`);\r\n          data = await TranslationService.translateResponse(data, targetLanguage);\r\n        }\r\n\r\n        // Cache the response for future use with language context\r\n        CacheService.setCachedResponse(queryToSend, data, cacheContext, targetLanguage);\r\n      }\r\n      console.log(\"API Response received:\", data);\r\n\r\n      // Explicitly check for related_questions\r\n      if (data.related_questions) {\r\n        console.log(\"Found related_questions in API response:\", data.related_questions);\r\n      } else {\r\n        console.warn(\"No related_questions found in API response\");\r\n      }\r\n\r\n      // Check if the response includes a list of FAISS indexes/categories\r\n      if ((data as any).faiss_categories && Array.isArray((data as any).faiss_categories) && (data as any).faiss_categories.length > 0) {\r\n        console.log(\"Received FAISS categories from API:\", (data as any).faiss_categories);\r\n        // Update the dropdown options with the available categories\r\n        setPineconeIndexes((data as any).faiss_categories.map((cat: any) => cat.index_name || cat));\r\n      }\r\n\r\n      let aiResponse = data.ai_response;\r\n      console.log(\"Original AI Response:\", aiResponse);\r\n\r\n      // For Tamil, Telugu, or Kannada, translate the response from English\r\n      if ((isTamil || isTelugu || isKannada) && aiResponse) {\r\n        try {\r\n          // Extract continuous capital English words that should not be translated\r\n          const responseCapitalWordsMatches = aiResponse.match(/\\b[A-Z]{2,}\\b/g) || [];\r\n          const responseCapitalWords = responseCapitalWordsMatches.map((word: string) => ({\r\n            word,\r\n            placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`\r\n          }));\r\n\r\n          // Create a version of the response with placeholders for capital words\r\n          let responseWithPlaceholders = aiResponse;\r\n          responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {\r\n            responseWithPlaceholders = responseWithPlaceholders.replace(item.word, item.placeholder);\r\n          });\r\n\r\n          // Translate the response based on the detected language\r\n          if (isTamil) {\r\n            console.log(`Translating response from English to Tamil (preserving capital words): \"${responseWithPlaceholders.substring(0, 50)}...\"`);\r\n            const translatedResponse = await TranslationService.translateText(responseWithPlaceholders, \"en\", \"ta\");\r\n            aiResponse = translatedResponse;\r\n\r\n            // After translation, restore the capital words\r\n            responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {\r\n              aiResponse = aiResponse.replace(item.placeholder, item.word);\r\n            });\r\n          } else if (isTelugu) {\r\n            console.log(`Translating response from English to Telugu (preserving capital words): \"${responseWithPlaceholders.substring(0, 50)}...\"`);\r\n            const translatedResponse = await TranslationService.translateText(responseWithPlaceholders, \"en\", \"te\");\r\n            aiResponse = translatedResponse;\r\n\r\n            // After translation, restore the capital words\r\n            responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {\r\n              aiResponse = aiResponse.replace(item.placeholder, item.word);\r\n            });\r\n          } else if (isKannada) {\r\n            console.log(`Translating response from English to Kannada (preserving capital words): \"${responseWithPlaceholders.substring(0, 50)}...\"`);\r\n            const translatedResponse = await TranslationService.translateText(responseWithPlaceholders, \"en\", \"kn\");\r\n            aiResponse = translatedResponse;\r\n\r\n            // After translation, restore the capital words\r\n            responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {\r\n              aiResponse = aiResponse.replace(item.placeholder, item.word);\r\n            });\r\n          }\r\n        } catch (error) {\r\n          console.error(`Error translating response to ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}:`, error);\r\n        }\r\n      }\r\n\r\n      console.log(\"AI Response to display:\", aiResponse);\r\n\r\n      // Update response data sources state\r\n      const extendedData = data as any;\r\n      if (extendedData.has_uploaded_content !== undefined) {\r\n        setResponseHasUploadedContent(extendedData.has_uploaded_content);\r\n      }\r\n      if (extendedData.upload_sources && Array.isArray(extendedData.upload_sources)) {\r\n        setResponseUploadSources(extendedData.upload_sources);\r\n      }\r\n\r\n      if (data.sentence_analysis && Array.isArray(data.sentence_analysis)) {\r\n        console.log(\"Sentence analysis data:\", data.sentence_analysis);\r\n\r\n        // For Tamil, Telugu, or Kannada, we might want to translate the sentence analysis too\r\n        if (isTamil || isTelugu || isKannada) {\r\n          // In a real app, you would translate each sentence and summary\r\n          // This is just a placeholder for the actual implementation\r\n          console.log(`Would translate sentence analysis for ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'} here`);\r\n\r\n          // Example of how you might translate each item in a real implementation:\r\n          // for (let i = 0; i < data.sentence_analysis.length; i++) {\r\n          //   const item = data.sentence_analysis[i];\r\n          //   if (item.summary) {\r\n          //     item.summary = await translateText(item.summary, \"en\", isTamil ? \"ta\" : \"te\");\r\n          //   }\r\n          //   if (item.sentence) {\r\n          //     item.sentence = await translateText(item.sentence, \"en\", isTamil ? \"ta\" : \"te\");\r\n          //   }\r\n          // }\r\n        }\r\n\r\n        data.sentence_analysis.forEach((item: { sentence: string; url: string; summary?: string }, index: number) => {\r\n          const sentence = item.sentence;\r\n          const url = item.url;\r\n          const summary = item.summary || \"\";\r\n          console.log(`Sentence ${index + 1}: ${sentence}`);\r\n          console.log(`URL ${index + 1}: ${url}`);\r\n          console.log(`Summary ${index + 1}: ${summary}`);\r\n        });\r\n      } else {\r\n        console.log(\"No sentence_analysis data available.\");\r\n      }\r\n\r\n      // Log related questions if available\r\n      if (data.related_questions && Array.isArray(data.related_questions)) {\r\n        console.log(\"Related questions data:\", data.related_questions);\r\n\r\n        // For Tamil, Telugu, or Kannada, we might want to translate the related questions too\r\n        if (isTamil || isTelugu || isKannada) {\r\n          console.log(`Would translate related questions for ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'} here`);\r\n        }\r\n\r\n        data.related_questions.forEach((question: string, index: number) => {\r\n          console.log(`Related Question ${index + 1}: ${question}`);\r\n        });\r\n      } else {\r\n        console.log(\"No related_questions data available.\");\r\n      }\r\n\r\n      if (aiResponse === undefined || aiResponse === null) {\r\n        console.warn(\"API returned null/undefined ai_response\");\r\n        handleSubmit(queryText, currentChatId, \"Sorry, I couldn't process your request properly.\");\r\n        return;\r\n      }\r\n\r\n      // Create a properly structured response object with all fields\r\n      const responseObject = {\r\n        ai_response: aiResponse,\r\n        sentence_analysis: data.sentence_analysis || [],\r\n        related_questions: data.related_questions || []\r\n      };\r\n\r\n      // Explicitly log the related_questions in the response object\r\n      console.log(\"Related questions in response object:\", responseObject.related_questions);\r\n      console.log(\"Sending structured response to handleSubmit:\", responseObject);\r\n      handleSubmit(queryText, currentChatId, responseObject);\r\n    } catch (error) {\r\n      console.error(\"Error fetching AI response:\", error);\r\n\r\n      // Provide a more specific error message if it's related to the FAISS index\r\n      let errorMessage = \"I'm sorry, I couldn't process your request at the moment. Please try again later.\";\r\n\r\n      if (error instanceof Error) {\r\n        const errorText = error.message;\r\n\r\n        // Check if the error is related to the FAISS index\r\n        if (errorText.includes(\"index\") || errorText.includes(\"Index\") || errorText.includes(\"FAISS\")) {\r\n          errorMessage = errorText;\r\n          console.log(\"Index-related error detected:\", errorText);\r\n        }\r\n      }\r\n\r\n      handleSubmit(queryText, currentChatId, errorMessage);\r\n    } finally {\r\n      setIsLoading(false);\r\n      // Re-enable language buttons once the response is received or if there's an error\r\n      setLanguageButtonsDisabled(false);\r\n    }\r\n  };\r\n\r\n  // Initialize client-side values after mount to prevent hydration errors\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      // Update state with client-side values\r\n      setTranscript(clientTranscript);\r\n      setListening(clientListening);\r\n      setBrowserSupportsSpeechRecognition(clientBrowserSupport);\r\n      setIsMicrophoneAvailable(clientMicrophoneAvailable);\r\n\r\n      // Clean up expired cache entries on component mount\r\n      CacheService.cleanupExpiredCache();\r\n\r\n      console.log(\"SpeechRecognition API check:\", {\r\n        webkitSpeechRecognition: 'webkitSpeechRecognition' in window,\r\n        SpeechRecognition: 'SpeechRecognition' in window,\r\n        mozSpeechRecognition: 'mozSpeechRecognition' in window,\r\n        msSpeechRecognition: 'msSpeechRecognition' in window,\r\n        clientBrowserSupport\r\n      });\r\n\r\n      // Fetch available indexes and initialize selected index\r\n      const fetchIndexesAndInitialize = async () => {\r\n        setIndexesLoading(true);\r\n        console.log(\"Fetching available FAISS indexes from PINE collection...\");\r\n\r\n        try {\r\n          // Fetch user's available indexes from PINE collection\r\n          const availableIndexes = await ApiService.fetchUserIndexes();\r\n          console.log(\"Available indexes for user:\", availableIndexes);\r\n\r\n          // Update the indexes state\r\n          setPineconeIndexes(availableIndexes);\r\n\r\n          // Initialize selectedIndex from localStorage, fallback to first available or 'default'\r\n          const savedIndex = localStorage.getItem('selectedFaissIndex') || localStorage.getItem('faiss_index_name');\r\n          let indexToSelect = 'default';\r\n\r\n          if (savedIndex && availableIndexes.includes(savedIndex)) {\r\n            indexToSelect = savedIndex;\r\n          } else if (availableIndexes.includes('default')) {\r\n            indexToSelect = 'default';\r\n          } else if (availableIndexes.length > 0) {\r\n            indexToSelect = availableIndexes[0];\r\n          }\r\n\r\n          setSelectedIndex(indexToSelect);\r\n          localStorage.setItem('selectedFaissIndex', indexToSelect);\r\n          localStorage.setItem('faiss_index_name', indexToSelect);\r\n\r\n          console.log(`Selected index: ${indexToSelect}`);\r\n        } catch (error) {\r\n          console.error(\"Error fetching indexes:\", error);\r\n          // Fallback to default configuration\r\n          setPineconeIndexes(['default']);\r\n          setSelectedIndex('default');\r\n          localStorage.setItem('selectedFaissIndex', 'default');\r\n          localStorage.setItem('faiss_index_name', 'default');\r\n        } finally {\r\n          setIndexesLoading(false);\r\n        }\r\n      };\r\n\r\n      fetchIndexesAndInitialize();\r\n    }\r\n  }, [clientBrowserSupport, clientMicrophoneAvailable, clientListening, clientTranscript]);\r\n\r\n  // Get user email from localStorage and set up API environment\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      // Get the logged-in user's email from localStorage\r\n      const email = localStorage.getItem('user_email');\r\n      setUserEmail(email);\r\n\r\n      // Check if we should use the development environment\r\n      const useDevEnv = localStorage.getItem('use_dev_environment');\r\n      if (useDevEnv === 'true') {\r\n        console.log('Using development API endpoint');\r\n        setApiEnvironment('development');\r\n      } else {\r\n        console.log('Using production API endpoint');\r\n        setApiEnvironment('production');\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Clear uploaded content when navigating to a different chat\r\n  useEffect(() => {\r\n    // Clear uploaded content when chat ID changes (new chat session)\r\n    setUploadedFiles([]);\r\n    setUploadedURLs([]);\r\n    setShowUploadedContent(false);\r\n    console.log('Cleared uploaded content for new chat session:', chatIdUrl);\r\n  }, [chatIdUrl]);\r\n\r\n  // Check for browser support and microphone availability\r\n  useEffect(() => {\r\n    if (!browserSupportsSpeechRecognition && typeof window !== 'undefined') {\r\n      console.warn(\"Browser doesn't support speech recognition.\");\r\n    }\r\n\r\n    if (!isMicrophoneAvailable && typeof window !== 'undefined') {\r\n      console.warn(\"Microphone is not available.\");\r\n    }\r\n\r\n    // Cleanup function to ensure microphone is stopped when component unmounts\r\n    return () => {\r\n      if (isListening && typeof window !== 'undefined') {\r\n        console.log(\"Component unmounting, stopping speech recognition\");\r\n        SpeechRecognition.stopListening();\r\n      }\r\n\r\n      // Clear any timers\r\n      if (speakingTimerRef.current) {\r\n        clearTimeout(speakingTimerRef.current);\r\n        speakingTimerRef.current = null;\r\n      }\r\n    };\r\n  }, [browserSupportsSpeechRecognition, isMicrophoneAvailable, isListening]);\r\n\r\n  // Function to log speech recognition state for debugging - uncomment the debug button below to use\r\n  // const logSpeechRecognitionState = () => {\r\n  //   console.group(\"Speech Recognition Debug Info\");\r\n  //   console.log(\"Browser supports speech recognition:\", browserSupportsSpeechRecognition);\r\n  //   console.log(\"Microphone available:\", isMicrophoneAvailable);\r\n  //   console.log(\"Current listening state (from hook):\", listening);\r\n  //   console.log(\"Current isListening state (component):\", isListening);\r\n  //   console.log(\"Current transcript:\", transcript);\r\n  //   console.log(\"Selected language:\", selectedLanguage);\r\n  //   console.log(\"Language code:\", getLanguageCode());\r\n  //   console.groupEnd();\r\n  // };\r\n\r\n  // Generate sound wave animation elements\r\n  // Debug log for hasUserMadeFirstRequest state\r\n  console.log('🔍 ChatBox render: hasUserMadeFirstRequest =', hasUserMadeFirstRequest);\r\n\r\n  const renderSoundWave = () => {\r\n    // Use different colors based on selected language\r\n    let waveColor = \"bg-red-500\"; // Default color\r\n\r\n    if (selectedLanguage === \"Tamil\") {\r\n      waveColor = \"bg-purple-500\";\r\n    } else if (selectedLanguage === \"Telugu\") {\r\n      waveColor = \"bg-green-500\";\r\n    } else if (selectedLanguage === \"Kannada\") {\r\n      waveColor = \"bg-orange-500\";\r\n    }\r\n\r\n    return (\r\n      <div className=\"flex items-center gap-[2px] h-5\">\r\n        {Array.from({ length: 5 }).map((_, i) => {\r\n          const isActive = speaking || (i === 2); // Middle bar always active when not speaking\r\n          const delay = i * 0.1;\r\n          const duration = isActive ? (0.5 + (i * 0.1)) : 1; // Use deterministic duration\r\n          const height = isActive ?\r\n            (i % 2 === 0 ? 10 + (i * 2) : 6 + (i * 1.5)) : // Use deterministic height based on index\r\n            (i === 2 ? 6 : 4);\r\n\r\n          return (\r\n            <div\r\n              key={`wave-${i}`} // Use stable key\r\n              className={`w-1 ${waveColor} rounded-full transition-all`}\r\n              style={{\r\n                height: `${height}px`,\r\n                animationName: isActive ? 'soundWavePulse' : 'none',\r\n                animationDuration: `${duration}s`,\r\n                animationIterationCount: 'infinite',\r\n                animationDirection: 'alternate',\r\n                animationDelay: `${delay}s`,\r\n                opacity: isActive ? 1 : 0.5\r\n              }}\r\n            ></div>\r\n          );\r\n        })}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full max-w-[1070px] mx-auto px-2 sm:px-4 md:px-6\">\r\n      <style jsx global>{`\r\n        @keyframes fadeIn {\r\n          from { opacity: 0; transform: translateY(-5px); }\r\n          to { opacity: 1; transform: translateY(0); }\r\n        }\r\n        .animate-fadeIn {\r\n          animation: fadeIn 0.3s ease-out forwards;\r\n        }\r\n      `}</style>\r\n\r\n\r\n\r\n      {isListening && (\r\n        <div className={`mb-4 p-3 rounded-lg flex items-center justify-between shadow-sm relative z-[10]\r\n          ${selectedLanguage === \"Tamil\"\r\n            ? \"bg-gradient-to-r from-purple-50 to-purple-50/70 border border-purple-200\"\r\n            : selectedLanguage === \"Telugu\"\r\n              ? \"bg-gradient-to-r from-green-50 to-green-50/70 border border-green-200\"\r\n              : selectedLanguage === \"Kannada\"\r\n                ? \"bg-gradient-to-r from-orange-50 to-orange-50/70 border border-orange-200\"\r\n                : \"bg-gradient-to-r from-red-50 to-red-50/70 border border-red-200\"}`}>\r\n          <div className=\"flex items-center gap-3\">\r\n            <div className=\"flex items-center bg-white p-1 rounded-full shadow-sm\">\r\n              {renderSoundWave()}\r\n            </div>\r\n            <div>\r\n              <span className={`text-sm font-medium ${\r\n                selectedLanguage === \"Tamil\"\r\n                  ? \"text-purple-700\"\r\n                  : selectedLanguage === \"Telugu\"\r\n                    ? \"text-green-700\"\r\n                    : selectedLanguage === \"Kannada\"\r\n                      ? \"text-orange-700\"\r\n                      : \"text-red-700\"\r\n              }`}>\r\n                {selectedLanguage === \"Tamil\"\r\n                  ? \"குரல் பதிவு செயலில் உள்ளது\"\r\n                  : selectedLanguage === \"Telugu\"\r\n                    ? \"వాయిస్ రికార్డింగ్ యాక్టివ్\"\r\n                    : selectedLanguage === \"Kannada\"\r\n                      ? \"ಧ್ವನಿ ರೆಕಾರ್ಡಿಂಗ್ ಸಕ್ರಿಯವಾಗಿದೆ\"\r\n                      : \"Voice recording active in\"} <strong>{selectedLanguage}</strong>\r\n              </span>\r\n              {wordCount > 0 && (\r\n                <div className={`text-xs mt-0.5 ${\r\n                  selectedLanguage === \"Tamil\"\r\n                    ? \"text-purple-500/80\"\r\n                    : selectedLanguage === \"Telugu\"\r\n                      ? \"text-green-500/80\"\r\n                      : selectedLanguage === \"Kannada\"\r\n                        ? \"text-orange-500/80\"\r\n                        : \"text-red-500/80\"\r\n                }`}>\r\n                  {selectedLanguage === \"Tamil\"\r\n                    ? `இதுவரை ${wordCount} சொற்கள் பதிவு செய்யப்பட்டுள்ளன`\r\n                    : selectedLanguage === \"Telugu\"\r\n                      ? `ఇప్పటివరకు ${wordCount} పదాలు క్యాప్చర్ చేయబడ్డాయి`\r\n                      : selectedLanguage === \"Kannada\"\r\n                        ? `ಇಲ್ಲಿಯವರೆಗೆ ${wordCount} ಪದಗಳನ್ನು ಸೆರೆಹಿಡಿಯಲಾಗಿದೆ`\r\n                        : `Captured ${wordCount} ${wordCount === 1 ? 'word' : 'words'} so far`}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <button\r\n            onClick={() => {\r\n              toggleListening();\r\n              // Additional cleanup to ensure UI is reset\r\n              setIsListening(false);\r\n              resetTranscript();\r\n              setSpeaking(false);\r\n            }}\r\n            className={`text-xs px-3 py-1.5 bg-white rounded-full transition-colors shadow-sm hover:shadow flex items-center gap-1.5\r\n              ${selectedLanguage === \"Tamil\"\r\n                ? \"border border-purple-300 text-purple-600 hover:bg-purple-100\"\r\n                : selectedLanguage === \"Telugu\"\r\n                  ? \"border border-green-300 text-green-600 hover:bg-green-100\"\r\n                  : selectedLanguage === \"Kannada\"\r\n                    ? \"border border-orange-300 text-orange-600 hover:bg-orange-100\"\r\n                    : \"border border-red-300 text-red-600 hover:bg-red-100\"}`}\r\n          >\r\n            <PiStop className={\r\n              selectedLanguage === \"Tamil\"\r\n                ? \"text-purple-600\"\r\n                : selectedLanguage === \"Telugu\"\r\n                  ? \"text-green-600\"\r\n                  : selectedLanguage === \"Kannada\"\r\n                    ? \"text-orange-600\"\r\n                    : \"text-red-600\"\r\n            } />\r\n            <span>{\r\n              selectedLanguage === \"Tamil\"\r\n                ? \"பதிவை மீட்டமை\"\r\n                : selectedLanguage === \"Telugu\"\r\n                  ? \"రీసెట్ రికార్డింగ్\"\r\n                  : selectedLanguage === \"Kannada\"\r\n                    ? \"ರಿಸೆಟ್ ರೆಕಾರ್ಡಿಂಗ್\"\r\n                    : \"Reset Recording\"\r\n            }</span>\r\n          </button>\r\n        </div>\r\n      )}\r\n      {/* Index Selector and Indicator - Only show after first request */}\r\n      {hasUserMadeFirstRequest && (\r\n        <div className=\"w-full flex justify-between items-center mb-2\">\r\n          {/* Index Selector Dropdown */}\r\n          <div className=\"relative\" ref={indexSelectorRef}>\r\n            {/* <button\r\n              onClick={() => setShowIndexSelector(!showIndexSelector)}\r\n              disabled={indexesLoading}\r\n              className=\"px-3 py-1.5 bg-gray-50 dark:bg-gray-800 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              {indexesLoading ? (\r\n                <>\r\n                  <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600\"></div>\r\n                  <span>Loading...</span>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <span>Select Index</span>\r\n                  <svg className={`w-4 h-4 transition-transform ${showIndexSelector ? 'rotate-180' : ''}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\r\n                  </svg>\r\n                </>\r\n              )}\r\n            </button> */}\r\n\r\n            {showIndexSelector && !indexesLoading && (\r\n              <div className=\"absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-[50] max-h-60 overflow-y-auto\">\r\n                {pineconeIndexes.length > 0 ? (\r\n                  pineconeIndexes.map((index) => (\r\n                    <button\r\n                      key={index}\r\n                      onClick={() => {\r\n                        setSelectedIndex(index);\r\n                        setShowIndexSelector(false);\r\n                        setShowIndexConfirmation(true);\r\n                        localStorage.setItem('selectedFaissIndex', index);\r\n                        localStorage.setItem('faiss_index_name', index);\r\n                        console.log(`Selected index: ${index}`);\r\n                      }}\r\n                      className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between\r\n                        ${selectedIndex === index ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`}\r\n                    >\r\n                      <span>{index}</span>\r\n                      {selectedIndex === index && (\r\n                        <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                          <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                        </svg>\r\n                      )}\r\n                    </button>\r\n                  ))\r\n                ) : (\r\n                  <div className=\"px-3 py-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                    No indexes available\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Index Indicator - Shows which index is currently selected */}\r\n          <div className=\"px-2 py-0.5 bg-blue-50 dark:bg-blue-900/20 rounded-md text-xs font-medium text-gray-600 dark:text-gray-400 border border-blue-200 dark:border-blue-800/50 shadow-sm flex items-center gap-1\">\r\n            <span>Active Category:</span>\r\n            <span className=\"text-blue-600 dark:text-blue-400 font-semibold\">\r\n              {selectedIndex || \"Default\"}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Index Selection Confirmation */}\r\n      {showIndexConfirmation && (\r\n        <div className=\"mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg animate-fadeIn\">\r\n          <div className=\"flex items-center gap-2 text-green-700 dark:text-green-400\">\r\n            <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n            <span className=\"text-sm font-medium\">\r\n              Index \"{selectedIndex}\" selected! Your responses will be filtered based on this index.\r\n            </span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Data Sources Indicator */}\r\n      {/* <UploadedContentIndicator\r\n        hasUploadedContent={responseHasUploadedContent}\r\n        selectedIndex={selectedIndex}\r\n        uploadSources={responseUploadSources}\r\n        selectedLanguage={selectedLanguage.toLowerCase()}\r\n      /> */}\r\n\r\n      <form\r\n        onSubmit={handleSendMessage}\r\n        className=\"w-full bg-primaryColor/5 p-2 sm:p-3 lg:p-4 rounded-xl border border-primaryColor/20\"\r\n      >\r\n        {/* Language validation error message */}\r\n        {languageError && (\r\n          <div className=\"mb-3 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm flex items-center\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 flex-shrink-0\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n            {languageError}\r\n          </div>\r\n        )}\r\n\r\n\r\n        <div className=\"w-full bg-white rounded-lg max-lg:text-sm block dark:bg-n0 relative z-[5]\">\r\n\r\n\r\n          {isListening || transcript ? (\r\n            <div className=\"w-full p-4 min-h-[56px] max-h-[200px] overflow-y-auto relative flex flex-col\">\r\n              {isEditingTranscript ? (\r\n                // Editable textarea for transcript\r\n                <div className=\"flex-grow relative\">\r\n                  <textarea\r\n                    ref={editableTranscriptRef}\r\n                    className=\"w-full h-full min-h-[80px] p-2 border border-primaryColor/30 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor/50 text-gray-800 resize-none\"\r\n                    value={editedTranscript}\r\n                    onChange={handleTranscriptChange}\r\n                    onKeyDown={(e) => {\r\n                      if (e.key === 'Enter' && !e.shiftKey) {\r\n                        e.preventDefault();\r\n                        // Only submit if there's text to send and not currently loading\r\n                        if (editedTranscript.trim() && !isLoading) {\r\n                          // Create a synthetic form event to trigger handleSendMessage\r\n                          const syntheticEvent = {\r\n                            preventDefault: () => {},\r\n                            stopPropagation: () => {},\r\n                            nativeEvent: e.nativeEvent,\r\n                            target: e.target,\r\n                            currentTarget: e.currentTarget,\r\n                            bubbles: false,\r\n                            cancelable: false,\r\n                            defaultPrevented: false,\r\n                            eventPhase: 0,\r\n                            isTrusted: false,\r\n                            timeStamp: Date.now(),\r\n                            type: 'submit',\r\n                            isDefaultPrevented: () => false,\r\n                            isPropagationStopped: () => false,\r\n                            persist: () => {}\r\n                          } as unknown as FormEvent;\r\n                          handleSendMessage(syntheticEvent);\r\n                        }\r\n                      }\r\n                    }}\r\n                    placeholder=\"Edit your transcribed text here...\"\r\n                  />\r\n                  <button\r\n                    onClick={toggleTranscriptEditMode}\r\n                    className={`absolute top-2 right-2 p-1.5 rounded-full transition-colors\r\n                      ${selectedLanguage === \"Tamil\"\r\n                        ? 'bg-purple-100 text-purple-600 hover:bg-purple-200'\r\n                        : selectedLanguage === \"Telugu\"\r\n                          ? 'bg-green-100 text-green-600 hover:bg-green-200'\r\n                          : 'bg-blue-100 text-blue-600 hover:bg-blue-200'\r\n                      }`}\r\n                    title=\"Save edits\"\r\n                  >\r\n                    <PiCheck className=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              ) : (\r\n                // View mode for transcript\r\n                <div\r\n                  ref={transcriptRef}\r\n                  className=\"flex-grow text-gray-800 dark:text-white break-words relative\"\r\n                >\r\n                  {transcript && transcript.trim() !== \"\" ? (\r\n                    // Show transcript with animated cursor\r\n                    <div className=\"relative\">\r\n                      <p className=\"m-0 leading-relaxed\">\r\n                        {transcript ? transcript : \"\"}\r\n                        {speaking && (\r\n                          <span className=\"inline-block w-1 h-4 bg-primaryColor ml-1\"\r\n                            style={{\r\n                              animationName: 'pulse',\r\n                              animationDuration: '1s',\r\n                              animationIterationCount: 'infinite',\r\n                              animationDirection: 'alternate'\r\n                            }}\r\n                          ></span>\r\n                        )}\r\n                      </p>\r\n\r\n                      {/* Action buttons - only show when not actively listening */}\r\n                      {!isListening && transcript && transcript.trim() !== \"\" && (\r\n                        <div className=\"absolute top-0 right-0 flex gap-1\">\r\n                          <button\r\n                            onClick={() => {\r\n                              setTranscript(\"\");\r\n                              setInputText(\"\");\r\n                              setUserQuery(\"\");\r\n                              resetTranscript();\r\n                              setWordCount(0);\r\n                              setRecentWords([]);\r\n                            }}\r\n                            className={`p-1.5 rounded-full transition-colors\r\n                              ${selectedLanguage === \"Tamil\"\r\n                                ? 'bg-red-100 text-red-600 hover:bg-red-200'\r\n                                : selectedLanguage === \"Telugu\"\r\n                                  ? 'bg-red-100 text-red-600 hover:bg-red-200'\r\n                                  : 'bg-red-100 text-red-600 hover:bg-red-200'\r\n                              }`}\r\n                            title=\"Clear transcript\"\r\n                          >\r\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\r\n                            </svg>\r\n                          </button>\r\n                          <button\r\n                            onClick={toggleTranscriptEditMode}\r\n                            className={`p-1.5 rounded-full transition-colors\r\n                              ${selectedLanguage === \"Tamil\"\r\n                                ? 'bg-purple-100 text-purple-600 hover:bg-purple-200'\r\n                                : selectedLanguage === \"Telugu\"\r\n                                  ? 'bg-green-100 text-green-600 hover:bg-green-200'\r\n                                  : 'bg-blue-100 text-blue-600 hover:bg-blue-200'\r\n                              }`}\r\n                            title=\"Edit transcript\"\r\n                          >\r\n                            <PiPencilSimple className=\"text-lg\" />\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"flex items-center gap-2 text-gray-400 italic\">\r\n                      <span>\r\n                        {selectedLanguage === \"Tamil\"\r\n                          ? \"தமிழில் கேட்கிறது... இப்போது பேசவும்\"\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? \"తెలుగులో వింటున్నాము... ఇప్పుడు మాట్లాడండి\"\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? \"ಕನ್ನಡದಲ್ಲಿ ಆಲಿಸುತ್ತಿದ್ದೇವೆ... ಈಗ ಮಾತನಾಡಿ\"\r\n                              : `Listening in ${selectedLanguage}... Speak now`}\r\n                      </span>\r\n                      <div className=\"flex space-x-1 ml-2\">\r\n                        {[0, 1, 2].map((i) => (\r\n                          <div\r\n                            key={`dot-${i}`} /* Use stable key */\r\n                            className={`h-2 w-2 rounded-full ${\r\n                              selectedLanguage === \"Tamil\"\r\n                                ? \"bg-purple-500\"\r\n                                : selectedLanguage === \"Telugu\"\r\n                                  ? \"bg-green-500\"\r\n                                  : selectedLanguage === \"Kannada\"\r\n                                    ? \"bg-orange-500\"\r\n                                    : \"bg-red-500\"\r\n                            }`}\r\n                            style={{\r\n                              animationName: 'pulse',\r\n                              animationDuration: '1s',\r\n                              animationIterationCount: 'infinite',\r\n                              animationDirection: 'alternate',\r\n                              animationDelay: `${i * 0.2}s`\r\n                            }}\r\n                          ></div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Speaking indicator */}\r\n              {(transcript && transcript.trim() !== \"\") && (\r\n                <div className=\"mt-2 flex items-center justify-between text-xs text-gray-500 border-t pt-2 border-gray-100\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    {isEditingTranscript ? (\r\n                      <span className=\"text-blue-500 flex items-center gap-1\">\r\n                        <PiPencilSimple />\r\n                        {selectedLanguage === \"Tamil\"\r\n                          ? \"திருத்துகிறது\"\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? \"సవరిస్తోంది\"\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? \"ಸಂಪಾದಿಸುತ್ತಿದೆ\"\r\n                              : \"Editing\"}\r\n                      </span>\r\n                    ) : speaking ? (\r\n                      <span className=\"text-green-500 flex items-center gap-1\">\r\n                        <PiWaveform className=\"animate-pulse\" />\r\n                        {selectedLanguage === \"Tamil\"\r\n                          ? \"செயலில்\"\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? \"యాక్టివ్\"\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? \"ಸಕ್ರಿಯ\"\r\n                              : \"Active\"}\r\n                      </span>\r\n                    ) : (\r\n                      <span className=\"text-gray-400 flex items-center gap-1\">\r\n                        <PiWaveform />\r\n                        {selectedLanguage === \"Tamil\"\r\n                          ? \"இடைநிறுத்தப்பட்டது\"\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? \"నిలిపివేయబడింది\"\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? \"ವಿರಾಮಗೊಳಿಸಲಾಗಿದೆ\"\r\n                              : \"Paused\"}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                  <span className=\"bg-gray-100 px-2 py-1 rounded-full text-xs\">\r\n                    {selectedLanguage === \"Tamil\"\r\n                      ? `${wordCount} சொற்கள்`\r\n                      : selectedLanguage === \"Telugu\"\r\n                        ? `${wordCount} పదాలు`\r\n                        : selectedLanguage === \"Kannada\"\r\n                          ? `${wordCount} ಪದಗಳು`\r\n                          : `${wordCount} words`}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <div className=\"relative w-full\">\r\n              {/* Uploaded content display for regular input mode */}\r\n              {showUploadedContent && (uploadedFiles.length > 0 || uploadedURLs.length > 0) && (\r\n                <div className=\"px-4 pt-3 pb-2 border-b border-gray-100 dark:border-gray-700\">\r\n                  <div className=\"space-y-2\">\r\n                    {/* Files */}\r\n                    {uploadedFiles.map((file, index) => (\r\n                      <div\r\n                        key={`file-${index}`}\r\n                        className=\"flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300\"\r\n                      >\r\n                        <div className={`flex-shrink-0 ${\r\n                          selectedLanguage === \"Tamil\"\r\n                            ? 'text-purple-600'\r\n                            : selectedLanguage === \"Telugu\"\r\n                              ? 'text-green-600'\r\n                              : selectedLanguage === \"Kannada\"\r\n                                ? 'text-orange-600'\r\n                                : 'text-blue-600'\r\n                        }`}>\r\n                          {getFileIcon(file.name)}\r\n                        </div>\r\n                        <span className=\"flex-grow truncate font-medium\">\r\n                          {file.name}\r\n                        </span>\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          {(file.size / 1024 / 1024).toFixed(1)}MB\r\n                        </span>\r\n                        <button\r\n                          onClick={() => removeUploadedFile(index)}\r\n                          className=\"flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors rounded\"\r\n                          title={getUploadDisplayText().removeFile}\r\n                        >\r\n                          <PiX className=\"w-3 h-3\" />\r\n                        </button>\r\n                      </div>\r\n                    ))}\r\n\r\n                    {/* URLs */}\r\n                    {uploadedURLs.map((urlItem, index) => (\r\n                      <div\r\n                        key={`url-${index}`}\r\n                        className=\"flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300\"\r\n                      >\r\n                        <div className={`flex-shrink-0 ${\r\n                          urlItem.type === 'youtube' ? 'text-red-600' :\r\n                          selectedLanguage === \"Tamil\"\r\n                            ? 'text-purple-600'\r\n                            : selectedLanguage === \"Telugu\"\r\n                              ? 'text-green-600'\r\n                              : selectedLanguage === \"Kannada\"\r\n                                ? 'text-orange-600'\r\n                                : 'text-blue-600'\r\n                        }`}>\r\n                          {getUrlIcon(urlItem.type)}\r\n                        </div>\r\n                        <span className=\"flex-grow truncate font-medium\">\r\n                          {urlItem.type === 'youtube'\r\n                            ? getUploadDisplayText().youtubeVideo\r\n                            : getUploadDisplayText().articleLink}\r\n                        </span>\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400 truncate max-w-[100px]\">\r\n                          {urlItem.url.replace(/^https?:\\/\\//, '')}\r\n                        </span>\r\n                        <button\r\n                          onClick={() => removeUploadedURL(index)}\r\n                          className=\"flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors rounded\"\r\n                          title={getUploadDisplayText().removeUrl}\r\n                        >\r\n                          <PiX className=\"w-3 h-3\" />\r\n                        </button>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"relative flex items-center w-full\">\r\n                <input\r\n                  className=\"w-full outline-none p-4 pr-12 bg-transparent relative z-[1]\"\r\n                placeholder={\r\n                  selectedLanguage === \"Tamil\"\r\n                    ? \"நிதி தொடர்பான கேள்வியை தமிழில் கேளுங்கள்...\"\r\n                    : selectedLanguage === \"Telugu\"\r\n                      ? \"ఒక ఆర్థిక విషయంపై నేను ఒక ప్రశ్నను అడగదలుచుకున్నాను...\"\r\n                      : selectedLanguage === \"Kannada\"\r\n                        ? \"ಹಣಕಾಸು ವಿಷಯದ ಬಗ್ಗೆ ಪ್ರಶ్ನೆಯನ್ನು ಕೇಳಿ...\"\r\n                        : \"ask question based financial topic..\"\r\n                }\r\n                value={inputText}\r\n                onChange={(e) => {\r\n                  setUserQuery(e.target.value);\r\n                  setInputText(e.target.value);\r\n                  // Clear any language error when the user starts typing\r\n                  if (languageError) {\r\n                    setLanguageError(null);\r\n                  }\r\n                }}\r\n                onKeyDown={(e) => {\r\n                  if (e.key === 'Enter' && !e.shiftKey) {\r\n                    e.preventDefault();\r\n                    // Only submit if there's text to send and not currently loading\r\n                    if (inputText.trim() && !isLoading) {\r\n                      // Create a synthetic form event to trigger handleSendMessage\r\n                          const syntheticEvent = {\r\n                            preventDefault: () => {},\r\n                            stopPropagation: () => {},\r\n                            nativeEvent: e.nativeEvent,\r\n                            target: e.target,\r\n                            currentTarget: e.currentTarget,\r\n                            bubbles: false,\r\n                            cancelable: false,\r\n                            defaultPrevented: false,\r\n                            eventPhase: 0,\r\n                            isTrusted: false,\r\n                            timeStamp: Date.now(),\r\n                            type: 'submit',\r\n                            isDefaultPrevented: () => false,\r\n                            isPropagationStopped: () => false,\r\n                            persist: () => {}\r\n                          } as unknown as FormEvent;\r\n                      handleSendMessage(syntheticEvent);\r\n                    }\r\n                  }\r\n                }}\r\n                disabled={isLoading}\r\n                onClick={() => {\r\n                  // Close any open dropdowns when clicking in the input field\r\n                  // No longer need to close language dropdown since we're using horizontal buttons\r\n                  // setShowLanguageDropdown(false);\r\n                  setShowSuggestions(false);\r\n                }}\r\n              />\r\n\r\n              {/* Upload component positioned to the right of the input with improved alignment */}\r\n                <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 z-[2]\">\r\n                  <ChatInputUpload\r\n                    selectedLanguage={selectedLanguage}\r\n                    disabled={isLoading}\r\n                    onUploadStateChange={handleUploadStateChange}\r\n                    onNetworkError={() => setShowConnectionTest(true)}\r\n                    onFileUpload={(files: File[]) => {\r\n                      console.log('Files uploaded:', files);\r\n                      // Only process if we have valid files\r\n                      if (files && files.length > 0) {\r\n                        // Prevent duplicates by checking if files already exist\r\n                        setUploadedFiles(prevFiles => {\r\n                          const newFiles = files.filter(newFile =>\r\n                            !prevFiles.some(existingFile =>\r\n                              existingFile.name === newFile.name &&\r\n                              existingFile.size === newFile.size &&\r\n                              existingFile.lastModified === newFile.lastModified\r\n                            )\r\n                          );\r\n                          if (newFiles.length > 0) {\r\n                            setShowUploadedContent(true);\r\n                            return [...prevFiles, ...newFiles];\r\n                          }\r\n                          return prevFiles;\r\n                        });\r\n                      }\r\n                    }}\r\n                    onURLSubmit={(url: string, type: 'youtube' | 'article') => {\r\n                      console.log('URL submitted:', url, 'Type:', type);\r\n                      // Validate URL before adding\r\n                      if (url && url.trim()) {\r\n                        // Prevent duplicate URLs\r\n                        setUploadedURLs(prev => {\r\n                          const urlExists = prev.some(existingUrl => existingUrl.url === url.trim());\r\n                          if (urlExists) {\r\n                            console.log('URL already exists:', url);\r\n                            return prev;\r\n                          }\r\n                          setShowUploadedContent(true);\r\n                          return [...prev, { url: url.trim(), type }];\r\n                        });\r\n                      }\r\n                    }}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 pt-3 sm:pt-4\">\r\n          <div className=\"flex justify-start items-center gap-3 relative\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => setShowSuggestions(!showSuggestions)}\r\n              className={`bg-white px-3 py-2 rounded-lg flex items-center gap-2 border\r\n                ${showSuggestions ? 'border-primaryColor bg-primaryColor/5' : 'border-primaryColor/20'}\r\n                hover:bg-primaryColor/5 hover:border-primaryColor transition-all shadow-sm hover:shadow dark:bg-n0\r\n                text-sm font-medium\r\n                ${selectedLanguage === \"Tamil\"\r\n                  ? 'text-purple-700'\r\n                  : selectedLanguage === \"Telugu\"\r\n                    ? 'text-green-700'\r\n                    : selectedLanguage === \"Kannada\"\r\n                      ? 'text-orange-700'\r\n                      : 'text-gray-700'}\r\n                dark:text-gray-300`}>\r\n              <PiLightbulb className={`${showSuggestions\r\n                ? 'text-primaryColor'\r\n                : selectedLanguage === \"Tamil\"\r\n                  ? 'text-purple-500'\r\n                  : selectedLanguage === \"Telugu\"\r\n                    ? 'text-green-500'\r\n                    : selectedLanguage === \"Kannada\"\r\n                      ? 'text-orange-500'\r\n                      : 'text-amber-500'}`} />\r\n              <span>{\r\n                selectedLanguage === \"Tamil\"\r\n                  ? 'பரிந்துரைகள்'\r\n                  : selectedLanguage === \"Telugu\"\r\n                    ? 'సూచనలు'\r\n                    : selectedLanguage === \"Kannada\"\r\n                      ? 'ಸಲಹೆಗಳು'\r\n                      : 'Suggestions'\r\n              }</span>\r\n              <PiSparkle className={`${showSuggestions\r\n                ? 'text-primaryColor'\r\n                : selectedLanguage === \"Tamil\"\r\n                  ? 'text-purple-400'\r\n                  : selectedLanguage === \"Telugu\"\r\n                    ? 'text-green-400'\r\n                    : selectedLanguage === \"Kannada\"\r\n                      ? 'text-orange-400'\r\n                      : 'text-amber-400'}\r\n                ${!showSuggestions ? 'animate-pulse' : ''}`} />\r\n            </button>\r\n\r\n            {/* Suggestions popup with professional styling and animations */}\r\n            {showSuggestions && (\r\n              <div\r\n                className={`fixed inset-0 flex items-center justify-center z-[60] animate-fadeIn ${\r\n                  selectedLanguage === \"Tamil\"\r\n                    ? 'bg-gradient-to-br from-purple-900/30 to-black/40'\r\n                    : selectedLanguage === \"Telugu\"\r\n                      ? 'bg-gradient-to-br from-green-900/30 to-black/40'\r\n                      : selectedLanguage === \"Kannada\"\r\n                        ? 'bg-gradient-to-br from-orange-900/30 to-black/40'\r\n                        : 'bg-gradient-to-br from-blue-900/30 to-black/40'\r\n                }`}\r\n                onClick={() => setShowSuggestions(false)}\r\n                style={{ backdropFilter: 'blur(3px)', animationDuration: '0.2s' }}\r\n              >\r\n                <div\r\n                  ref={suggestionsRef}\r\n                  className={`rounded-xl shadow-2xl border max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden animate-scaleIn ${\r\n                    selectedLanguage === \"Tamil\"\r\n                      ? 'border-purple-200/50 bg-gradient-to-b from-white to-purple-50/30'\r\n                      : selectedLanguage === \"Telugu\"\r\n                        ? 'border-green-200/50 bg-gradient-to-b from-white to-green-50/30'\r\n                        : selectedLanguage === \"Kannada\"\r\n                          ? 'border-orange-200/50 bg-gradient-to-b from-white to-orange-50/30'\r\n                          : 'border-blue-200/50 bg-gradient-to-b from-white to-blue-50/30'\r\n                  }`}\r\n                  onClick={(e) => e.stopPropagation()}\r\n                  style={{\r\n                    animationDuration: '0.3s',\r\n                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)'\r\n                  }}\r\n                >\r\n                  <div className={`p-5 border-b flex justify-between items-center rounded-t-xl ${\r\n                    selectedLanguage === \"Tamil\"\r\n                      ? 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-100'\r\n                      : selectedLanguage === \"Telugu\"\r\n                        ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-100'\r\n                        : selectedLanguage === \"Kannada\"\r\n                          ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-100'\r\n                          : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-100'\r\n                  }`}>\r\n                    <h3 className=\"text-lg font-semibold flex items-center gap-2\">\r\n                      <div className={`p-2 rounded-full ${\r\n                        selectedLanguage === \"Tamil\"\r\n                          ? 'bg-purple-100'\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? 'bg-green-100'\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? 'bg-orange-100'\r\n                              : 'bg-blue-100'\r\n                      }`}>\r\n                        <PiLightbulb className={`text-xl ${\r\n                          selectedLanguage === \"Tamil\"\r\n                            ? 'text-purple-500'\r\n                            : selectedLanguage === \"Telugu\"\r\n                              ? 'text-green-500'\r\n                              : selectedLanguage === \"Kannada\"\r\n                                ? 'text-orange-500'\r\n                                : 'text-blue-500'\r\n                        }`} />\r\n                      </div>\r\n                      <span className={\r\n                        selectedLanguage === \"Tamil\"\r\n                          ? 'text-purple-800'\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? 'text-green-800'\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? 'text-orange-800'\r\n                              : 'text-blue-800'\r\n                      }>\r\n                        {selectedLanguage === \"Tamil\"\r\n                          ? 'பரிந்துரைக்கப்பட்ட நிதி கேள்விகள்'\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? 'సిఫార్సు చేయబడిన ఆర్థిక ప్రశ్నలు'\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? 'ಶಿఫಾರಸು ಮಾಡಲಾದ ಹಣಕಾಸು ಪ್ರಶ್ನೆಗಳು'\r\n                              : 'Recommended Financial Questions'}\r\n                      </span>\r\n                    </h3>\r\n                    <button\r\n                      onClick={() => setShowSuggestions(false)}\r\n                      className={`p-2 rounded-full transition-colors ${\r\n                        selectedLanguage === \"Tamil\"\r\n                          ? 'bg-purple-100/50 text-purple-500 hover:bg-purple-200 hover:text-purple-700'\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? 'bg-green-100/50 text-green-500 hover:bg-green-200 hover:text-green-700'\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? 'bg-orange-100/50 text-orange-500 hover:bg-orange-200 hover:text-orange-700'\r\n                              : 'bg-blue-100/50 text-blue-500 hover:bg-blue-200 hover:text-blue-700'\r\n                      }`}\r\n                      aria-label=\"Close\"\r\n                    >\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"p-5 overflow-y-auto max-h-[60vh]\">\r\n                    <div className={`rounded-lg p-3 mb-5 ${\r\n                      selectedLanguage === \"Tamil\"\r\n                        ? 'bg-gradient-to-r from-purple-50 to-white border border-purple-100'\r\n                        : selectedLanguage === \"Telugu\"\r\n                          ? 'bg-gradient-to-r from-green-50 to-white border border-green-100'\r\n                          : selectedLanguage === \"Kannada\"\r\n                            ? 'bg-gradient-to-r from-orange-50 to-white border border-orange-100'\r\n                            : 'bg-gradient-to-r from-blue-50 to-white border border-blue-100'\r\n                    }`}>\r\n                      <p className={`text-sm font-medium ${\r\n                        selectedLanguage === \"Tamil\"\r\n                          ? 'text-purple-700'\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? 'text-green-700'\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? 'text-orange-700'\r\n                              : 'text-blue-700'\r\n                      }`}>\r\n                        {selectedLanguage === \"Tamil\"\r\n                          ? 'உங்கள் நிதி தேவைகளுக்கான பரிந்துரைக்கப்பட்ட கேள்விகளைப் பார்க்கவும்'\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? 'మీ ఆర్థిక అవసరాలకు సిఫార్సు చేయబడిన ప్రశ్నలను చూడండి'\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? 'ನಿಮ್ಮ ಹಣಕಾಸು ಅಗತ್ಯಗಳಿಗಾಗಿ ಈ ಶಿಫಾರಸು ಮಾಡಲಾದ ಪ್ರಶ್ನೆಗಳನ್ನು ಆಯ್ಕೆಮಾಡಿ'\r\n                              : 'Select from these recommended questions for your financial needs'}\r\n                      </p>\r\n                    </div>\r\n\r\n                    <div className=\"space-y-3\">\r\n                      {recommendedSuggestions.map((suggestion, index) => (\r\n                        <button\r\n                          key={index}\r\n                          onClick={() => {\r\n                            handleSelectSuggestion(suggestion);\r\n                            setShowSuggestions(false);\r\n                          }}\r\n                          className={`w-full text-left px-5 py-4 text-sm rounded-lg border shadow-sm hover:shadow-md transition-all\r\n                            ${selectedLanguage === \"Tamil\"\r\n                              ? 'border-purple-200 bg-white hover:bg-gradient-to-r hover:from-purple-50 hover:to-white hover:border-purple-300'\r\n                              : selectedLanguage === \"Telugu\"\r\n                                ? 'border-green-200 bg-white hover:bg-gradient-to-r hover:from-green-50 hover:to-white hover:border-green-300'\r\n                                : selectedLanguage === \"Kannada\"\r\n                                  ? 'border-orange-200 bg-white hover:bg-gradient-to-r hover:from-orange-50 hover:to-white hover:border-orange-300'\r\n                                  : 'border-blue-200 bg-white hover:bg-gradient-to-r hover:from-blue-50 hover:to-white hover:border-blue-300'}\r\n                            text-gray-700 animate-fadeInUp`}\r\n                          style={{ animationDelay: `${index * 0.05}s`, animationDuration: '0.3s' }}\r\n                        >\r\n                          <div className=\"flex items-start\">\r\n                            <span className={`inline-block p-1.5 rounded-full mr-3 mt-0.5 ${\r\n                              selectedLanguage === \"Tamil\"\r\n                                ? 'bg-gradient-to-br from-purple-100 to-purple-200 text-purple-600'\r\n                                : selectedLanguage === \"Telugu\"\r\n                                  ? 'bg-gradient-to-br from-green-100 to-green-200 text-green-600'\r\n                                  : selectedLanguage === \"Kannada\"\r\n                                    ? 'bg-gradient-to-br from-orange-100 to-orange-200 text-orange-600'\r\n                                    : 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600'\r\n                            }`}>\r\n                              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\" clipRule=\"evenodd\" />\r\n                              </svg>\r\n                            </span>\r\n                            <span className=\"font-medium\">{suggestion}</span>\r\n                          </div>\r\n                        </button>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className={`p-4 border-t rounded-b-xl text-right ${\r\n                    selectedLanguage === \"Tamil\"\r\n                      ? 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-100'\r\n                      : selectedLanguage === \"Telugu\"\r\n                        ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-100'\r\n                        : selectedLanguage === \"Kannada\"\r\n                          ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-100'\r\n                          : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-100'\r\n                  }`}>\r\n                    <button\r\n                      onClick={() => setShowSuggestions(false)}\r\n                      className={`px-5 py-2 rounded-lg text-white font-medium shadow-sm transition-all hover:shadow-md\r\n                        ${selectedLanguage === \"Tamil\"\r\n                          ? 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700'\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'\r\n                              : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'}`}\r\n                    >\r\n                      {selectedLanguage === \"Tamil\"\r\n                        ? 'மூடு'\r\n                        : selectedLanguage === \"Telugu\"\r\n                          ? 'మూసివేయండి'\r\n                          : selectedLanguage === \"Kannada\"\r\n                            ? 'ಮುಚ್ಚಿರಿ'\r\n                            : 'Close'}\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div className=\"flex justify-between items-center gap-4\">\r\n            {/* Language selection - responsive design */}\r\n            <div className=\"flex-grow\">\r\n              {/* Desktop view - horizontal buttons */}\r\n              <div className=\"hidden md:flex md:items-center\">\r\n                {/* <div className=\"flex items-center mr-4\">\r\n                  <PiGlobe className=\"text-gray-600 mr-2 text-lg\" />\r\n                  <span className=\"text-sm font-medium text-gray-700\">Select Language:</span>\r\n                </div> */}\r\n\r\n                <div className=\"flex space-x-4\">\r\n                  {/* Show English, Tamil, and Telugu buttons as prominent language selectors on desktop */}\r\n                  {languages.map((language, index) => {\r\n                    const isSelected = selectedLanguage === language.name;\r\n\r\n                    // Get color classes based on language\r\n                    const getColorClass = () => {\r\n                      if (language.name === \"Tamil\") return \"text-purple-700 border-purple-300 bg-purple-50\";\r\n                      if (language.name === \"Telugu\") return \"text-green-700 border-green-300 bg-green-50\";\r\n                      if (language.name === \"Kannada\") return \"text-orange-700 border-orange-300 bg-orange-50\";\r\n                      return \"text-blue-700 border-blue-300 bg-blue-50\";\r\n                    };\r\n\r\n                    const getHoverClass = () => {\r\n                      if (language.name === \"Tamil\") return \"hover:bg-purple-100 hover:border-purple-400\";\r\n                      if (language.name === \"Telugu\") return \"hover:bg-green-100 hover:border-green-400\";\r\n                      if (language.name === \"Kannada\") return \"hover:bg-orange-100 hover:border-orange-400\";\r\n                      return \"hover:bg-blue-100 hover:border-blue-400\";\r\n                    };\r\n\r\n                    const getIconColor = () => {\r\n                      if (language.name === \"Tamil\") return \"text-purple-600\";\r\n                      if (language.name === \"Telugu\") return \"text-green-600\";\r\n                      if (language.name === \"Kannada\") return \"text-orange-600\";\r\n                      return \"text-blue-600\";\r\n                    };\r\n\r\n                    const getBorderColor = () => {\r\n                      if (language.name === \"Tamil\") return \"bg-purple-500\";\r\n                      if (language.name === \"Telugu\") return \"bg-green-500\";\r\n                      if (language.name === \"Kannada\") return \"bg-orange-500\";\r\n                      return \"bg-blue-500\";\r\n                    };\r\n\r\n                    // Using type=\"button\" to prevent form submission when clicking language buttons\r\n                    return (\r\n                      <button\r\n                        key={index}\r\n                        type=\"button\"\r\n                        onClick={(e) => handleSelectLanguage(e, language.name)}\r\n                        disabled={languageButtonsDisabled || isLoading}\r\n                        className={`px-5 py-2 rounded-lg text-sm font-medium transition-all border relative\r\n                          ${isSelected\r\n                            ? getColorClass()\r\n                            : 'bg-white text-gray-600 border-gray-200 ' + getHoverClass()}\r\n                          transform transition-all duration-300 ease-in-out\r\n                          ${isSelected ? 'scale-105 shadow-md' : 'hover:scale-105 hover:shadow-sm'}\r\n                          ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}\r\n                          animate-fadeIn\r\n                        `}\r\n                        style={{\r\n                          animationDelay: `${index * 0.05}s`,\r\n                          animationDuration: '0.3s'\r\n                        }}\r\n                      >\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <PiGlobe className={`${getIconColor()} text-lg ${isSelected ? 'animate-fadeIn' : ''}`}\r\n                            style={{ animationDuration: '0.3s' }}\r\n                          />\r\n                          <span className=\"font-medium\">{language.name}</span>\r\n                        </div>\r\n\r\n                        {/* Bottom border animation for selected language */}\r\n                        {isSelected && (\r\n                          <div\r\n                            className={`absolute bottom-0 left-0 h-0.5 rounded-full animate-scaleInHorizontal ${getBorderColor()}`}\r\n                            style={{\r\n                              width: '100%',\r\n                              transformOrigin: 'left',\r\n                              animationDuration: '0.4s'\r\n                            }}\r\n                          ></div>\r\n                        )}\r\n\r\n                        {/* Top border animation for selected language */}\r\n                        {isSelected && (\r\n                          <div\r\n                            className={`absolute top-0 right-0 h-0.5 rounded-full animate-scaleInHorizontal ${getBorderColor()}`}\r\n                            style={{\r\n                              width: '100%',\r\n                              transformOrigin: 'right',\r\n                              animationDuration: '0.4s',\r\n                              animationDelay: '0.1s'\r\n                            }}\r\n                          ></div>\r\n                        )}\r\n                      </button>\r\n                    );\r\n                  })}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Mobile view - dropup menu */}\r\n              <div className=\"md:hidden relative\" ref={languageMenuRef}>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setShowLanguageMenu(!showLanguageMenu)}\r\n                  disabled={languageButtonsDisabled || isLoading}\r\n                  className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all\r\n                    ${selectedLanguage === \"Tamil\"\r\n                      ? \"text-purple-700 border-purple-300 bg-purple-50\"\r\n                      : selectedLanguage === \"Telugu\"\r\n                        ? \"text-green-700 border-green-300 bg-green-50\"\r\n                        : selectedLanguage === \"Kannada\"\r\n                          ? \"text-orange-700 border-orange-300 bg-orange-50\"\r\n                          : \"text-blue-700 border-blue-300 bg-blue-50\"\r\n                    }\r\n                    hover:shadow-sm\r\n                    ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}\r\n                  `}\r\n                >\r\n                  <PiGlobe className={`text-lg ${\r\n                    selectedLanguage === \"Tamil\"\r\n                      ? \"text-purple-600\"\r\n                      : selectedLanguage === \"Telugu\"\r\n                        ? \"text-green-600\"\r\n                        : selectedLanguage === \"Kannada\"\r\n                          ? \"text-orange-600\"\r\n                          : \"text-blue-600\"\r\n                  }`} />\r\n                  <span className=\"font-medium text-sm\">{selectedLanguage}</span>\r\n                  <span className={`transition-transform duration-300 ${showLanguageMenu ? 'rotate-180' : ''}`}>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 15l7-7 7 7\" />\r\n                    </svg>\r\n                  </span>\r\n                </button>\r\n\r\n                {/* Dropup menu */}\r\n                {showLanguageMenu && (\r\n                  <div className=\"absolute bottom-full left-0 mb-1 w-full bg-white dark:bg-n0 rounded-lg border shadow-lg z-[40] overflow-hidden animate-fadeIn\"\r\n                    style={{ animationDuration: '0.2s' }}\r\n                  >\r\n                    {languages.map((language, index) => {\r\n                      const isSelected = selectedLanguage === language.name;\r\n\r\n                      // Get color classes based on language\r\n                      const getItemColorClass = () => {\r\n                        if (language.name === \"Tamil\") return isSelected ? \"bg-purple-50 text-purple-700\" : \"hover:bg-purple-50\";\r\n                        if (language.name === \"Telugu\") return isSelected ? \"bg-green-50 text-green-700\" : \"hover:bg-green-50\";\r\n                        if (language.name === \"Kannada\") return isSelected ? \"bg-orange-50 text-orange-700\" : \"hover:bg-orange-50\";\r\n                        return isSelected ? \"bg-blue-50 text-blue-700\" : \"hover:bg-blue-50\";\r\n                      };\r\n\r\n                      const getIconColor = () => {\r\n                        if (language.name === \"Tamil\") return \"text-purple-600\";\r\n                        if (language.name === \"Telugu\") return \"text-green-600\";\r\n                        if (language.name === \"Kannada\") return \"text-orange-600\";\r\n                        return \"text-blue-600\";\r\n                      };\r\n\r\n                      return (\r\n                        <button\r\n                          key={index}\r\n                          type=\"button\"\r\n                          onClick={(e) => {\r\n                            handleSelectLanguage(e, language.name);\r\n                            setShowLanguageMenu(false);\r\n                          }}\r\n                          disabled={languageButtonsDisabled || isLoading}\r\n                          className={`w-full flex items-center gap-2 px-4 py-3 text-left text-sm ${getItemColorClass()} transition-colors\r\n                            ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}\r\n                          `}\r\n                          style={{\r\n                            animationDelay: `${index * 0.05}s`,\r\n                          }}\r\n                        >\r\n                          <PiGlobe className={`${getIconColor()} text-lg`} />\r\n                          <span className=\"font-medium\">{language.name}</span>\r\n                          {isSelected && (\r\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 ml-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                            </svg>\r\n                          )}\r\n                        </button>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Action buttons container with improved spacing */}\r\n            <div className=\"flex items-center gap-4\">\r\n              {/* Microphone button with enhanced alignment - hide when upload is active or dropdown is visible */}\r\n              {!uploadIsActive && !uploadDropdownVisible && (\r\n                <div className=\"relative z-[3] flex items-center justify-center\">\r\n                  {browserSupportsSpeechRecognition ? (\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={isListening ? toggleListening : toggleListening}\r\n                    className={`p-3 rounded-full flex justify-center items-center border-2 transition-all duration-200 shadow-md hover:shadow-lg relative z-[3] min-w-[48px] min-h-[48px]\r\n                      ${isListening\r\n                        ? 'border-red-500 bg-gradient-to-r from-red-50 to-red-100 hover:from-red-100 hover:to-red-200 scale-105'\r\n                        : selectedLanguage === \"Tamil\"\r\n                          ? 'border-purple-400 bg-white hover:bg-purple-50 hover:border-purple-500 hover:scale-105'\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? 'border-green-400 bg-white hover:bg-green-50 hover:border-green-500 hover:scale-105'\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? 'border-orange-400 bg-white hover:bg-orange-50 hover:border-orange-500 hover:scale-105'\r\n                              : 'border-blue-400 bg-white hover:bg-blue-50 hover:border-blue-500 hover:scale-105'\r\n                      }\r\n                      dark:bg-n0 transform`}\r\n                    title={isListening ? \"Stop voice recording\" : `Start voice recording in ${selectedLanguage}`}\r\n                  >\r\n                    {isListening ? (\r\n                      <div className=\"relative flex items-center justify-center\">\r\n                        <div\r\n                          className=\"absolute -inset-2 bg-red-200 rounded-full opacity-60\"\r\n                          style={{\r\n                            animationName: 'pulse',\r\n                            animationDuration: '1.5s',\r\n                            animationIterationCount: 'infinite',\r\n                            animationDirection: 'alternate'\r\n                          }}\r\n                        ></div>\r\n                        <PiStop className=\"text-red-600 relative z-[4] w-5 h-5\" />\r\n                        <span\r\n                          className=\"absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500\"\r\n                          style={{\r\n                            animationName: 'ping',\r\n                            animationDuration: '1.5s',\r\n                            animationIterationCount: 'infinite'\r\n                          }}\r\n                        ></span>\r\n                      </div>\r\n                    ) : (\r\n                      <PiMicrophone className={`w-5 h-5 ${\r\n                        selectedLanguage === \"Tamil\"\r\n                          ? 'text-purple-600'\r\n                          : selectedLanguage === \"Telugu\"\r\n                            ? 'text-green-600'\r\n                            : selectedLanguage === \"Kannada\"\r\n                              ? 'text-orange-600'\r\n                              : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                  </button>\r\n                ) : (\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"bg-white p-3 rounded-full flex justify-center items-center border-2 border-gray-300 text-gray-400 cursor-not-allowed min-w-[48px] min-h-[48px] shadow-md\"\r\n                    title=\"Speech recognition not supported in this browser\"\r\n                    disabled\r\n                  >\r\n                    <PiMicrophone className=\"w-5 h-5\" />\r\n                  </button>\r\n                )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Submit button with enhanced alignment */}\r\n              <button\r\n                type=\"submit\"\r\n                className={`rounded-full flex justify-center items-center border-2 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 min-h-[48px]\r\n                  ${selectedLanguage === \"Tamil\"\r\n                    ? \"px-5 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 border-purple-600\"\r\n                    : selectedLanguage === \"Telugu\"\r\n                      ? \"px-5 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 border-green-600\"\r\n                      : selectedLanguage === \"Kannada\"\r\n                        ? \"px-5 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 border-orange-600\"\r\n                        : \"px-5 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-blue-600\"}\r\n                  ${isLoading ? 'opacity-80 cursor-not-allowed scale-100' : ''}`}\r\n                disabled={isLoading}\r\n                title={\r\n                  isLoading\r\n                    ? selectedLanguage === \"Tamil\"\r\n                      ? \"அனுப்புகிறது...\"\r\n                      : selectedLanguage === \"Telugu\"\r\n                        ? \"పంపుతోంది...\"\r\n                        : selectedLanguage === \"Kannada\"\r\n                          ? \"ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ...\"\r\n                          : \"Sending...\"\r\n                    : selectedLanguage === \"Tamil\"\r\n                      ? \"கேள்வியை அனுப்பு\"\r\n                      : selectedLanguage === \"Telugu\"\r\n                        ? \"ప్రశ్నను పంపండి\"\r\n                        : selectedLanguage === \"Kannada\"\r\n                          ? \"ಪ್ರಶ್ನೆಯನ್ನು ಕಳುಹಿಸಿ\"\r\n                          : \"Send question\"\r\n                }\r\n              >\r\n                {isLoading ? (\r\n                  // Loading state with spinner\r\n                  selectedLanguage === \"Tamil\" ? (\r\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                      அனுப்புகிறது... <PiSpinner className=\"animate-spin\" />\r\n                    </span>\r\n                  ) : selectedLanguage === \"Telugu\" ? (\r\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                      పంపుతోంది... <PiSpinner className=\"animate-spin\" />\r\n                    </span>\r\n                  ) : selectedLanguage === \"Kannada\" ? (\r\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                      ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ... <PiSpinner className=\"animate-spin\" />\r\n                    </span>\r\n                  ) : (\r\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                      Sending... <PiSpinner className=\"animate-spin\" />\r\n                    </span>\r\n                  )\r\n                ) : (\r\n                  // Normal state\r\n                  selectedLanguage === \"Tamil\" ? (\r\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                      அனுப்பு <PiArrowUp />\r\n                    </span>\r\n                  ) : selectedLanguage === \"Telugu\" ? (\r\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                      పంపండి <PiArrowUp />\r\n                    </span>\r\n                  ) : selectedLanguage === \"Kannada\" ? (\r\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                      ಕಳುಹಿಸಿ <PiArrowUp />\r\n                    </span>\r\n                  ) : (\r\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\r\n                      Send <PiArrowUp />\r\n                    </span>\r\n                  )\r\n                )}\r\n              </button>\r\n            </div>\r\n\r\n            {/* Debug button - only visible in development */}\r\n            {/* {process.env.NODE_ENV === 'development' && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setShowConnectionTest(true)}\r\n                className=\"text-xs px-2 py-1 bg-blue-100 border border-blue-300 rounded text-blue-600 hover:bg-blue-200 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-800\"\r\n                title=\"Test backend connection\"\r\n              >\r\n                🔌 Test Connection\r\n              </button>\r\n            )} */}\r\n          </div>\r\n        </div>\r\n      </form>\r\n\r\n      {/* Add style for animation and z-index fixes */}\r\n      <style jsx>{`\r\n        @keyframes pulse {\r\n          0% { height: 4px; }\r\n          100% { height: 16px; }\r\n        }\r\n\r\n        @keyframes spin {\r\n          0% { transform: rotate(0deg); }\r\n          100% { transform: rotate(360deg); }\r\n        }\r\n\r\n        /* Spinner animation */\r\n        .animate-spin {\r\n          animation: spin 1s linear infinite;\r\n        }\r\n\r\n        /* Ensure microphone elements are above other UI elements */\r\n        .relative.z-50 {\r\n          position: relative;\r\n          z-index: 50;\r\n        }\r\n\r\n        /* Fix for overlapping elements */\r\n        .absolute.z-50 {\r\n          position: absolute;\r\n          z-index: 50;\r\n        }\r\n\r\n        /* Ensure input is always clickable */\r\n        input {\r\n          position: relative;\r\n          z-index: 30;\r\n        }\r\n\r\n        /* Style for editable transcript textarea */\r\n        textarea {\r\n          font-family: inherit;\r\n          line-height: 1.5;\r\n          transition: all 0.2s ease-in-out;\r\n        }\r\n\r\n        /* Animation for edit button */\r\n        button:hover svg {\r\n          transform: scale(1.1);\r\n          transition: transform 0.2s ease-in-out;\r\n        }\r\n\r\n        /* Dropup animations */\r\n        @keyframes fadeIn {\r\n          from { opacity: 0; transform: translateY(10px); }\r\n          to { opacity: 1; transform: translateY(0); }\r\n        }\r\n\r\n        .animate-fadeIn {\r\n          animation: fadeIn 0.2s ease-out forwards;\r\n        }\r\n\r\n        /* Scale in animation for horizontal borders */\r\n        @keyframes scaleInHorizontal {\r\n          from { transform: scaleX(0); }\r\n          to { transform: scaleX(1); }\r\n        }\r\n\r\n        .animate-scaleInHorizontal {\r\n          animation: scaleInHorizontal 0.4s ease-out forwards;\r\n        }\r\n\r\n        /* Additional animations for upload components */\r\n        @keyframes fadeInUp {\r\n          from { opacity: 0; transform: translateY(20px); }\r\n          to { opacity: 1; transform: translateY(0); }\r\n        }\r\n\r\n        .animate-fadeInUp {\r\n          animation: fadeInUp 0.3s ease-out forwards;\r\n        }\r\n\r\n        @keyframes scaleIn {\r\n          from { opacity: 0; transform: scale(0.95); }\r\n          to { opacity: 1; transform: scale(1); }\r\n        }\r\n\r\n        .animate-scaleIn {\r\n          animation: scaleIn 0.2s ease-out forwards;\r\n        }\r\n      `}</style>\r\n\r\n      {/* Connection Test Dialog */}\r\n      {showConnectionTest && (\r\n        <ConnectionTest onClose={() => setShowConnectionTest(false)} />\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nChatBox.displayName = 'ChatBox';\r\n\r\nexport default ChatBox;\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAqBA;AACA;AACA,2CAA2C;AAC3C;AACA,qEAAqE;AACrE,yCAAyC;AACzC;AACA;AACA;AACA,gDAAgD;AAChD;AA7BA;AADA;;;;;;;;;;;;;;AA0CA,MAAM,wBAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAA4B,CAAC,EAAE,gBAAgB,EAAE,EAAE;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,kDAAkD;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,oCAAoC;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,mCAAmC;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,oCAAoC;IACpC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,4DAA4D;IAC5D,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,6CAA6C;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,2DAA2D;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IACnF,gCAAgC;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,mDAAmD;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,6CAA6C;IAC7C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC5E,yFAAyF;IACzF,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;QAC9E,uCAAmC;;QAInC;QACA,OAAO;IACT;IACA,4CAA4C;IAC5C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC5E,uCAAuC;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEtE,8BAA8B;IAC9B,MAAM,0BAA0B,CAAC,UAAmB;QAClD,kBAAkB;QAClB,yBAAyB;IAC3B;IACA,yCAAyC;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqD,EAAE;IACtG,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACxE,sCAAsC;IACtC,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACtF,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/E,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAChD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,8CAA8C;IAC9C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC/C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC7C,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAC1D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE9G,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,QAAQ,KAAK,QAAQ,CAAC,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG;QAC9E,IAAI,WAAW;YACb,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACtD,IAAI,eAAe,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC1E,QAAQ,GAAG,CAAC;gBACZ,2BAA2B;gBAC3B,uCAAmC;;gBAEnC;YACF;QACF;IACF,GAAG;QAAC;QAAU;KAAK;IAEnB,yBAAyB;IACzB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEvD,iFAAiF;IACjF,MAAM,4BAA4B,OAAO;QACvC,QAAQ,GAAG,CAAC,sDAAsD;QAClE,MAAM,oBAAoB;IAC5B;IAEA,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAM,CAAC;YAC9B,sBAAsB,CAAC;gBACrB,QAAQ,GAAG,CAAC,iDAAiD;gBAC7D,QAAQ,GAAG,CAAC,wCAAwC;gBAEpD,8BAA8B;gBAC9B,aAAa;gBACb,aAAa;gBAEb,+DAA+D;gBAC/D,sBAAsB;oBACpB,MAAM,eAAe,SAAS,aAAa,CAAC;oBAC5C,IAAI,cAAc;wBAChB,QAAQ,GAAG,CAAC;wBACZ,8CAA8C;wBAC9C,aAAa,KAAK,GAAG;wBACrB,kCAAkC;wBAClC,MAAM,aAAa,IAAI,MAAM,SAAS;4BAAE,SAAS;wBAAK;wBACtD,aAAa,aAAa,CAAC;wBAC3B,mCAAmC;wBACnC,MAAM,cAAc,IAAI,MAAM,UAAU;4BAAE,SAAS;wBAAK;wBACxD,aAAa,aAAa,CAAC;wBAC3B,kBAAkB;wBAClB,aAAa,KAAK;wBAClB,4BAA4B;wBAC5B,aAAa,iBAAiB,CAAC,SAAS,MAAM,EAAE,SAAS,MAAM;wBAC/D,QAAQ,GAAG,CAAC;wBAEZ,4DAA4D;wBAC5D,WAAW;4BACT,QAAQ,GAAG,CAAC;4BACZ,QAAQ,GAAG,CAAC,gCAAgC,SAAS,IAAI;4BACzD,QAAQ,GAAG,CAAC,0BAA0B;4BACtC,QAAQ,GAAG,CAAC,8CAA8C,SAAS,IAAI,MAAM,CAAC;4BAE9E,IAAI,SAAS,IAAI,MAAM,CAAC,WAAW;gCACjC,QAAQ,GAAG,CAAC;gCACZ,+DAA+D;gCAC/D,MAAM,gBAAgB,IAAI,cAAc,WAAW;oCACjD,KAAK;oCACL,MAAM;oCACN,SAAS;oCACT,OAAO;oCACP,SAAS;oCACT,YAAY;gCACd;gCACA,aAAa,aAAa,CAAC;gCAC3B,QAAQ,GAAG,CAAC;4BACd,OAAO;gCACL,QAAQ,IAAI,CAAC;gCACb,QAAQ,IAAI,CAAC,+BAA+B,SAAS,IAAI;gCACzD,QAAQ,IAAI,CAAC,yBAAyB;4BACxC;wBACF,GAAG,MAAM,iEAAiE;oBAC5E,OAAO;wBACL,QAAQ,IAAI,CAAC;oBACf;gBACF;YACF;QACF,CAAC;IAID,sGAAsG;IACtG,MAAM,YAAY;QAChB;YAAE,MAAM;YAAW,MAAM;YAAS,OAAO;QAAO;QAChD;YAAE,MAAM;YAAS,MAAM;YAAS,OAAO;QAAS;QAChD;YAAE,MAAM;YAAU,MAAM;YAAS,OAAO;QAAQ;QAChD;YAAE,MAAM;YAAW,MAAM;YAAS,OAAO;QAAS;KACnD;IAED,kDAAkD;IAClD,MAAM,kBAAkB;QACtB,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QACtD,OAAO,WAAW,SAAS,IAAI,GAAG,SAAS,kCAAkC;IAC/E;IAEA,2DAA2D;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kCAAkC,oCAAoC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzF,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,qDAAqD;IACrD,MAAM,EACJ,YAAY,gBAAgB,EAC5B,WAAW,eAAe,EAC1B,eAAe,EACf,kCAAkC,oBAAoB,EACtD,uBAAuB,yBAAyB,EAIjD,GAAG,6EAWC;QACH,YAAY;QACZ,WAAW;QACX,iBAAiB,KAAO;QACxB,kCAAkC;QAClC,uBAAuB;IACzB;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,gDAAgD;YAChD,IAAI,eAAe,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBACpF,mBAAmB;YACrB;YAEA,yCAAyC;YACzC,IAAI,gBAAgB,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBACtF,oBAAoB;YACtB;YAEA,0CAA0C;YAC1C,IAAI,iBAAiB,OAAO,IAAI,CAAC,iBAAiB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBACxF,qBAAqB;YACvB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,OAAO,IAAI,YAAY;YACvC,cAAc,OAAO,CAAC,SAAS,GAAG,cAAc,OAAO,CAAC,YAAY;QACtE;IACF,GAAG;QAAC;KAAW;IAEf,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,MAAM,QAAQ,WAAW,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,SAAS;YACrE,aAAa,MAAM,MAAM;YAEzB,wCAAwC;YACxC,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,MAAM,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;gBACxC,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;oBACnC,eAAe,CAAC;wBACd,MAAM,WAAW;+BAAI;4BAAM;yBAAS;wBACpC,OAAO,SAAS,KAAK,CAAC,CAAC,IAAI,6BAA6B;oBAC1D;gBACF;YACF;YAEA,iDAAiD;YACjD,IAAI,aAAa;gBACf,YAAY;gBAEZ,oCAAoC;gBACpC,IAAI,iBAAiB,OAAO,EAAE;oBAC5B,aAAa,iBAAiB,OAAO;gBACvC;gBAEA,iDAAiD;gBACjD,iBAAiB,OAAO,GAAG,WAAW;oBACpC,YAAY;gBACd,GAAG,OAAO,+CAA+C;YAC3D;QACF;IACF,GAAG;QAAC;QAAY;KAAY;IAE5B,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0DAA0D;QAC1D,IAAI,aAAa,aAAa;YAC5B,aAAa;YACb,aAAa;YAEb,oEAAoE;YACpE,+EAA+E;YAC/E,oBAAoB;YAEpB,QAAQ,GAAG,CAAC,sBAAsB;YAElC,IAAI,cAAc,CAAC,aAAa;gBAC9B,QAAQ,GAAG,CAAC;gBACZ,eAAe;YACjB;QACF;IACF,GAAG;QAAC;QAAY;QAAW;QAAa;KAAa;IAErD,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,IAAI,cAAc,IAAI;gBACpB,aAAa;gBACb,aAAa;YACf;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,kCAAkC;YAE9C,IAAI,cAAc,WAAW,IAAI,OAAO,IAAI;gBAC1C,aAAa;gBACb,aAAa;YACf,OAAO,uCAAmC;;YAE1C;YAEA,4CAA4C;YAC5C,YAAY;YACZ,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;gBACrC,iBAAiB,OAAO,GAAG;YAC7B;QAEA,4CAA4C;QAC5C,kCAAkC;QACpC;QAEA,QAAQ,GAAG,CAAC,4BAA4B;IAC1C,GAAG;QAAC;QAAa;QAAY;KAAU;IAEvC,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC,QAAQ,sBAAsB;;IA6BnE,GAAG;QAAC;QAAW;KAAY;IAE3B,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC,QAAQ,sBAAsB;;IA4BnE,GAAG;QAAC;KAAiB;IAErB,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QACJ,IAAI,uBAAuB;YACzB,QAAQ,WAAW;gBACjB,yBAAyB;YAC3B,GAAG;QACL;QACA,OAAO;YACL,IAAI,OAAO,aAAa;QAC1B;IACF,GAAG;QAAC;KAAsB;IAE1B,iCAAiC;IACjC,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;KACD;IAED,8BAA8B;IAC9B,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;KACD;IAED,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB;QACA;QACA;QACA;QACA;KACD;IAED,gCAAgC;IAChC,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;KACD;IAED,6CAA6C;IAC7C,MAAM,2BAA2B;QAC/B,OAAO;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,yBAAyB;IAE/B,2BAA2B;IAC3B,MAAM,YAAY,QAAQ,KAAK,QAAQ,CAAC,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG;IAE9E,gCAAgC;IAChC,MAAM,yBAAyB,CAAC;QAC9B,aAAa;QACb,aAAa;QACb,mBAAmB;IACrB;IAEA,6CAA6C;IAC7C,MAAM,cAAc,CAAC;QACnB,qCAAqC;QACrC,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,8CAA8C;IAC9C,MAAM,eAAe,CAAC;QACpB,sCAAsC;QACtC,MAAM,cAAc;QACpB,OAAO,YAAY,IAAI,CAAC;IAC1B;IAEA,+CAA+C;IAC/C,MAAM,gBAAgB,CAAC;QACrB,uCAAuC;QACvC,MAAM,eAAe;QACrB,OAAO,aAAa,IAAI,CAAC;IAC3B;IAEA,6DAA6D;IAC7D,MAAM,wBAAwB,CAAC,MAAc;QAC3C,IAAI,CAAC,QAAQ,KAAK,IAAI,OAAO,IAAI,OAAO,MAAM,uCAAuC;QAErF,gFAAgF;QAChF,sEAAsE;QACtE,MAAM,0BAA0B,KAAK,OAAO,CAAC,kBAAkB;QAE/D,2EAA2E;QAC3E,MAAM,gBAAgB,YAAY;QAClC,MAAM,iBAAiB,aAAa;QACpC,MAAM,kBAAkB,cAAc;QAEtC,wDAAwD;QACxD,MAAM,cAAc,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC;QAEjG,wDAAwD;QACxD,IAAI,cAAc,GAAG;YACnB,OAAO;QACT;QAEA,gFAAgF;QAChF,IAAI,aAAa,WAAW;YAC1B,+FAA+F;YAC/F,OAAO,CAAC,CAAC,iBAAiB,kBAAkB,eAAe;QAC7D,OAAO,IAAI,aAAa,SAAS;YAC/B,gDAAgD;YAChD,OAAO,iBAAiB,wBAAwB,IAAI,OAAO;QAC7D,OAAO,IAAI,aAAa,UAAU;YAChC,kDAAkD;YAClD,OAAO,kBAAkB,wBAAwB,IAAI,OAAO;QAC9D,OAAO,IAAI,aAAa,WAAW;YACjC,oDAAoD;YACpD,OAAO,mBAAmB,wBAAwB,IAAI,OAAO;QAC/D;QAEA,OAAO,MAAM,eAAe;IAC9B;IAGA,sEAAsE;IACtE,MAAM,uBAAuB;QAC3B,OAAO;YACL,KAAK;gBACH,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;gBACf;YACF;gBACE,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;gBACf;QACJ;IACF;IAEA,+CAA+C;IAC/C,MAAM,cAAc,CAAC;QACnB,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;QAE7C,iBAAiB;QACjB,IAAI;YAAC;YAAO;YAAO;YAAQ;YAAO;YAAO;SAAM,CAAC,QAAQ,CAAC,aAAa,KAAK;YACzE,qBAAO,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC/B;QAEA,cAAc;QACd,IAAI;YAAC;YAAO;YAAO;YAAO;YAAO;YAAQ;YAAO;SAAM,CAAC,QAAQ,CAAC,aAAa,KAAK;YAChF,qBAAO,8OAAC,8IAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,oBAAoB;QACpB,qBAAO,8OAAC,8IAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IAEA,yCAAyC;IACzC,MAAM,aAAa,CAAC;QAClB,IAAI,SAAS,WAAW;YACtB,qBAAO,8OAAC,8IAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAClC;QACA,qBAAO,8OAAC,8IAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACrD,IAAI,cAAc,MAAM,KAAK,KAAK,aAAa,MAAM,KAAK,GAAG;YAC3D,uBAAuB;QACzB;IACF;IAEA,kCAAkC;IAClC,MAAM,oBAAoB,CAAC;QACzB,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACpD,IAAI,aAAa,MAAM,KAAK,KAAK,cAAc,MAAM,KAAK,GAAG;YAC3D,uBAAuB;QACzB;IACF;IAEA,qDAAqD;IACrD,MAAM,uBAAuB,OAAO,GAAqB;QACvD,oEAAoE;QACpE,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,oBAAoB;QACpB,kDAAkD;QAClD,oBAAoB;QAEpB,8DAA8D;QAC9D,IAAI,eAAe;YACjB,iBAAiB;QACnB;QAEA,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,UAAU;QAE1C,wEAAwE;QACxE,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;QAEA,sEAAsE;QACtE,IAAI,iBAAiB;YACnB,mBAAmB;YACnB,WAAW,IAAM,mBAAmB,OAAO;QAC7C;QAEA,oDAAoD;QACpD,uCAAkD;;QAUlD;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB;QACtB,sBAAsB;QACtB,wCAAmC;YACjC,QAAQ,IAAI,CAAC;YACb;QACF;;IAiBF;IAEA,wDAAwD;IACxD,MAAM,2BAA2B;QAC/B,IAAI,qBAAqB;YACvB,6BAA6B;YAC7B,IAAI,iBAAiB,IAAI,OAAO,IAAI;gBAClC,aAAa;gBACb,aAAa;YACf;YACA,uBAAuB;QACzB,OAAO;YACL,kBAAkB;YAClB,uBAAuB;YAEvB,0EAA0E;YAC1E,WAAW;gBACT,IAAI,sBAAsB,OAAO,EAAE;oBACjC,sBAAsB,OAAO,CAAC,KAAK;gBACrC;YACF,GAAG;QACL;IACF;IAEA,4CAA4C;IAC5C,MAAM,yBAAyB,CAAC;QAC9B,oBAAoB,EAAE,MAAM,CAAC,KAAK;QAElC,oBAAoB;QACpB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,SAAS;QACzE,aAAa,MAAM,MAAM;QAEzB,8DAA8D;QAC9D,IAAI,eAAe;YACjB,iBAAiB;QACnB;IACF;IAEA,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,sBAAsB;QACtB,wCAAmC;YACjC,QAAQ,IAAI,CAAC;YACb;QACF;;QAYA,MAAM;IA8CR;IAWA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,mDAAmD;QACnD,gEAAgE;QAChE,IAAI,aAAa,sBAAsB,mBAAoB,cAAc,aAAa;QAEtF,mEAAmE;QACnE,qDAAqD;QACrD,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,IAAI;YACrC,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,IAAI,gBAAgB,aAAa,KAAK,IAAI,aAAa,KAAK,CAAC,IAAI,IAAI;gBACnE,QAAQ,GAAG,CAAC,sDAAsD,aAAa,KAAK;gBACpF,aAAa,aAAa,KAAK;YACjC;QACF;QAEA,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,IAAI;YACrC,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC,0CAA0C;QAEtD,gDAAgD;QAChD,MAAM,oBAAoB;IAC5B;IAEA,2DAA2D;IAC3D,MAAM,sBAAsB,OAAO;QACjC,6DAA6D;QAC7D,IAAI,CAAC,sBAAsB,YAAY,mBAAmB;YACxD,iBAAiB;YACjB;QACF;QAEA,qCAAqC;QACrC,iBAAiB;QAEjB,wCAAwC;QACxC,IAAI,qBAAqB;YACvB,uBAAuB;QACzB;QAEA,uCAAkD;;QAKlD;QAEA,kCAAkC;QAClC,oBAAoB;QACpB,mBAAmB;QAEnB,mDAAmD;QACnD,2BAA2B;QAE3B,6DAA6D;QAC7D,2BAA2B;QAC3B,uCAAmC;;QAEnC;QAEA,aAAa;QAEb,MAAM,gBAAgB,aAAa,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;QAExC,IAAI,CAAC,WAAW;YACd,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,eAAe;QACtC;QAEA,MAAM,YAAY,WAAW,IAAI;QAEjC,sCAAsC;QACtC,MAAM,UAAU,YAAY,cAAc,qBAAqB;QAC/D,MAAM,WAAW,aAAa,cAAc,qBAAqB;QACjE,MAAM,YAAY,cAAc,cAAc,qBAAqB;QAEnE,2CAA2C;QAC3C,IAAI,SAAS;YACX,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,iBAAiB,kBAAkB,EAAE,YAAY,YAAY;QACrH;QAEA,2FAA2F;QAC3F,mCAAmC;QACnC,IAAI,kBAAkB;QACtB,IAAI,mBAAmB;QAEvB,qDAAqD;QACrD,yEAAyE;QAEzE,yEAAyE;QACzE,MAAM,sBAAsB,UAAU,KAAK,CAAC,qBAAqB,EAAE;QACnE,MAAM,eAAe,oBAAoB,GAAG,CAAC,CAAC,OAAiB,CAAC;gBAC9D;gBACA,aAAa,CAAC,eAAe,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,CAAC;YAChF,CAAC;QAED,oEAAoE;QACpE,IAAI,wBAAwB;QAC5B,aAAa,OAAO,CAAC,CAAC;YACpB,wBAAwB,sBAAsB,OAAO,CAAC,KAAK,IAAI,EAAE,KAAK,WAAW;QACnF;QAEA,IAAI,SAAS;YACX,sEAAsE;YACtE,mBAAmB;YAEnB,6BAA6B;YAC7B,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,gEAAgE,EAAE,sBAAsB,CAAC,CAAC;gBACvG,kBAAkB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,uBAAuB,MAAM;gBAEtF,+CAA+C;gBAC/C,aAAa,OAAO,CAAC,CAAA;oBACnB,kBAAkB,gBAAgB,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;gBACvE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAClD;QACF,OAAO,IAAI,UAAU;YACnB,uEAAuE;YACvE,mBAAmB;YAEnB,8BAA8B;YAC9B,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,iEAAiE,EAAE,sBAAsB,CAAC,CAAC;gBACxG,kBAAkB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,uBAAuB,MAAM;gBAEtF,+CAA+C;gBAC/C,aAAa,OAAO,CAAC,CAAA;oBACnB,kBAAkB,gBAAgB,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;gBACvE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD;QACF,OAAO,IAAI,WAAW;YACpB,wEAAwE;YACxE,mBAAmB;YAEnB,+BAA+B;YAC/B,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,kEAAkE,EAAE,sBAAsB,CAAC,CAAC;gBACzG,kBAAkB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,uBAAuB,MAAM;gBAEtF,+CAA+C;gBAC/C,aAAa,OAAO,CAAC,CAAA;oBACnB,kBAAkB,gBAAgB,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;gBACvE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,UAAU,WAAW,WAAW,YAAY,YAAY,WAAW;QAE9G,aAAa;QACb,aAAa;QACb;QACA,aAAa;QACb,eAAe,EAAE;QAEjB,oGAAoG;QACpG,kGAAkG;QAClG,uCAAuC;QAEvC,MAAM,uBAAuB,IAAI,OAAO,WAAW;QAEnD,yEAAyE;QACzE,MAAM,uBAAuB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACtD,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,cAAc,KAAK,YAAY;YACjC,CAAC;QAED,WAAW;YACT,QAAQ;YACR,MAAM;YACN,WAAW;YACX,eAAe,qBAAqB,MAAM,GAAG,IAAI,uBAAuB;YACxE,cAAc,aAAa,MAAM,GAAG,IAAI,eAAe;QACzD,GAAG;QAEH,MAAM,0BAA0B,IAAI,OAAO,WAAW;QACtD,MAAM,mBAAmB,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,KAAK,GAAG,IAAI;QAEjE,WAAW;YACT,QAAQ;YACR,MAAM;YACN,WAAW;YACX,WAAW;QACb,GAAG;QAEH,IAAI;YACF,wEAAwE;YACxE,MAAM,cAAc,mBAAmB,kBAAkB;YAEzD,yDAAyD;YACzD,MAAM,iBAAiB,6EAA2E;YAClG,MAAM,kBAAkB,6EAA4E;YACpG,MAAM,mBAAmB,6EAA6E;YAEtG,6EAA6E;YAC7E,MAAM,cAAmB;gBACvB,OAAO;gBACP,UAAU,iBAAkB,4CAA4C;YAC1E;YAEA,gCAAgC;YAChC,IAAI,WAAW;gBACb,YAAY,YAAY,GAAG;gBAC3B,YAAY,UAAU,GAAG,WAAW,6CAA6C;gBACjF,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,WAAW;YAC7D;YAEA,yCAAyC;YACzC,MAAM,aAAa,iBAAiB;YACpC,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,eAAe;YACzD,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,gBAAgB;YAClE,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,YAAY;YAElD,4CAA4C;YAC5C,IAAI,cAAc,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,GAAG;gBACvD,MAAM,gBAAgB,EAAE;gBAExB,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,cAAc,IAAI,CAAC,CAAC,yBAAyB,EAAE,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO;gBAC5F;gBAEA,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,cAAc,IAAI,CAAC,CAAC,yBAAyB,EAAE,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO;gBAC1F;gBAEA,YAAY,cAAc,GAAG,cAAc,IAAI,CAAC;gBAChD,YAAY,kBAAkB,GAAG;gBACjC,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,cAAc,EAAE;YAC1E;YAEA,yDAAyD;YACzD,IAAI,YAAY;gBACd,YAAY,UAAU,GAAG;gBACzB,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,WAAW,CAAC,CAAC;YAC9E,OAEK;gBACH,YAAY,UAAU,GAAG;gBACzB,QAAQ,GAAG,CAAC,CAAC,mDAAmD,CAAC;gBACjE,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;YACjE;YAEA,2DAA2D;YAC3D,MAAM,gBAAgB,8JAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YACxD,MAAM,iBAAiB,qBAAqB,UAAU,OACjC,qBAAqB,WAAW,OAChC,qBAAqB,YAAY,OAAO;YAE7D,oFAAoF;YACpF,MAAM,eAAe,GAAG,YAAY,UAAU,IAAI,UAAU,CAAC,EAAE,aAAa,YAAY,CAAC,EAAE,YAAY,cAAc,IAAI,IAAI;YAC7H,MAAM,iBAAiB,MAAM,wJAAA,CAAA,eAAY,CAAC,0BAA0B,CAAC,aAAa,cAAc;YAEhG,IAAI;YACJ,IAAI,gBAAgB;gBAClB,4EAA4E;gBAC5E,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,YAAY,SAAS,CAAC,GAAG,IAAI,gBAAgB,EAAE,eAAe,CAAC,CAAC;gBAEnH,OAAO;oBACL,aAAa,eAAe,WAAW;oBACvC,mBAAmB,eAAe,iBAAiB;oBACnD,mBAAmB,eAAe,iBAAiB;oBACnD,kBAAkB,eAAe,gBAAgB;oBACjD,kBAAkB,eAAe,gBAAgB;oBACjD,sBAAsB,eAAe,oBAAoB;oBACzD,gBAAgB,eAAe,cAAc;oBAC7C,qBAAqB,eAAe,mBAAmB;oBACvD,gBAAgB,eAAe,cAAc;gBAC/C;gBAEA,QAAQ,GAAG,CAAC,CAAC,6CAA6C,CAAC;YAC7D,OAAO;gBACL,+BAA+B;gBAC/B,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,YAAY,SAAS,CAAC,GAAG,IAAI,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;gBAEhH,kDAAkD;gBAClD,IAAI,mBAAmB,MAAM;oBAC3B,YAAY,eAAe,GAAG;oBAC9B,YAAY,kBAAkB,GAAG;gBACnC;gBAEA,OAAO,MAAM,sJAAA,CAAA,aAAU,CAAC,SAAS,CAAC;gBAElC,sEAAsE;gBACtE,IAAI,CAAC,KAAK,mBAAmB,IAAI,mBAAmB,MAAM;oBACxD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,gBAAgB;oBACtE,OAAO,MAAM,8JAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC,MAAM;gBAC1D;gBAEA,0DAA0D;gBAC1D,wJAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC,aAAa,MAAM,cAAc;YAClE;YACA,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,yCAAyC;YACzC,IAAI,KAAK,iBAAiB,EAAE;gBAC1B,QAAQ,GAAG,CAAC,4CAA4C,KAAK,iBAAiB;YAChF,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;YAEA,oEAAoE;YACpE,IAAI,AAAC,KAAa,gBAAgB,IAAI,MAAM,OAAO,CAAC,AAAC,KAAa,gBAAgB,KAAK,AAAC,KAAa,gBAAgB,CAAC,MAAM,GAAG,GAAG;gBAChI,QAAQ,GAAG,CAAC,uCAAuC,AAAC,KAAa,gBAAgB;gBACjF,4DAA4D;gBAC5D,mBAAmB,AAAC,KAAa,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAa,IAAI,UAAU,IAAI;YACxF;YAEA,IAAI,aAAa,KAAK,WAAW;YACjC,QAAQ,GAAG,CAAC,yBAAyB;YAErC,qEAAqE;YACrE,IAAI,CAAC,WAAW,YAAY,SAAS,KAAK,YAAY;gBACpD,IAAI;oBACF,yEAAyE;oBACzE,MAAM,8BAA8B,WAAW,KAAK,CAAC,qBAAqB,EAAE;oBAC5E,MAAM,uBAAuB,4BAA4B,GAAG,CAAC,CAAC,OAAiB,CAAC;4BAC9E;4BACA,aAAa,CAAC,eAAe,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,CAAC;wBAChF,CAAC;oBAED,uEAAuE;oBACvE,IAAI,2BAA2B;oBAC/B,qBAAqB,OAAO,CAAC,CAAC;wBAC5B,2BAA2B,yBAAyB,OAAO,CAAC,KAAK,IAAI,EAAE,KAAK,WAAW;oBACzF;oBAEA,wDAAwD;oBACxD,IAAI,SAAS;wBACX,QAAQ,GAAG,CAAC,CAAC,wEAAwE,EAAE,yBAAyB,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC;wBACtI,MAAM,qBAAqB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,0BAA0B,MAAM;wBAClG,aAAa;wBAEb,+CAA+C;wBAC/C,qBAAqB,OAAO,CAAC,CAAC;4BAC5B,aAAa,WAAW,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;wBAC7D;oBACF,OAAO,IAAI,UAAU;wBACnB,QAAQ,GAAG,CAAC,CAAC,yEAAyE,EAAE,yBAAyB,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC;wBACvI,MAAM,qBAAqB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,0BAA0B,MAAM;wBAClG,aAAa;wBAEb,+CAA+C;wBAC/C,qBAAqB,OAAO,CAAC,CAAC;4BAC5B,aAAa,WAAW,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;wBAC7D;oBACF,OAAO,IAAI,WAAW;wBACpB,QAAQ,GAAG,CAAC,CAAC,0EAA0E,EAAE,yBAAyB,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC;wBACxI,MAAM,qBAAqB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,0BAA0B,MAAM;wBAClG,aAAa;wBAEb,+CAA+C;wBAC/C,qBAAqB,OAAO,CAAC,CAAC;4BAC5B,aAAa,WAAW,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;wBAC7D;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,UAAU,UAAU,WAAW,WAAW,UAAU,CAAC,CAAC,EAAE;gBACzG;YACF;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,qCAAqC;YACrC,MAAM,eAAe;YACrB,IAAI,aAAa,oBAAoB,KAAK,WAAW;gBACnD,8BAA8B,aAAa,oBAAoB;YACjE;YACA,IAAI,aAAa,cAAc,IAAI,MAAM,OAAO,CAAC,aAAa,cAAc,GAAG;gBAC7E,yBAAyB,aAAa,cAAc;YACtD;YAEA,IAAI,KAAK,iBAAiB,IAAI,MAAM,OAAO,CAAC,KAAK,iBAAiB,GAAG;gBACnE,QAAQ,GAAG,CAAC,2BAA2B,KAAK,iBAAiB;gBAE7D,sFAAsF;gBACtF,IAAI,WAAW,YAAY,WAAW;oBACpC,+DAA+D;oBAC/D,2DAA2D;oBAC3D,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,UAAU,UAAU,WAAW,WAAW,UAAU,KAAK,CAAC;gBAE/G,yEAAyE;gBACzE,4DAA4D;gBAC5D,4CAA4C;gBAC5C,wBAAwB;gBACxB,qFAAqF;gBACrF,MAAM;gBACN,yBAAyB;gBACzB,uFAAuF;gBACvF,MAAM;gBACN,IAAI;gBACN;gBAEA,KAAK,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAA2D;oBACzF,MAAM,WAAW,KAAK,QAAQ;oBAC9B,MAAM,MAAM,KAAK,GAAG;oBACpB,MAAM,UAAU,KAAK,OAAO,IAAI;oBAChC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU;oBAChD,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK;oBACtC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,SAAS;gBAChD;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,qCAAqC;YACrC,IAAI,KAAK,iBAAiB,IAAI,MAAM,OAAO,CAAC,KAAK,iBAAiB,GAAG;gBACnE,QAAQ,GAAG,CAAC,2BAA2B,KAAK,iBAAiB;gBAE7D,sFAAsF;gBACtF,IAAI,WAAW,YAAY,WAAW;oBACpC,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,UAAU,UAAU,WAAW,WAAW,UAAU,KAAK,CAAC;gBACjH;gBAEA,KAAK,iBAAiB,CAAC,OAAO,CAAC,CAAC,UAAkB;oBAChD,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU;gBAC1D;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,eAAe,aAAa,eAAe,MAAM;gBACnD,QAAQ,IAAI,CAAC;gBACb,aAAa,WAAW,eAAe;gBACvC;YACF;YAEA,+DAA+D;YAC/D,MAAM,iBAAiB;gBACrB,aAAa;gBACb,mBAAmB,KAAK,iBAAiB,IAAI,EAAE;gBAC/C,mBAAmB,KAAK,iBAAiB,IAAI,EAAE;YACjD;YAEA,8DAA8D;YAC9D,QAAQ,GAAG,CAAC,yCAAyC,eAAe,iBAAiB;YACrF,QAAQ,GAAG,CAAC,gDAAgD;YAC5D,aAAa,WAAW,eAAe;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,2EAA2E;YAC3E,IAAI,eAAe;YAEnB,IAAI,iBAAiB,OAAO;gBAC1B,MAAM,YAAY,MAAM,OAAO;gBAE/B,mDAAmD;gBACnD,IAAI,UAAU,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;oBAC7F,eAAe;oBACf,QAAQ,GAAG,CAAC,iCAAiC;gBAC/C;YACF;YAEA,aAAa,WAAW,eAAe;QACzC,SAAU;YACR,aAAa;YACb,kFAAkF;YAClF,2BAA2B;QAC7B;IACF;IAEA,wEAAwE;IACxE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QA6DnC;IACF,GAAG;QAAC;QAAsB;QAA2B;QAAiB;KAAiB;IAEvF,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAcnC;IACF,GAAG,EAAE;IAEL,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iEAAiE;QACjE,iBAAiB,EAAE;QACnB,gBAAgB,EAAE;QAClB,uBAAuB;QACvB,QAAQ,GAAG,CAAC,kDAAkD;IAChE,GAAG;QAAC;KAAU;IAEd,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAwE;;QAExE;QAEA,uCAA6D;;QAE7D;QAEA,2EAA2E;QAC3E,OAAO;YACL,uCAAkD;;YAGlD;YAEA,mBAAmB;YACnB,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;gBACrC,iBAAiB,OAAO,GAAG;YAC7B;QACF;IACF,GAAG;QAAC;QAAkC;QAAuB;KAAY;IAEzE,mGAAmG;IACnG,4CAA4C;IAC5C,oDAAoD;IACpD,2FAA2F;IAC3F,iEAAiE;IACjE,oEAAoE;IACpE,wEAAwE;IACxE,oDAAoD;IACpD,yDAAyD;IACzD,sDAAsD;IACtD,wBAAwB;IACxB,KAAK;IAEL,yCAAyC;IACzC,8CAA8C;IAC9C,QAAQ,GAAG,CAAC,gDAAgD;IAE5D,MAAM,kBAAkB;QACtB,kDAAkD;QAClD,IAAI,YAAY,cAAc,gBAAgB;QAE9C,IAAI,qBAAqB,SAAS;YAChC,YAAY;QACd,OAAO,IAAI,qBAAqB,UAAU;YACxC,YAAY;QACd,OAAO,IAAI,qBAAqB,WAAW;YACzC,YAAY;QACd;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG;gBACjC,MAAM,WAAW,YAAa,MAAM,GAAI,6CAA6C;gBACrF,MAAM,QAAQ,IAAI;gBAClB,MAAM,WAAW,WAAY,MAAO,IAAI,MAAQ,GAAG,6BAA6B;gBAChF,MAAM,SAAS,WACZ,IAAI,MAAM,IAAI,KAAM,IAAI,IAAK,IAAK,IAAI,MACtC,MAAM,IAAI,IAAI;gBAEjB,qBACE,8OAAC;oBAEC,WAAW,CAAC,IAAI,EAAE,UAAU,4BAA4B,CAAC;oBACzD,OAAO;wBACL,QAAQ,GAAG,OAAO,EAAE,CAAC;wBACrB,eAAe,WAAW,mBAAmB;wBAC7C,mBAAmB,GAAG,SAAS,CAAC,CAAC;wBACjC,yBAAyB;wBACzB,oBAAoB;wBACpB,gBAAgB,GAAG,MAAM,CAAC,CAAC;wBAC3B,SAAS,WAAW,IAAI;oBAC1B;mBAVK,CAAC,KAAK,EAAE,GAAG;;;;;YAatB;;;;;;IAGN;IAEA,qBACE,8OAAC;kDAAc;;;;;;YAaZ,6BACC,8OAAC;0DAAe,CAAC;UACf,EAAE,qBAAqB,UACnB,6EACA,qBAAqB,WACnB,0EACA,qBAAqB,YACnB,6EACA,mEAAmE;;kCAC3E,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;0CACZ;;;;;;0CAEH,8OAAC;;;kDACC,8OAAC;kFAAgB,CAAC,oBAAoB,EACpC,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,gBACR;;4CACC,qBAAqB,UAClB,+BACA,qBAAqB,WACnB,gCACA,qBAAqB,YACnB,mCACA;4CAA4B;0DAAC,8OAAC;;0DAAQ;;;;;;;;;;;;oCAE/C,YAAY,mBACX,8OAAC;kFAAe,CAAC,eAAe,EAC9B,qBAAqB,UACjB,uBACA,qBAAqB,WACnB,sBACA,qBAAqB,YACnB,uBACA,mBACR;kDACC,qBAAqB,UAClB,CAAC,OAAO,EAAE,UAAU,+BAA+B,CAAC,GACpD,qBAAqB,WACnB,CAAC,WAAW,EAAE,UAAU,2BAA2B,CAAC,GACpD,qBAAqB,YACnB,CAAC,YAAY,EAAE,UAAU,yBAAyB,CAAC,GACnD,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE,cAAc,IAAI,SAAS,QAAQ,OAAO,CAAC;;;;;;;;;;;;;;;;;;kCAKpF,8OAAC;wBACC,SAAS;4BACP;4BACA,2CAA2C;4BAC3C,eAAe;4BACf;4BACA,YAAY;wBACd;kEACW,CAAC;cACV,EAAE,qBAAqB,UACnB,iEACA,qBAAqB,WACnB,8DACA,qBAAqB,YACnB,iEACA,uDAAuD;;0CAEjE,8OAAC,8IAAA,CAAA,SAAM;gCAAC,WACN,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA;;;;;;0CAEV,8OAAC;;0CACC,qBAAqB,UACjB,kBACA,qBAAqB,WACnB,uBACA,qBAAqB,YACnB,uBACA;;;;;;;;;;;;;;;;;;YAMf,yCACC,8OAAC;0DAAc;;kCAEb,8OAAC;wBAAyB,KAAK;kEAAhB;kCAqBZ,qBAAqB,CAAC,gCACrB,8OAAC;sEAAc;sCACZ,gBAAgB,MAAM,GAAG,IACxB,gBAAgB,GAAG,CAAC,CAAC,sBACnB,8OAAC;oCAEC,SAAS;wCACP,iBAAiB;wCACjB,qBAAqB;wCACrB,yBAAyB;wCACzB,aAAa,OAAO,CAAC,sBAAsB;wCAC3C,aAAa,OAAO,CAAC,oBAAoB;wCACzC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO;oCACxC;8EACW,CAAC;wBACV,EAAE,kBAAkB,QAAQ,oEAAoE,oCAAoC;;sDAEtI,8OAAC;;sDAAM;;;;;;wCACN,kBAAkB,uBACjB,8OAAC;4CAAwB,MAAK;4CAAe,SAAQ;sFAAtC;sDACb,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;;;mCAfxJ;;;;0DAqBT,8OAAC;0EAAc;0CAAqD;;;;;;;;;;;;;;;;kCAS5E,8OAAC;kEAAc;;0CACb,8OAAC;;0CAAK;;;;;;0CACN,8OAAC;0EAAe;0CACb,iBAAiB;;;;;;;;;;;;;;;;;;YAOzB,uCACC,8OAAC;0DAAc;0BACb,cAAA,8OAAC;8DAAc;;sCACb,8OAAC;4BAAwB,MAAK;4BAAe,SAAQ;sEAAtC;sCACb,cAAA,8OAAC;gCAAK,UAAS;gCAAU,GAAE;gCAAwI,UAAS;;;;;;;;;;;;sCAE9K,8OAAC;sEAAe;;gCAAsB;gCAC5B;gCAAc;;;;;;;;;;;;;;;;;;0BAc9B,8OAAC;gBACC,UAAU;0DACA;;oBAGT,+BACC,8OAAC;kEAAc;;0CACb,8OAAC;gCAAI,OAAM;gCAAoE,SAAQ;gCAAY,MAAK;0EAAtD;0CAChD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoH,UAAS;;;;;;;;;;;;4BAEzJ;;;;;;;kCAKL,8OAAC;kEAAc;kCAGZ,eAAe,2BACd,8OAAC;sEAAc;;gCACZ,sBACC,mCAAmC;8CACnC,8OAAC;8EAAc;;sDACb,8OAAC;4CACC,KAAK;4CAEL,OAAO;4CACP,UAAU;4CACV,WAAW,CAAC;gDACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;oDACpC,EAAE,cAAc;oDAChB,gEAAgE;oDAChE,IAAI,iBAAiB,IAAI,MAAM,CAAC,WAAW;wDACzC,6DAA6D;wDAC7D,MAAM,iBAAiB;4DACrB,gBAAgB,KAAO;4DACvB,iBAAiB,KAAO;4DACxB,aAAa,EAAE,WAAW;4DAC1B,QAAQ,EAAE,MAAM;4DAChB,eAAe,EAAE,aAAa;4DAC9B,SAAS;4DACT,YAAY;4DACZ,kBAAkB;4DAClB,YAAY;4DACZ,WAAW;4DACX,WAAW,KAAK,GAAG;4DACnB,MAAM;4DACN,oBAAoB,IAAM;4DAC1B,sBAAsB,IAAM;4DAC5B,SAAS,KAAO;wDAClB;wDACA,kBAAkB;oDACpB;gDACF;4CACF;4CACA,aAAY;sFA9BF;;;;;;sDAgCZ,8OAAC;4CACC,SAAS;4CAQT,OAAM;sFAPK,CAAC;sBACV,EAAE,qBAAqB,UACnB,sDACA,qBAAqB,WACnB,mDACA,+CACJ;sDAGJ,cAAA,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;2CAIvB,2BAA2B;8CAC3B,8OAAC;oCACC,KAAK;8EACK;8CAET,cAAc,WAAW,IAAI,OAAO,KACnC,uCAAuC;kDACvC,8OAAC;kFAAc;;0DACb,8OAAC;0FAAY;;oDACV,aAAa,aAAa;oDAC1B,0BACC,8OAAC;wDACC,OAAO;4DACL,eAAe;4DACf,mBAAmB;4DACnB,yBAAyB;4DACzB,oBAAoB;wDACtB;kGANc;;;;;;;;;;;;4CAYnB,CAAC,eAAe,cAAc,WAAW,IAAI,OAAO,oBACnD,8OAAC;0FAAc;;kEACb,8OAAC;wDACC,SAAS;4DACP,cAAc;4DACd,aAAa;4DACb,aAAa;4DACb;4DACA,aAAa;4DACb,eAAe,EAAE;wDACnB;wDAQA,OAAM;kGAPK,CAAC;8BACV,EAAE,qBAAqB,UACnB,6CACA,qBAAqB,WACnB,6CACA,4CACJ;kEAGJ,cAAA,8OAAC;4DAAI,OAAM;4DAAiD,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sGAAjD;sEAChD,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDACC,SAAS;wDAQT,OAAM;kGAPK,CAAC;8BACV,EAAE,qBAAqB,UACnB,sDACA,qBAAqB,WACnB,mDACA,+CACJ;kEAGJ,cAAA,8OAAC,8IAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;6DAMlC,8OAAC;kFAAc;;0DACb,8OAAC;;0DACE,qBAAqB,UAClB,yCACA,qBAAqB,WACnB,+CACA,qBAAqB,YACnB,6CACA,CAAC,aAAa,EAAE,iBAAiB,aAAa,CAAC;;;;;;0DAEzD,8OAAC;0FAAc;0DACZ;oDAAC;oDAAG;oDAAG;iDAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;wDAWC,OAAO;4DACL,eAAe;4DACf,mBAAmB;4DACnB,yBAAyB;4DACzB,oBAAoB;4DACpB,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;wDAC/B;kGAfW,CAAC,qBAAqB,EAC/B,qBAAqB,UACjB,kBACA,qBAAqB,WACnB,iBACA,qBAAqB,YACnB,kBACA,cACR;uDATG,CAAC,IAAI,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;gCA0B3B,cAAc,WAAW,IAAI,OAAO,oBACpC,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDACZ,oCACC,8OAAC;0FAAe;;kEACd,8OAAC,8IAAA,CAAA,iBAAc;;;;;oDACd,qBAAqB,UAClB,kBACA,qBAAqB,WACnB,gBACA,qBAAqB,YACnB,mBACA;;;;;;uDAER,yBACF,8OAAC;0FAAe;;kEACd,8OAAC,8IAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDACrB,qBAAqB,UAClB,YACA,qBAAqB,WACnB,aACA,qBAAqB,YACnB,WACA;;;;;;qEAGV,8OAAC;0FAAe;;kEACd,8OAAC,8IAAA,CAAA,aAAU;;;;;oDACV,qBAAqB,UAClB,uBACA,qBAAqB,WACnB,oBACA,qBAAqB,YACnB,qBACA;;;;;;;;;;;;sDAId,8OAAC;sFAAe;sDACb,qBAAqB,UAClB,GAAG,UAAU,QAAQ,CAAC,GACtB,qBAAqB,WACnB,GAAG,UAAU,MAAM,CAAC,GACpB,qBAAqB,YACnB,GAAG,UAAU,MAAM,CAAC,GACpB,GAAG,UAAU,MAAM,CAAC;;;;;;;;;;;;;;;;;iDAMpC,8OAAC;sEAAc;;gCAEZ,uBAAuB,CAAC,cAAc,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,CAAC,mBAC1E,8OAAC;8EAAc;8CACb,cAAA,8OAAC;kFAAc;;4CAEZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC;8FAEW;;sEAEV,8OAAC;sGAAe,CAAC,cAAc,EAC7B,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;sEACC,YAAY,KAAK,IAAI;;;;;;sEAExB,8OAAC;sGAAe;sEACb,KAAK,IAAI;;;;;;sEAEZ,8OAAC;sGAAe;;gEACb,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEAExC,8OAAC;4DACC,SAAS,IAAM,mBAAmB;4DAElC,OAAO,uBAAuB,UAAU;sGAD9B;sEAGV,cAAA,8OAAC,8IAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;mDAzBZ,CAAC,KAAK,EAAE,OAAO;;;;;4CA+BvB,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;8FAEW;;sEAEV,8OAAC;sGAAe,CAAC,cAAc,EAC7B,QAAQ,IAAI,KAAK,YAAY,iBAC7B,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;sEACC,WAAW,QAAQ,IAAI;;;;;;sEAE1B,8OAAC;sGAAe;sEACb,QAAQ,IAAI,KAAK,YACd,uBAAuB,YAAY,GACnC,uBAAuB,WAAW;;;;;;sEAExC,8OAAC;sGAAe;sEACb,QAAQ,GAAG,CAAC,OAAO,CAAC,gBAAgB;;;;;;sEAEvC,8OAAC;4DACC,SAAS,IAAM,kBAAkB;4DAEjC,OAAO,uBAAuB,SAAS;sGAD7B;sEAGV,cAAA,8OAAC,8IAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;mDA5BZ,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;;;;;;;8CAoC7B,8OAAC;8EAAc;;sDACb,8OAAC;4CAED,aACE,qBAAqB,UACjB,gDACA,qBAAqB,WACnB,2DACA,qBAAqB,YACnB,4CACA;4CAEV,OAAO;4CACP,UAAU,CAAC;gDACT,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC3B,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC3B,uDAAuD;gDACvD,IAAI,eAAe;oDACjB,iBAAiB;gDACnB;4CACF;4CACA,WAAW,CAAC;gDACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;oDACpC,EAAE,cAAc;oDAChB,gEAAgE;oDAChE,IAAI,UAAU,IAAI,MAAM,CAAC,WAAW;wDAClC,6DAA6D;wDACzD,MAAM,iBAAiB;4DACrB,gBAAgB,KAAO;4DACvB,iBAAiB,KAAO;4DACxB,aAAa,EAAE,WAAW;4DAC1B,QAAQ,EAAE,MAAM;4DAChB,eAAe,EAAE,aAAa;4DAC9B,SAAS;4DACT,YAAY;4DACZ,kBAAkB;4DAClB,YAAY;4DACZ,WAAW;4DACX,WAAW,KAAK,GAAG;4DACnB,MAAM;4DACN,oBAAoB,IAAM;4DAC1B,sBAAsB,IAAM;4DAC5B,SAAS,KAAO;wDAClB;wDACJ,kBAAkB;oDACpB;gDACF;4CACF;4CACA,UAAU;4CACV,SAAS;gDACP,4DAA4D;gDAC5D,iFAAiF;gDACjF,kCAAkC;gDAClC,mBAAmB;4CACrB;sFApDY;;;;;;sDAwDZ,8OAAC;sFAAc;sDACb,cAAA,8OAAC,gJAAA,CAAA,UAAe;gDACd,kBAAkB;gDAClB,UAAU;gDACV,qBAAqB;gDACrB,gBAAgB,IAAM,sBAAsB;gDAC5C,cAAc,CAAC;oDACb,QAAQ,GAAG,CAAC,mBAAmB;oDAC/B,sCAAsC;oDACtC,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;wDAC7B,wDAAwD;wDACxD,iBAAiB,CAAA;4DACf,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,UAC5B,CAAC,UAAU,IAAI,CAAC,CAAA,eACd,aAAa,IAAI,KAAK,QAAQ,IAAI,IAClC,aAAa,IAAI,KAAK,QAAQ,IAAI,IAClC,aAAa,YAAY,KAAK,QAAQ,YAAY;4DAGtD,IAAI,SAAS,MAAM,GAAG,GAAG;gEACvB,uBAAuB;gEACvB,OAAO;uEAAI;uEAAc;iEAAS;4DACpC;4DACA,OAAO;wDACT;oDACF;gDACF;gDACA,aAAa,CAAC,KAAa;oDACzB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS;oDAC5C,6BAA6B;oDAC7B,IAAI,OAAO,IAAI,IAAI,IAAI;wDACrB,yBAAyB;wDACzB,gBAAgB,CAAA;4DACd,MAAM,YAAY,KAAK,IAAI,CAAC,CAAA,cAAe,YAAY,GAAG,KAAK,IAAI,IAAI;4DACvE,IAAI,WAAW;gEACb,QAAQ,GAAG,CAAC,uBAAuB;gEACnC,OAAO;4DACT;4DACA,uBAAuB;4DACvB,OAAO;mEAAI;gEAAM;oEAAE,KAAK,IAAI,IAAI;oEAAI;gEAAK;6DAAE;wDAC7C;oDACF;gDACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQZ,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB,CAAC;kFACxB,CAAC;gBACV,EAAE,kBAAkB,0CAA0C,yBAAyB;;;gBAGvF,EAAE,qBAAqB,UACnB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,gBAAgB;kCACN,CAAC;;0DACrB,8OAAC,8IAAA,CAAA,cAAW;gDAAC,WAAW,GAAG,kBACvB,sBACA,qBAAqB,UACnB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,kBAAkB;;;;;;0DAC5B,8OAAC;;0DACC,qBAAqB,UACjB,iBACA,qBAAqB,WACnB,WACA,qBAAqB,YACnB,YACA;;;;;;0DAEV,8OAAC,8IAAA,CAAA,YAAS;gDAAC,WAAW,GAAG,kBACrB,sBACA,qBAAqB,UACnB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBAAiB;gBACzB,EAAE,CAAC,kBAAkB,kBAAkB,IAAI;;;;;;;;;;;;oCAI9C,iCACC,8OAAC;wCAUC,SAAS,IAAM,mBAAmB;wCAClC,OAAO;4CAAE,gBAAgB;4CAAa,mBAAmB;wCAAO;kFAVrD,CAAC,qEAAqE,EAC/E,qBAAqB,UACjB,qDACA,qBAAqB,WACnB,oDACA,qBAAqB,YACnB,qDACA,kDACR;kDAIF,cAAA,8OAAC;4CACC,KAAK;4CAUL,SAAS,CAAC,IAAM,EAAE,eAAe;4CACjC,OAAO;gDACL,mBAAmB;gDACnB,WAAW;4CACb;sFAbW,CAAC,gGAAgG,EAC1G,qBAAqB,UACjB,qEACA,qBAAqB,WACnB,mEACA,qBAAqB,YACnB,qEACA,gEACR;;8DAOF,8OAAC;8FAAe,CAAC,4DAA4D,EAC3E,qBAAqB,UACjB,oEACA,qBAAqB,WACnB,iEACA,qBAAqB,YACnB,oEACA,6DACR;;sEACA,8OAAC;sGAAa;;8EACZ,8OAAC;8GAAe,CAAC,iBAAiB,EAChC,qBAAqB,UACjB,kBACA,qBAAqB,WACnB,iBACA,qBAAqB,YACnB,kBACA,eACR;8EACA,cAAA,8OAAC,8IAAA,CAAA,cAAW;wEAAC,WAAW,CAAC,QAAQ,EAC/B,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;;;;;;;;;;;8EAEJ,8OAAC;+GACC,CAAA,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,eAAc;8EAErB,qBAAqB,UAClB,sCACA,qBAAqB,WACnB,qCACA,qBAAqB,YACnB,qCACA;;;;;;;;;;;;sEAGZ,8OAAC;4DACC,SAAS,IAAM,mBAAmB;4DAUlC,cAAW;sGATA,CAAC,mCAAmC,EAC7C,qBAAqB,UACjB,+EACA,qBAAqB,WACnB,2EACA,qBAAqB,YACnB,+EACA,sEACR;sEAGF,cAAA,8OAAC;gEAAI,OAAM;gEAAiD,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0GAAjD;0EAChD,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;8DAK3E,8OAAC;8FAAc;;sEACb,8OAAC;sGAAe,CAAC,oBAAoB,EACnC,qBAAqB,UACjB,sEACA,qBAAqB,WACnB,oEACA,qBAAqB,YACnB,sEACA,iEACR;sEACA,cAAA,8OAAC;0GAAa,CAAC,oBAAoB,EACjC,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;0EACC,qBAAqB,UAClB,wEACA,qBAAqB,WACnB,yDACA,qBAAqB,YACnB,uEACA;;;;;;;;;;;sEAIZ,8OAAC;sGAAc;sEACZ,uBAAuB,GAAG,CAAC,CAAC,YAAY,sBACvC,8OAAC;oEAEC,SAAS;wEACP,uBAAuB;wEACvB,mBAAmB;oEACrB;oEAUA,OAAO;wEAAE,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;wEAAE,mBAAmB;oEAAO;8GAT5D,CAAC;4BACV,EAAE,qBAAqB,UACnB,kHACA,qBAAqB,WACnB,+GACA,qBAAqB,YACnB,kHACA,0GAA0G;0DACpF,CAAC;8EAGjC,cAAA,8OAAC;kHAAc;;0FACb,8OAAC;0HAAgB,CAAC,4CAA4C,EAC5D,qBAAqB,UACjB,oEACA,qBAAqB,WACnB,iEACA,qBAAqB,YACnB,oEACA,6DACR;0FACA,cAAA,8OAAC;oFAAI,OAAM;oFAAiD,SAAQ;oFAAY,MAAK;8HAAnC;8FAChD,cAAA,8OAAC;wFAAK,UAAS;wFAAU,GAAE;wFAAyL,UAAS;;;;;;;;;;;;;;;;;0FAGjO,8OAAC;0HAAe;0FAAe;;;;;;;;;;;;mEA9B5B;;;;;;;;;;;;;;;;8DAqCb,8OAAC;8FAAe,CAAC,qCAAqC,EACpD,qBAAqB,UACjB,oEACA,qBAAqB,WACnB,iEACA,qBAAqB,YACnB,oEACA,6DACR;8DACA,cAAA,8OAAC;wDACC,SAAS,IAAM,mBAAmB;kGACvB,CAAC;wBACV,EAAE,qBAAqB,UACnB,6FACA,qBAAqB,WACnB,yFACA,qBAAqB,YACnB,6FACA,oFAAoF;kEAE7F,qBAAqB,UAClB,SACA,qBAAqB,WACnB,eACA,qBAAqB,YACnB,aACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOpB,8OAAC;0EAAc;;kDAEb,8OAAC;kFAAc;;0DAEb,8OAAC;0FAAc;0DAMb,cAAA,8OAAC;8FAAc;8DAEZ,UAAU,GAAG,CAAC,CAAC,UAAU;wDACxB,MAAM,aAAa,qBAAqB,SAAS,IAAI;wDAErD,sCAAsC;wDACtC,MAAM,gBAAgB;4DACpB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO;4DACtC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;4DACvC,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;4DACxC,OAAO;wDACT;wDAEA,MAAM,gBAAgB;4DACpB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO;4DACtC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;4DACvC,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;4DACxC,OAAO;wDACT;wDAEA,MAAM,eAAe;4DACnB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO;4DACtC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;4DACvC,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;4DACxC,OAAO;wDACT;wDAEA,MAAM,iBAAiB;4DACrB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO;4DACtC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;4DACvC,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;4DACxC,OAAO;wDACT;wDAEA,gFAAgF;wDAChF,qBACE,8OAAC;4DAEC,MAAK;4DACL,SAAS,CAAC,IAAM,qBAAqB,GAAG,SAAS,IAAI;4DACrD,UAAU,2BAA2B;4DAUrC,OAAO;gEACL,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;gEAClC,mBAAmB;4DACrB;sGAZW,CAAC;0BACV,EAAE,aACE,kBACA,4CAA4C,gBAAgB;;0BAEhE,EAAE,aAAa,wBAAwB,kCAAkC;0BACzE,EAAE,AAAC,2BAA2B,YAAa,kCAAkC,GAAG;;wBAElF,CAAC;;8EAMD,8OAAC;8GAAc;;sFACb,8OAAC,8IAAA,CAAA,UAAO;4EAAC,WAAW,GAAG,eAAe,SAAS,EAAE,aAAa,mBAAmB,IAAI;4EACnF,OAAO;gFAAE,mBAAmB;4EAAO;;;;;;sFAErC,8OAAC;sHAAe;sFAAe,SAAS,IAAI;;;;;;;;;;;;gEAI7C,4BACC,8OAAC;oEAEC,OAAO;wEACL,OAAO;wEACP,iBAAiB;wEACjB,mBAAmB;oEACrB;8GALW,CAAC,sEAAsE,EAAE,kBAAkB;;;;;;gEAUzG,4BACC,8OAAC;oEAEC,OAAO;wEACL,OAAO;wEACP,iBAAiB;wEACjB,mBAAmB;wEACnB,gBAAgB;oEAClB;8GANW,CAAC,oEAAoE,EAAE,kBAAkB;;;;;;;2DAxCnG;;;;;oDAmDX;;;;;;;;;;;0DAKJ,8OAAC;gDAAmC,KAAK;0FAA1B;;kEACb,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,oBAAoB,CAAC;wDACpC,UAAU,2BAA2B;kGAC1B,CAAC;oBACV,EAAE,qBAAqB,UACnB,mDACA,qBAAqB,WACnB,gDACA,qBAAqB,YACnB,mDACA,2CACP;;oBAED,EAAE,AAAC,2BAA2B,YAAa,kCAAkC,GAAG;kBAClF,CAAC;;0EAED,8OAAC,8IAAA,CAAA,UAAO;gEAAC,WAAW,CAAC,QAAQ,EAC3B,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;;;;;;0EACF,8OAAC;0GAAe;0EAAuB;;;;;;0EACvC,8OAAC;0GAAgB,CAAC,kCAAkC,EAAE,mBAAmB,eAAe,IAAI;0EAC1F,cAAA,8OAAC;oEAAI,OAAM;oEAAiD,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8GAAjD;8EAChD,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;oDAM1E,kCACC,8OAAC;wDACC,OAAO;4DAAE,mBAAmB;wDAAO;kGADtB;kEAGZ,UAAU,GAAG,CAAC,CAAC,UAAU;4DACxB,MAAM,aAAa,qBAAqB,SAAS,IAAI;4DAErD,sCAAsC;4DACtC,MAAM,oBAAoB;gEACxB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO,aAAa,iCAAiC;gEACpF,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO,aAAa,+BAA+B;gEACnF,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO,aAAa,iCAAiC;gEACtF,OAAO,aAAa,6BAA6B;4DACnD;4DAEA,MAAM,eAAe;gEACnB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO;gEACtC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;gEACvC,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;gEACxC,OAAO;4DACT;4DAEA,qBACE,8OAAC;gEAEC,MAAK;gEACL,SAAS,CAAC;oEACR,qBAAqB,GAAG,SAAS,IAAI;oEACrC,oBAAoB;gEACtB;gEACA,UAAU,2BAA2B;gEAIrC,OAAO;oEACL,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;gEACpC;0GALW,CAAC,2DAA2D,EAAE,oBAAoB;4BAC3F,EAAE,AAAC,2BAA2B,YAAa,kCAAkC,GAAG;0BAClF,CAAC;;kFAKD,8OAAC,8IAAA,CAAA,UAAO;wEAAC,WAAW,GAAG,eAAe,QAAQ,CAAC;;;;;;kFAC/C,8OAAC;kHAAe;kFAAe,SAAS,IAAI;;;;;;oEAC3C,4BACC,8OAAC;wEAAI,OAAM;wEAAyD,MAAK;wEAAO,SAAQ;wEAAY,QAAO;kHAAzD;kFAChD,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;+DAlBpE;;;;;wDAuBX;;;;;;;;;;;;;;;;;;kDAOR,8OAAC;kFAAc;;4CAEZ,CAAC,kBAAkB,CAAC,uCACnB,8OAAC;0FAAc;0DACZ,iDACD,8OAAC;oDACC,MAAK;oDACL,SAAS,cAAc,kBAAkB;oDAazC,OAAO,cAAc,yBAAyB,CAAC,yBAAyB,EAAE,kBAAkB;8FAZjF,CAAC;sBACV,EAAE,cACE,yGACA,qBAAqB,UACnB,0FACA,qBAAqB,WACnB,uFACA,qBAAqB,YACnB,0FACA,kFACT;0CACmB,CAAC;8DAGtB,4BACC,8OAAC;kGAAc;;0EACb,8OAAC;gEAEC,OAAO;oEACL,eAAe;oEACf,mBAAmB;oEACnB,yBAAyB;oEACzB,oBAAoB;gEACtB;0GANU;;;;;;0EAQZ,8OAAC,8IAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;gEAEC,OAAO;oEACL,eAAe;oEACf,mBAAmB;oEACnB,yBAAyB;gEAC3B;0GALU;;;;;;;;;;;6EASd,8OAAC,8IAAA,CAAA,eAAY;wDAAC,WAAW,CAAC,QAAQ,EAChC,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;;;;;;;;;;yEAIN,8OAAC;oDACC,MAAK;oDAEL,OAAM;oDACN,QAAQ;8FAFE;8DAIV,cAAA,8OAAC,8IAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAO9B,8OAAC;gDACC,MAAK;gDAUL,UAAU;gDACV,OACE,YACI,qBAAqB,UACnB,oBACA,qBAAqB,WACnB,iBACA,qBAAqB,YACnB,wBACA,eACN,qBAAqB,UACnB,qBACA,qBAAqB,WACnB,oBACA,qBAAqB,YACnB,yBACA;0FAzBD,CAAC;kBACV,EAAE,qBAAqB,UACnB,yHACA,qBAAqB,WACnB,oHACA,qBAAqB,YACnB,yHACA,6GAA6G;kBACrH,EAAE,YAAY,4CAA4C,IAAI;0DAoB/D,YACC,6BAA6B;gDAC7B,qBAAqB,wBACnB,8OAAC;8FAAe;;wDAAgD;sEAC9C,8OAAC,8IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;2DAErC,qBAAqB,yBACvB,8OAAC;8FAAe;;wDAAgD;sEACjD,8OAAC,8IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;2DAElC,qBAAqB,0BACvB,8OAAC;8FAAe;;wDAAgD;sEAC1C,8OAAC,8IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;yEAG3C,8OAAC;8FAAe;;wDAAgD;sEACnD,8OAAC,8IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;2DAIpC,eAAe;gDACf,qBAAqB,wBACnB,8OAAC;8FAAe;;wDAAgD;sEACtD,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;2DAElB,qBAAqB,yBACvB,8OAAC;8FAAe;;wDAAgD;sEACvD,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;2DAEjB,qBAAqB,0BACvB,8OAAC;8FAAe;;wDAAgD;sEACtD,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;yEAGpB,8OAAC;8FAAe;;wDAAgD;sEACzD,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YA+G9B,oCACC,8OAAC,sIAAA,CAAA,UAAc;gBAAC,SAAS,IAAM,sBAAsB;;;;;;;;;;;;AAI7D;AAEA,QAAQ,WAAW,GAAG;uCAEP"}}, {"offset": {"line": 6406, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6412, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/app/%28with-layout%29/new-chat/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport logo from \"@/public/images/favicon.ico\";\r\nimport Image from \"next/image\";\r\n\r\nimport ChatBox from \"@/components/chatComponents/ChatBox\"\r\n\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { ApiService } from \"@/components/chatComponents/services/ApiService\";\r\n\r\nfunction NewChat() {\r\n  const router = useRouter();\r\n\r\n  // State for FAISS index selector and fetched indexes\r\n  const [faissIndexes, setFaissIndexes] = useState<string[]>([]);\r\n  const [selectedIndex, setSelectedIndex] = useState<string>(() => {\r\n    // Initialize from localStorage if available\r\n    if (typeof window !== 'undefined') {\r\n      const savedIndex = localStorage.getItem('faiss_index_name') || localStorage.getItem('selectedFaissIndex');\r\n      return savedIndex || '';\r\n    }\r\n    return '';\r\n  });\r\n  const [showConfirmation, setShowConfirmation] = useState<boolean>(false);\r\n  const [isLoading, setIsLoading] = useState<boolean>(true);\r\n\r\n\r\n\r\n  // Fetch FAISS indexes on mount\r\n  useEffect(() => {\r\n    const fetchFilteredFaissData = async () => {\r\n      // Only run on client side to prevent hydration errors\r\n      if (typeof window === 'undefined') return;\r\n\r\n      setIsLoading(true);\r\n\r\n      try {\r\n        console.log(\"Fetching available FAISS indexes from PINE collection...\");\r\n\r\n        // Use the centralized ApiService method\r\n        const availableIndexes = await ApiService.fetchUserIndexes();\r\n        console.log(\"Available indexes for user:\", availableIndexes);\r\n\r\n        // Update the indexes state\r\n        setFaissIndexes(availableIndexes);\r\n\r\n        // Initialize selectedIndex from localStorage, fallback to first available or 'default'\r\n        const savedIndex = localStorage.getItem('selectedFaissIndex') || localStorage.getItem('faiss_index_name');\r\n        let indexToSelect = 'default';\r\n\r\n        if (savedIndex && availableIndexes.includes(savedIndex)) {\r\n          indexToSelect = savedIndex;\r\n        } else if (availableIndexes.includes('default')) {\r\n          indexToSelect = 'default';\r\n        } else if (availableIndexes.length > 0) {\r\n          indexToSelect = availableIndexes[0];\r\n        }\r\n\r\n        setSelectedIndex(indexToSelect);\r\n        localStorage.setItem('selectedFaissIndex', indexToSelect);\r\n        localStorage.setItem('faiss_index_name', indexToSelect);\r\n\r\n        console.log(`Selected index: ${indexToSelect}`);\r\n      } catch (error) {\r\n        console.error(\"Error fetching indexes:\", error);\r\n        // Fallback to default configuration\r\n        setFaissIndexes(['default']);\r\n        setSelectedIndex('default');\r\n        localStorage.setItem('selectedFaissIndex', 'default');\r\n        localStorage.setItem('faiss_index_name', 'default');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n\r\n\r\n    fetchFilteredFaissData();\r\n  }, []);\r\n\r\n  // Hide confirmation message after 5 seconds\r\n  useEffect(() => {\r\n    let timer: NodeJS.Timeout;\r\n    if (showConfirmation) {\r\n      timer = setTimeout(() => {\r\n        setShowConfirmation(false);\r\n      }, 5000);\r\n    }\r\n    return () => {\r\n      if (timer) clearTimeout(timer);\r\n    };\r\n  }, [showConfirmation]);\r\n\r\n  // Handle FAISS index selection\r\n  const handleIndexSelect = (index: string) => {\r\n    setSelectedIndex(index);\r\n    setShowConfirmation(true);\r\n\r\n    // Store the selected index in localStorage for use in other components\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('selectedFaissIndex', index);\r\n      localStorage.setItem('faiss_index_name', index);\r\n    }\r\n    console.log(`Selected FAISS index: ${index}`);\r\n  };\r\n\r\n  // Function to start a new chat with the selected index\r\n  const startNewChat = () => {\r\n    const chatId = uuidv4();\r\n    const currentChatId = chatId;\r\n\r\n    // Store the selected index in localStorage directly\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('selectedFaissIndex', selectedIndex);\r\n      localStorage.setItem('faiss_index_name', selectedIndex);\r\n    }\r\n\r\n    // Navigate to the chat page without creating a message\r\n    router.push(`/chat/${currentChatId}`);\r\n  };\r\n  return (\r\n    <>\r\n      <style jsx global>{`\r\n        @keyframes fadeIn {\r\n          from { opacity: 0; transform: translateY(10px); }\r\n          to { opacity: 1; transform: translateY(0); }\r\n        }\r\n\r\n        .animate-fadeIn {\r\n          animation: fadeIn 0.3s ease-out forwards;\r\n        }\r\n      `}</style>\r\n      <div className=\"w-full h-full flex items-center justify-center\">\r\n        <div className=\"w-full max-w-[1090px] mx-auto px-6 flex flex-col\">\r\n          <div\r\n            className={`flex flex-col justify-center items-center text-center pb-8 `}\r\n          >\r\n            <div className=\"flex justify-start items-center gap-3\">\r\n              <Image src={logo} alt=\"\" />\r\n              <p className=\"text-2xl font-semibold text-n700 dark:text-n30\">\r\n                Hello, I&apos;m QueryOne\r\n              </p>\r\n            </div>\r\n            <p className=\"text-n700 pt-4 dark:text-n30\">\r\n              How can I make things easier for you?\r\n            </p>\r\n          </div>\r\n\r\n          <ChatBox />\r\n\r\n          {/* FAISS Index buttons styled like the second image - Positioned first below ChatBox */}\r\n          <div className=\"w-full max-w-[800px] mx-auto mt-6 flex justify-center\">\r\n            <div className=\"bg-gray-100 dark:bg-gray-800 rounded-lg p-2 flex flex-wrap justify-center gap-2\">\r\n              {isLoading ? (\r\n                <div className=\"py-2 px-4 text-gray-500 dark:text-gray-400 flex items-center gap-2\">\r\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"></div>\r\n                  Loading FAISS indexes...\r\n                </div>\r\n              ) : faissIndexes.length > 0 ? (\r\n                faissIndexes.map((index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => handleIndexSelect(index)}\r\n                    className={`py-2 px-4 rounded-lg flex items-center gap-2 transition-colors\r\n                      ${selectedIndex === index\r\n                        ? 'bg-white dark:bg-gray-700 shadow-sm text-blue-600 dark:text-blue-400'\r\n                        : 'hover:bg-white dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'}`}\r\n                  >\r\n                    <span className=\"font-medium\">{index}</span>\r\n                  </button>\r\n                ))\r\n              ) : (\r\n                <div className=\"py-2 px-4 text-gray-500 dark:text-gray-400\">\r\n                  No FAISS indexes found. Please create an index first.\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Confirmation message */}\r\n          {showConfirmation && (\r\n            <div className=\"w-full max-w-[800px] mx-auto mt-6\">\r\n              <div className=\"bg-gray-100 dark:bg-gray-800 rounded-lg p-4 text-center animate-fadeIn\">\r\n                <div className=\"text-green-600 dark:text-green-400 font-medium mb-2\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 inline-block mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                  </svg>\r\n                  FAISS index &quot;{selectedIndex}&quot; is selected, you can now ask questions based on this!\r\n                </div>\r\n                <div className=\"flex justify-center mt-4\">\r\n                  <button\r\n                    onClick={startNewChat}\r\n                    className=\"py-2 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors flex items-center gap-2\"\r\n                  >\r\n                    <span>Start Chat</span>\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14 5l7 7m0 0l-7 7m7-7H3\" />\r\n                    </svg>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default NewChat;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAEA;AAGA;AACA;AAFA;AAPA;;;;;;;;;;AAWA,SAAS;IACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,qDAAqD;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;QACzD,4CAA4C;QAC5C,uCAAmC;;QAGnC;QACA,OAAO;IACT;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAIpD,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,sDAAsD;YACtD,wCAAmC;;QAyCrC;QAIA;IACF,GAAG,EAAE;IAEL,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QACJ,IAAI,kBAAkB;YACpB,QAAQ,WAAW;gBACjB,oBAAoB;YACtB,GAAG;QACL;QACA,OAAO;YACL,IAAI,OAAO,aAAa;QAC1B;IACF,GAAG;QAAC;KAAiB;IAErB,+BAA+B;IAC/B,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,oBAAoB;QAEpB,uEAAuE;QACvE,uCAAmC;;QAGnC;QACA,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,OAAO;IAC9C;IAEA,uDAAuD;IACvD,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;QACpB,MAAM,gBAAgB;QAEtB,oDAAoD;QACpD,uCAAmC;;QAGnC;QAEA,uDAAuD;QACvD,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,eAAe;IACtC;IACA,qBACE;;;;;;0BAWE,8OAAC;0DAAc;0BACb,cAAA,8OAAC;8DAAc;;sCACb,8OAAC;sEACY,CAAC,2DAA2D,CAAC;;8CAExE,8OAAC;8EAAc;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAK,0RAAA,CAAA,UAAI;4CAAE,KAAI;;;;;;sDACtB,8OAAC;sFAAY;sDAAiD;;;;;;;;;;;;8CAIhE,8OAAC;8EAAY;8CAA+B;;;;;;;;;;;;sCAK9C,8OAAC,wIAAA,CAAA,UAAO;;;;;sCAGR,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;0CACZ,0BACC,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;;;;;wCAAqE;;;;;;2CAGpF,aAAa,MAAM,GAAG,IACxB,aAAa,GAAG,CAAC,CAAC,sBAChB,8OAAC;wCAEC,SAAS,IAAM,kBAAkB;kFACtB,CAAC;sBACV,EAAE,kBAAkB,QAChB,yEACA,0EAA0E;kDAEhF,cAAA,8OAAC;sFAAe;sDAAe;;;;;;uCAP1B;;;;8DAWT,8OAAC;8EAAc;8CAA6C;;;;;;;;;;;;;;;;wBAQjE,kCACC,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;;0DACb,8OAAC;gDAAI,OAAM;gDAAmE,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0FAAnE;0DAChD,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;4CACjE;4CACa;4CAAc;;;;;;;kDAEnC,8OAAC;kFAAc;kDACb,cAAA,8OAAC;4CACC,SAAS;sFACC;;8DAEV,8OAAC;;8DAAK;;;;;;8DACN,8OAAC;oDAAI,OAAM;oDAAiD,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8FAAjD;8DAChD,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa3F;uCAEe"}}, {"offset": {"line": 6715, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}