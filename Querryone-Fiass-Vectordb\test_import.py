#!/usr/bin/env python3
"""
Test script to check if language-aware processor can be imported and used correctly.
"""

import sys
import os

# Add the python-fiass-backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

def test_imports():
    """Test importing the language-aware processor"""
    
    print("🧪 TESTING IMPORTS")
    print("=" * 50)
    
    try:
        print("📦 Importing language-aware processor...")
        from services.language_aware_processor import language_aware_processor
        print("✅ Successfully imported language_aware_processor")
        
        # Test basic functionality
        print("\n🔍 Testing language detection...")
        
        # Test Tamil query
        tamil_query = "பங்குச் சந்தை செய்தி என்ன?"
        query_lang_info = language_aware_processor.detect_query_language(tamil_query)
        print(f"Tamil Query: {tamil_query}")
        print(f"Detected Language: {query_lang_info.get('language', 'Unknown')}")
        print(f"Language Code: {query_lang_info.get('language_code', 'Unknown')}")
        print(f"Confidence: {query_lang_info.get('confidence', 0):.3f}")
        
        # Test English query
        english_query = "What is the stock market news?"
        query_lang_info_en = language_aware_processor.detect_query_language(english_query)
        print(f"\nEnglish Query: {english_query}")
        print(f"Detected Language: {query_lang_info_en.get('language', 'Unknown')}")
        print(f"Language Code: {query_lang_info_en.get('language_code', 'Unknown')}")
        print(f"Confidence: {query_lang_info_en.get('confidence', 0):.3f}")
        
        # Test direct processing logic
        print("\n🎯 Testing direct processing logic...")
        
        # Same language test
        should_direct_tamil = language_aware_processor.should_use_direct_processing("Tamil", "Tamil")
        print(f"Tamil -> Tamil: Direct Processing = {should_direct_tamil}")
        
        should_direct_english = language_aware_processor.should_use_direct_processing("English", "English")
        print(f"English -> English: Direct Processing = {should_direct_english}")
        
        # Different language test
        should_direct_mixed = language_aware_processor.should_use_direct_processing("Tamil", "English")
        print(f"Tamil -> English: Direct Processing = {should_direct_mixed}")
        
        print("\n✅ All import tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n🎉 SUCCESS: Language-aware processor is working correctly!")
    else:
        print("\n💥 FAILURE: There are issues with the language-aware processor!")
