#!/usr/bin/env python3
"""
Test script to verify FAISS index sampling for language detection.
"""

import sys
import os

# Add the python-fiass-backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-fiass-backend'))

def test_faiss_sampling():
    """Test FAISS index sampling for language detection"""
    
    print("🧪 TESTING FAISS INDEX SAMPLING")
    print("=" * 50)
    
    try:
        print("📦 Importing language-aware processor...")
        from services.language_aware_processor import language_aware_processor
        print("✅ Successfully imported language_aware_processor")
        
        # Test FAISS sampling directly
        print("\n🔍 Testing FAISS index sampling...")
        
        index_name = "default"
        samples = language_aware_processor._sample_texts_from_faiss_index(index_name)
        print(f"📊 Sampled {len(samples)} texts from index '{index_name}'")
        
        if samples:
            print("\n📝 Sample texts (first 3):")
            for i, sample in enumerate(samples[:3]):
                print(f"   {i+1}. {sample[:100]}...")
        
        # Test CSV language detection with index name
        print(f"\n🌐 Testing CSV language detection for index '{index_name}'...")
        csv_lang_info = language_aware_processor.detect_csv_language(index_name=index_name)
        print(f"Detected Language: {csv_lang_info.get('language', 'Unknown')}")
        print(f"Language Code: {csv_lang_info.get('language_code', 'Unknown')}")
        print(f"Confidence: {csv_lang_info.get('confidence', 0):.3f}")
        print(f"Method: {csv_lang_info.get('method', 'Unknown')}")
        print(f"Sample Size: {csv_lang_info.get('sample_size', 0)}")
        
        # Test full language-aware processing
        print(f"\n🎯 Testing full language-aware processing...")
        
        tamil_query = "பங்குச் சந்தை செய்தி என்ன?"
        
        def mock_search_function(query, **kwargs):
            return [
                {
                    'metadata': {
                        'chunk_text': f'Mock result for query: {query}',
                        'score': 0.95
                    }
                }
            ]
        
        result = language_aware_processor.process_query_with_language_awareness(
            query=tamil_query,
            index_name=index_name,
            search_function=mock_search_function
        )
        
        print(f"Query Language: {result.get('query_language_info', {}).get('language', 'Unknown')}")
        print(f"CSV Language: {result.get('csv_language_info', {}).get('language', 'Unknown')}")
        print(f"Processing Strategy: {result.get('processing_strategy', 'unknown')}")
        print(f"Direct Processing Used: {result.get('use_direct_processing', False)}")
        print(f"Success: {result.get('success', False)}")
        
        if result.get('use_direct_processing'):
            print("🎉 SUCCESS: Direct processing was used!")
        else:
            print("⚠️ WARNING: Translation-based processing was used")
            
        print("\n✅ All tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_faiss_sampling()
    if success:
        print("\n🎉 SUCCESS: FAISS sampling is working correctly!")
    else:
        print("\n💥 FAILURE: There are issues with FAISS sampling!")
